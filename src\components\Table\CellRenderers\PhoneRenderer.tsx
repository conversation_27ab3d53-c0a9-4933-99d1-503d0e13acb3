import { Anchor } from "@mantine/core";
import {
  type MRT_Column,
  type MRT_Row,
  type MRT_TableInstance,
  type MRT_Cell,
  type MRT_RowData,
} from "mantine-react-table";
import { type ReactNode, type RefObject } from "react";

export function PhoneRenderer(props: {
  cell: MRT_Cell<MRT_RowData, unknown>;
  column: MRT_Column<MRT_RowData, unknown>;
  renderedCellValue: ReactNode | number | string;
  renderedColumnIndex?: number;
  renderedRowIndex?: number;
  row: MRT_Row<MRT_RowData>;
  rowRef?: RefObject<HTMLTableRowElement>;
  table: MRT_TableInstance<MRT_RowData>;
}) {
  if (!props.cell.getValue<string>()) {
    return <></>;
  }

  const phone = "tel:" + props.cell.getValue<string>();
  return (
    <Anchor fz={12} href={phone} underline="hover" style={{ color: "primary" }}>
      {props.cell.getValue<string>()}
    </Anchor>
  );
}
