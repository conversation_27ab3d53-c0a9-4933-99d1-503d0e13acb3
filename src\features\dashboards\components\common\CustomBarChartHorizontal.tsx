import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  ResponsiveContainer,
  Cell,
  type TooltipProps,
} from "recharts";
import { type Statistic } from "../../utils/types";
import {
  type NameType,
  type ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { Box, Flex, Paper, Text } from "@mantine/core";
import { useCallback, useState } from "react";
import { IconArrowsMaximize, IconArrowsMinimize } from "@tabler/icons-react";

interface CustomBarChartVerticalProps {
  data: Statistic[] | undefined;
  title: string;
  marginTop?: number;
  height?: number;
  width?: string;
  percentage?: boolean;
}

interface CustomChartTooltipProps extends TooltipProps<ValueType, NameType> {
  percentage?: boolean;
}

export default function CustomBarChartHorizontal({
  data,
  title,
  marginTop = 10,
  height = 270,
  width = "100%",
  percentage = false,
}: CustomBarChartVerticalProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const CustomTooltip = ({
    active,
    payload,
    label,
    percentage,
  }: CustomChartTooltipProps) => {
    if (active) {
      return (
        <Paper px="md" py="sm" withBorder shadow="md" radius="md">
          {payload!.map((item) => (
            <Text key={label as string} fz="h6" fw={700}>
              {label}: {item.value} {percentage && "%"}
            </Text>
          ))}
        </Paper>
      );
    }

    return null;
  };

  const xKey = "display";
  const yKey = percentage ? "percentage" : "value";

  const expandedStyle = {
    position: "fixed" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: "white",
    padding: "20px",
    overflow: "hidden" as const,
  };

  const normalStyle = {
    border: "2px solid #e0e0e0",
    borderRadius: 4,
    height: height + 20,
    width,
  };

  return (
    <Box
      style={isExpanded == true ? expandedStyle : normalStyle}
      mt={isExpanded ? 0 : marginTop}
    >
      <Flex dir="row">
        <Text fw={600} ml={4} flex={3} ta={"left"} size="sm">
          {title}
        </Text>
        <Box
          ta="right"
          mr={4}
          style={{ cursor: "pointer" }}
          onClick={toggleExpand}
        >
          {isExpanded ? (
            <IconArrowsMinimize size={14} />
          ) : (
            <IconArrowsMaximize size={14} />
          )}
        </Box>
      </Flex>

      <ResponsiveContainer width={width} height={isExpanded ? "100%" : height}>
        <BarChart
          margin={{ left: -30, bottom: 0 }}
          layout="horizontal"
          height={height}
          data={data}
          barGap={"1%"}
        >
          <CartesianGrid vertical={false} strokeDasharray="1 1" />
          <XAxis
            orientation="bottom"
            minTickGap={1}
            axisLine={false}
            dataKey={xKey}
            tickLine={false}
            fontSize={10}
            interval={0}
            fontWeight={600}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            dataKey={yKey}
            fontSize={8}
            fontWeight={400}
          />
          <Tooltip
            cursor={{ fill: "transparent" }}
            content={<CustomTooltip percentage={percentage} />}
          />
          <Bar dataKey={yKey}>
            {data?.map((_entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={"#228be6"}
                strokeWidth={2}
              ></Cell>
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
}
