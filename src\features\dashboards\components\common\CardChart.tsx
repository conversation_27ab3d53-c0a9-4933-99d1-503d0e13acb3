import { Box, Center, Text, Title } from "@mantine/core";

interface CardChartProps {
  value: string;
  title: string;
  marginTop?: number;
  height?: string;
  width?: string;
  percentage?: boolean;
  predicate?: (value: number) => string;
}

export default function CardChart({
  value,
  title,
  marginTop = 0,
  height = "120",
  width = "100%",
  percentage = false,
  predicate,
}: CardChartProps) {
  const colorToUse = predicate ? predicate(parseInt(value)) : "transparent";
  return (
    <Box
      mt={marginTop}
      style={{ border: "2px solid #e0e0e0", borderRadius: 4 }}
      h={height}
      w={width}
    >
      <Text fw={600} ml={4} flex={3} ta={"left"} size="sm">
        {title}
      </Text>
      <Center h="80%">
        <Title
          order={1}
          style={{
            background: "#228be6",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: colorToUse,
          }}
        >
          {value}
          {percentage ? "%" : ""}
        </Title>
      </Center>
    </Box>
  );
}
