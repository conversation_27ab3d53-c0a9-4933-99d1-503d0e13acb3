import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  Box,
  Flex,
  Grid,
  NumberInput,
  Paper,
  Select,
  Textarea,
  TextInput,
  Text,
  Stack,
  Group,
} from "@mantine/core";
import { useState, type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { RefundPaymentMethod, RefundStatus, ReturnReason } from "@/types/enums";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { getEnumTransKey } from "@/utils/trans";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { RefundProductList } from "@/features/refundProducts/routes/RefundProductList";
import { DatePickerInput } from "@mantine/dates";
import { RefundAttachment } from "./RefundAttachment";
import { type Schemas } from "@/types";
import { CaseLookup } from "@/components/Lookup/Features/Cases/CaseLookup";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

const formSchema = z.object({
  number: z.string(),
  totalRefundedAmount: z.coerce.number().nullable(),
  totalOrderAmount: z.coerce.number().nullable(),
  description: z.string(),
  internalRemarks: z.string(),
  orderNumber: z.string(),
  customerBankAccountNumber: z.string(),
  paymentMethod: z.enum(RefundPaymentMethod as [string]).nullable(),
  returnReason: z.enum(ReturnReason as [string]).nullable(),
  status: z.enum(RefundStatus as [string]).nullable(),
  caseId: z.string().nullable(),
  case: z.object({}).nullable(),
  customerId: z.string().nullable(),
  customer: z.object({}).nullable(),
  contactId: z.string().nullable(),
  contact: z.object({}).nullable(),
  businessUnitId: z.string().nullable(),
  businessUnit: z.object({}).nullable(),
  id: z.string(),
  handlingDate: z.date().nullable(),
  recordState: z.string(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface Attachment {
  attachmentType: Schemas["AttachmentTypeEnum"];
  file: File;
}

interface RefundFormProps {
  onSubmit: (
    values: Partial<FormSchema>,
    attachments: Attachment[],
    deleteAttachments: string[],
  ) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
  recordState?: string;
}

export function RefundForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
  recordState,
}: RefundFormProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const [attachment, setAttachment] = useState<Attachment[]>([]);
  const [deleteAttachments, setDeleteAttachments] = useState<string[]>([]);

  const form = useForm<FormSchema>({
    initialValues: {
      number: initialValues?.number ?? "",
      totalRefundedAmount: initialValues?.totalRefundedAmount ?? null,
      totalOrderAmount: initialValues?.totalOrderAmount ?? null,
      description: initialValues?.description ?? "",
      internalRemarks: initialValues?.internalRemarks ?? "",
      orderNumber: initialValues?.orderNumber ?? "",
      customerBankAccountNumber: initialValues?.customerBankAccountNumber ?? "",
      paymentMethod: initialValues?.paymentMethod ?? null,
      returnReason: initialValues?.returnReason ?? null,
      status: initialValues?.status ?? "New",
      caseId: initialValues?.caseId ?? "",
      case: initialValues?.case ?? null,
      customerId: initialValues?.customerId ?? "",
      customer: initialValues?.customer ?? null,
      contactId: initialValues?.contactId ?? "",
      contact: initialValues?.contact ?? null,
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      id: initialValues?.id ?? "",
      handlingDate: initialValues?.handlingDate
        ? new Date(initialValues.handlingDate)
        : null,
      recordState: initialValues?.recordState ?? "",
    },
    validate: zodResolver(formSchema),
  });

  const isFormDisabled = form.getInputProps("recordState").value === "Inactive";
  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields, attachment, deleteAttachments);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
        recordState={recordState}
        disabled={isFormDisabled}
      >
        <Grid m={16}>
          <Grid.Col span={{ base: 6, md: 6 }}>
            <Paper shadow="xs" p="lg" h="100%">
              {!isCreate && (
                <TextInput
                  disabled
                  label={t("refunds.number")}
                  {...form.getInputProps("number")}
                />
              )}

              <TextInput
                label={t("refunds.orderNumber")}
                {...form.getInputProps("orderNumber")}
              />
              <FieldValidation
                isDirty={form.isDirty("customerBankAccountNumber")}
              >
                <TextInput
                  label={t("refunds.customerBankAccountNumber")}
                  {...form.getInputProps("customerBankAccountNumber")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("totalRefundedAmount")}>
                <NumberInput
                  disabled
                  label={t("refunds.totalRefundedAmount")}
                  leftSection={config.CURRENCY.symbol}
                  {...form.getInputProps("totalRefundedAmount")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("totalOrderAmount")}>
                <NumberInput
                  label={t("refunds.totalOrderAmount")}
                  leftSection={config.CURRENCY.symbol}
                  {...form.getInputProps("totalOrderAmount")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("status")}>
                <Select
                  searchable
                  label={t("refunds.status")}
                  data={RefundStatus.map((value) => ({
                    value,
                    label: t(getEnumTransKey("refunds", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("status")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("returnReason")}>
                <Select
                  searchable
                  label={t("refunds.returnReason")}
                  data={ReturnReason.map((value) => ({
                    value,
                    label: t(getEnumTransKey("refunds", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("returnReason")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("paymentMethod")}>
                <Select
                  searchable
                  label={t("refunds.paymentMethod")}
                  data={RefundPaymentMethod.map((value) => ({
                    value,
                    label: t(getEnumTransKey("refunds", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("paymentMethod")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("handlingDate")}>
                <DatePickerInput
                  valueFormat={dateFormat.toUpperCase()}
                  clearable
                  label={t("refunds.handlingDate")}
                  {...form.getInputProps("handlingDate")}
                />
              </FieldValidation>
              <Stack gap={8} my={4}>
                <Flex>
                  <Group
                    h={32}
                    align="center"
                    justify="flex-start"
                    gap="xs"
                    flex={2.4}
                  >
                    <Text size="sm">
                      {t("refunds.proofOfOrder")}
                      <Text span size="sm" c="red" inherit>
                        *
                      </Text>
                    </Text>
                  </Group>
                  <Stack flex={6}>
                    <RefundAttachment
                      refundId={form.getValues().id}
                      attachmentType={"ProofOfOrder"}
                      attachment={attachment}
                      setAttachment={setAttachment}
                      deleteAttachments={deleteAttachments}
                      setDeleteAttachments={setDeleteAttachments}
                      required={true}
                    />
                  </Stack>
                </Flex>
                <Flex>
                  <Stack
                    h={32}
                    align="flex-start"
                    justify="center"
                    gap="xs"
                    flex={2.4}
                  >
                    <Text size="sm">{t("refunds.bankAccount")}</Text>
                  </Stack>
                  <Stack flex={6}>
                    <RefundAttachment
                      refundId={form.getValues().id}
                      attachmentType={"BankAccount"}
                      attachment={attachment}
                      setAttachment={setAttachment}
                      deleteAttachments={deleteAttachments}
                      setDeleteAttachments={setDeleteAttachments}
                      allowDelete={true}
                    />
                  </Stack>
                </Flex>
              </Stack>
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 6, md: 6 }}>
            <Paper shadow="xs" p="lg" h="100%">
              <FieldValidation isDirty={form.isDirty("caseId")}>
                <CaseLookup
                  label={t("refunds.case")}
                  initial={form.getValues().case}
                  initialId={form.getValues().caseId}
                  identifier="caseIdRefund"
                  {...form.getInputProps("caseId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("customerId")}>
                <CustomerLookup
                  label={t("refunds.customer")}
                  initial={form.getValues().customer}
                  initialId={form.getValues().customerId}
                  identifier="customerIdRefund"
                  {...form.getInputProps("customerId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("contactId")}>
                <ContactLookup
                  label={t("refunds.contact")}
                  initial={form.getValues().contact}
                  initialId={form.getValues().contactId}
                  identifier="contactIdRefund"
                  {...form.getInputProps("contactId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("businessUnitId")}>
                <BusinessUnitLookup
                  label={t("refunds.businessUnit")}
                  initial={form.getValues().businessUnit}
                  initialId={form.getValues().businessUnitId}
                  identifier="businessUnitIdRefund"
                  {...form.getInputProps("businessUnitId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("description")}>
                <Textarea
                  label={t("refunds.description")}
                  {...form.getInputProps("description")}
                  minRows={4}
                  autosize
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("internalRemarks")}>
                <Textarea
                  label={t("refunds.internalRemarks")}
                  {...form.getInputProps("internalRemarks")}
                  minRows={4}
                  autosize
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 12 }}>
            {!isCreate && (
              <Box mb={6}>
                <RefundProductList
                  parentEntityId={initialValues?.id ?? ""}
                  parentEntityName="Refunds"
                  parentEntityIdParam="refundId"
                />
              </Box>
            )}
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
