import { Box, Flex, Group, Text } from "@mantine/core";
import { type PossibleTabs } from "../../Home";
import { type TabProps } from "../../Structure/HomeTabs";

import classes from "../..//Home.module.css";
import NumberLoader from "../../Components/numberLoader";
import { useTranslation } from "react-i18next";
import OpenLeadsQuery from "./OpenLeadsQuery";

const TAB: PossibleTabs = "OpenLeads";

export default function OpenLeadsTab({ setActiveTab, isActive }: TabProps) {
  const { t } = useTranslation("features");

  const { totalCount } = OpenLeadsQuery();
  return (
    <Box
      w={{ base: "100%", xs: "100%", md: "32%", lg: "16%", xl: "16%" }}
      className={isActive ? classes.activeHomeTab : classes.homeTab}
      onClick={() => setActiveTab(TAB)}
    >
      <Flex justify="center" align="center" direction="row" wrap="wrap">
        <Group justify="space-between" gap={4} w={"100%"}>
          <Text w={"100%"} className={classes.tabTitleText}>
            {t(`leads.openLeadsPriority5`)}
          </Text>
          <Text fz={{ xs: 16, sm: 12, md: 14, lg: 16, xl: 18 }} fw={700}></Text>
        </Group>

        <Text className={classes.tabDisplayText}>
          <NumberLoader number={totalCount} />
        </Text>
      </Flex>
    </Box>
  );
}
