import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { PageLoader } from "@/components/PageLoader";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";
import {
  type FormSchema,
  ContractLineForm,
} from "../components/ContractLineForm";

export function ContractLineShow() {
  const queryCache = useQueryClient();
  const navigate = useNavigate();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["ContractLine"],
    Schemas["ContractLineCreateDto"]
  >({
    resourcePath: "/api/ContractLines/{id}",
    resourceId: id!,
    queryKey: "contractLine",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["ContractLine"]>({
    resourcePath: "/api/ContractLines/{id}",
    resourceId: id!,
    queryKey: "contractLine",
  });
  const refreshForm = async () => {
    await queryCache.invalidateQueries("contractLine_" + id);
  };
  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <ContractLineForm
      isCreate={false}
      title={t("contractLines.showTitle", { id })}
      contractLineId={data.id}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/contractLines");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/contractLines");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      initialValues={
        filterFalsyValues({
          ...data,
          from: data.from ? new Date(data.from) : null,
          to: data.to ? new Date(data.to) : null,
          insuranceStart: data.insuranceStart
            ? new Date(data.insuranceStart)
            : null,
          insuranceEnd: data.insuranceEnd ? new Date(data.insuranceEnd) : null,
        }) as FormSchema
      }
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
      headerSection={
        <Group>
          <EntityLayout.OwnerHeader
            setOwner={(userId: string) => {
              update({ ownerId: userId });
            }}
            ownerId={data.ownerId}
          />
        </Group>
      }
    />
  );
}
