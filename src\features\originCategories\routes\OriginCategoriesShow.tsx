import { PageLoader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  OriginCategoriesForm,
  type FormSchema,
} from "../components/OriginCategoriesForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function OriginCategoriesShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["OriginCategory"],
    Schemas["OriginCategoryCreateDto"]
  >({
    resourcePath: "/api/OriginCategories/{id}",
    resourceId: id!,
    queryKey: "originCategory",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/OriginCategories";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("originCategory_" + id);
  };
  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/OriginCategories/{id}",
    resourceId: id!,
    queryKey: "originCategory",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["OriginCategory"]>({
    resourcePath: "/api/OriginCategories/{id}",
    resourceId: id!,
    queryKey: "originCategory",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <OriginCategoriesForm
      isCreate={false}
      title={t("originCategories.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["OriginCategoryCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("originCategories.delete", { id })}
            modalContent={t("originCategories.deleteConfirmation", { id })}
            confirmLabel={t("originCategories.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/OriginCategories");
              }
            }}
          />
        </Group>
      }
    />
  );
}
