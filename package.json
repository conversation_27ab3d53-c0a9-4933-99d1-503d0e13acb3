{"name": "unitlogic-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "analyze": "source-map-explorer 'dist/**/*.js'", "ts-codegen": "npx cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 && npx openapi-typescript http://localhost:5163/swagger/v1/swagger.json -o src/types/api.generated.ts", "prepare": "husky", "locize-download": "locize download --project-id 0716991a-8210-4fa6-8622-39dcd0059b46 --api-key 6fcf884c-fa70-4a94-9cf2-311e1dc02053 --path public/locales"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@hookform/resolvers": "^3.3.4", "@mantine/carousel": "^7.10.1", "@mantine/charts": "^7.5.1", "@mantine/code-highlight": "^7.5.1", "@mantine/core": "^7.9", "@mantine/dates": "^7.10.1", "@mantine/dropzone": "^7.5.1", "@mantine/form": "^7.5.1", "@mantine/hooks": "^7.10.1", "@mantine/modals": "^7.5.1", "@mantine/notifications": "^7.5.1", "@mantine/nprogress": "^7.5.1", "@mantine/spotlight": "^7.5.1", "@mantine/tiptap": "^7.8.1", "@microsoft/signalr": "^8.0.7", "@sentry/react": "^8.7.0", "@sentry/vite-plugin": "^2.17.0", "@tabler/icons-react": "^2.47.0", "@tiptap/extension-link": "^2.2.2", "@tiptap/react": "^2.2.2", "@tiptap/starter-kit": "^2.2.2", "@total-typescript/ts-reset": "^0.5.1", "@types/react-signature-canvas": "^1.0.7", "axios": "^1.6.7", "date-fns": "^3.6.0", "formik": "^2.4.6", "i18next": "^23.8.2", "i18next-http-backend": "^2.4.3", "i18next-localstorage-cache": "^1.1.1", "intl-pluralrules": "^2.0.1", "luxon": "^3.4.4", "mantine-form-zod-resolver": "^1.1.0", "mantine-react-table": "^2.0.0-beta.3", "match-sorter": "^6.3.3", "query-string": "^9.0.0", "react": "^18.2.0", "react-big-calendar": "^1.11.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.50.0", "react-i18next": "^14.0.1", "react-idle-timer": "^5.7.2", "react-query": "^3.39.3", "react-router-dom": "^6.21.3", "react-select": "^5.8.0", "react-signature-canvas": "^1.1.0-alpha.2", "sort-by": "^1.2.0", "type-fest": "^4.12.0", "usehooks-ts": "^3.1.0", "validator": "^13.11.0", "zod": "^3.22.4", "zustand": "^5.0.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.3.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/luxon": "^3.4.2", "@types/node": "^20.11.10", "@types/react": "^18.3.3", "@types/react-big-calendar": "^1.8.8", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@types/validator": "^13.11.9", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.2.2", "autoprefixer": "^10.4.17", "dotenv": "^16.4.5", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^9.0.11", "jsdom": "^24.0.0", "jsdom-testing-mocks": "^1.13.0", "lint-staged": "^15.2.2", "postcss": "^8.4.35", "postcss-preset-mantine": "^1.13.0", "postcss-simple-vars": "^7.0.1", "prettier": "3.2.4", "recharts": "^2.12.7", "source-map-explorer": "^2.5.3", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-mkcert": "^1.17.3", "vitest": "^1.2.2"}, "lint-staged": {"**/*.{js,ts,jsx,tsx}": ["eslint --fix --report-unused-disable-directives --max-warnings 0"], "**/*.{json,js,ts,jsx,tsx,css}": ["prettier --write --ignore-unknown"]}}