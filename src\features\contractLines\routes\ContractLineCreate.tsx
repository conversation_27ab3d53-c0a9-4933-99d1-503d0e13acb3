import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { ContractLineForm } from "../components/ContractLineForm";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { notifications } from "@mantine/notifications";
import { Group } from "@mantine/core";

export function ContractLineCreate() {
  const { t } = useTranslation("features");
  const [close, setClose] = useState(false);
  const navigate = useNavigate();
  const { mutate } = useEntityCreateMutation<
    Schemas["ContractLine"],
    Schemas["ContractLineCreateDto"]
  >({ resourcePath: "/api/ContractLines", queryKey: "contractLines" });
  const [searchParams] = useSearchParams();
  const contractId = searchParams.get("contractId");

  return (
    <ContractLineForm
      isCreate={true}
      initialValues={{
        contractId: contractId ?? "",
      }}
      title={t("contractLines.createTitle")}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/contractLines");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/contractLines/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
