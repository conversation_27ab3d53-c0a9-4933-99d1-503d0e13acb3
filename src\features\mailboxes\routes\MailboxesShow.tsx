import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { MailboxesForm, type FormSchema } from "../components/MailboxesForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function MailboxesShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Mailbox"],
    Schemas["MailboxCreateDto"]
  >({
    resourcePath: "/api/Mailboxes/{id}",
    resourceId: id!,
    queryKey: "mailbox",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/Mailboxes";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("mailbox_" + id);
  };
  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/Mailboxes/{id}",
    resourceId: id!,
    queryKey: "mailbox",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Mailbox"]>({
    resourcePath: "/api/Mailboxes/{id}",
    resourceId: id!,
    queryKey: "mailbox",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <MailboxesForm
      isCreate={false}
      title={t("mailbox.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["MailboxCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("mailbox.delete", { id })}
            modalContent={t("mailbox.deleteConfirmation", { id })}
            confirmLabel={t("mailbox.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/Mailboxes");
              }
            }}
          />
        </Group>
      }
    />
  );
}
