import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Anchor, Box, Center, Flex, Loader, Stack } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { DateTime } from "luxon";
interface TimelineAppointmentPanelProps {
  appointmentId: string;
}

export function TimelineAppointmentPanel({
  appointmentId,
}: TimelineAppointmentPanelProps) {
  const { t } = useTranslation("features");
  const { dateFormat } = useSettingsContext();
  const { data: appointmentData, isLoading: isLoadingAppointment } =
    useEntityQuery<Schemas["AppointmentRetrieveDto"]>({
      resourcePath: `/api/Appointments/{id}`,
      resourceId: appointmentId,
      queryKey: ["appointment", appointmentId],
    });

  const { data: appUserDataCreatedBy, isLoading: isLoadingAppUserCreatedBy } =
    useEntityQuery<Schemas["AppUserRetrieveDto"]>({
      resourcePath: `/api/AppUsers/{id}`,
      resourceId: appointmentData?.createdBy ?? "",
      queryKey: ["appUser", appointmentData?.createdBy],
    });

  const { data: appUserDataModifiedBy, isLoading: isLoadingAppUserModifiedBy } =
    useEntityQuery<Schemas["AppUserRetrieveDto"]>({
      resourcePath: `/api/AppUsers/{id}`,
      resourceId: appointmentData?.modifiedBy ?? "",
      queryKey: ["appUser", appointmentData?.modifiedBy],
    });

  if (
    isLoadingAppointment ||
    isLoadingAppUserCreatedBy ||
    isLoadingAppUserModifiedBy
  ) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <Box ml={8}>
      <Stack gap="xs">
        <Flex>
          {t("common.owner")}
          <Anchor
            href={`/app/appUsers/${appointmentData?.ownerId}`}
            style={{ cursor: "pointer" }}
            ml={8}
          >
            {appointmentData?.owner?.name ?? appointmentData?.owner?.email}
          </Anchor>
        </Flex>
        <Flex>
          {t("appointments.businessUnit")}:
          <Anchor
            href={`/app/businessUnits/${appointmentData?.businessUnitId}`}
            style={{ cursor: "pointer" }}
            ml={8}
          >
            {appointmentData?.businessUnit?.name ??
              appointmentData?.businessUnit?.code}
          </Anchor>
        </Flex>
        <Flex>
          {t("appointments.rescheduledWizard")}:
          <Box ml={8}>
            {appointmentData?.rescheduledFromWizard ? "Yes" : "No"}
          </Box>
        </Flex>
        {appointmentData?.appointmentStatus == "Rescheduled" &&
          appointmentData?.rescheduleReason && (
            <Flex>
              {t("appointments.rescheduledWizardReason")}:
              <Box ml={8}>
                {t(`wizards.${appointmentData?.rescheduleReason}`)}
              </Box>
            </Flex>
          )}
        <Flex>
          {t("appointments.startDate")}:
          <Box ml={8}>
            {DateTime.fromJSDate(
              new Date(appointmentData?.startDate ?? ""),
            ).toFormat(dateFormat + " HH:mm")}
          </Box>
        </Flex>
        <Flex>
          {t("appointments.endDate")}:
          <Box ml={8}>
            {DateTime.fromJSDate(
              new Date(appointmentData?.endDate ?? ""),
            ).toFormat(dateFormat + " HH:mm")}
          </Box>
        </Flex>
        <Flex>
          {t("common.status")}:
          <Box ml={8}>{appointmentData?.appointmentStatus ?? ""}</Box>
        </Flex>
        <Flex>
          {t("common.createdOn")}:
          <Box ml={8}>
            {DateTime.fromJSDate(
              new Date(appointmentData?.createdOn ?? ""),
            ).toFormat(dateFormat + " HH:mm:ss")}
          </Box>
        </Flex>
        <Flex>
          {t("common.createdBy")}:
          <Anchor
            href={`/app/appUsers/${appointmentData?.createdBy}`}
            style={{ cursor: "pointer" }}
            ml={8}
          >
            {appUserDataCreatedBy?.name ?? ""}
          </Anchor>
        </Flex>
        <Flex>
          {t("common.modifiedOn")}:
          <Box ml={8}>
            {DateTime.fromJSDate(
              new Date(appointmentData?.modifiedOn ?? ""),
            ).toFormat(dateFormat + " HH:mm:ss")}
          </Box>
        </Flex>
        <Flex>
          {t("common.modifiedBy")}:
          <Anchor
            href={`/app/appUsers/${appointmentData?.modifiedBy}`}
            style={{ cursor: "pointer" }}
            ml={8}
          >
            {appUserDataModifiedBy?.name ?? ""}
          </Anchor>
        </Flex>
      </Stack>
    </Box>
  );
}
