import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { MailboxesForm } from "../components/MailboxesForm";
import { EntityLayout } from "@/features/entity";
import { Group, Loader } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";

interface MailboxesCreateProps {
  parentEntityId?: string;
  redirectTo?: string;
  usingModal?: boolean;
  closeModal?: () => void;
}

export function MailboxesCreate({
  parentEntityId: propParentEntityId,
  redirectTo: propRedirectTo,
  usingModal: propUsingModal,
  closeModal, // Add closeModal to props
}: MailboxesCreateProps) {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Mailbox"],
    Schemas["MailboxCreateDto"]
  >({ resourcePath: "/api/Mailboxes", queryKey: "mailbox" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();

  const businessUnitId =
    propParentEntityId ?? searchParams.get("businessUnitId");

  const redirectTo =
    propRedirectTo ?? searchParams.get("redirectTo") ?? "/app/advisedProducts";
  const usingModal = propUsingModal ?? false;

  const {
    data: businessUnit = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/BusinessUnits/{id}",
    resourceId: businessUnitId!,
    queryKey: ["businessUnit", businessUnitId],
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <MailboxesForm
      initialValues={{
        businessUnitId: businessUnitId ?? "",
        businessUnit: businessUnit,
      }}
      isCreate={true}
      title={t("mailbox.createTitle")}
      actionSection={
        <Group>
          {!usingModal && <EntityLayout.SaveButton setClose={setClose} />}
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (usingModal) {
              if (close) {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
                closeModal?.();
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
              }
            } else {
              if (close) {
                navigate(redirectTo);
              } else {
                let navigateTo = `/app/Mailboxes/${data.data.id}`;
                navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
                navigate(navigateTo);
              }
            }
          },
        });
      }}
    />
  );
}
