import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Checkbox, Grid, Paper, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import validator from "validator";
import i18next from "i18next";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";

const formSchema = z.object({
  email: z.string().refine(
    (value) => {
      if (value) {
        return validator.isEmail(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidEmail") },
  ),
  businessUnitId: z.string().nullable(),
  businessUnit: z.object({}).nullable(),
  isDefault: z.boolean().nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface MailboxesFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function MailboxesForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: MailboxesFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      email: initialValues?.email ?? "",
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      isDefault: initialValues?.isDefault ?? false,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <FieldValidation isDirty={form.isDirty("email")}>
                <TextInput
                  required
                  label={t("appointments.email")}
                  {...form.getInputProps("email")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("businessUnitId")}>
                <BusinessUnitLookup
                  required
                  label={t("appointments.businessUnit")}
                  initial={form.getValues().businessUnit}
                  initialId={form.getValues().businessUnitId}
                  identifier="businessUnitIdMailbox"
                  {...form.getInputProps("businessUnitId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("isDefault")}>
                <Checkbox
                  mt="sm"
                  checked={form.getValues().isDefault}
                  label={t("appointments.isDefault")}
                  {...form.getInputProps("isDefault")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
