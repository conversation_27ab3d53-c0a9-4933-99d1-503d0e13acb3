import type React from "react";
import { Grid } from "@mantine/core";
import HorizontalBarChart from "../common/HorizontalBarChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import CardChart from "../common/CardChart";

const metrics: MetricProps[] = [{ metric: "AllAppointments" }];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
    <Grid.Col span={6}>
      <HorizontalBarChart
        marginTop={10}
        height="180"
        data={data[0]}
        title={"title"}
      />
    </Grid.Col>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
    <Grid.Col span={6}>
      <HorizontalBarChart
        marginTop={10}
        height="180"
        data={data[0]}
        title={"title"}
      />
    </Grid.Col>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
    <Grid.Col span={6}>
      <HorizontalBarChart
        marginTop={10}
        height="180"
        data={data[0]}
        title={"title"}
      />
    </Grid.Col>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
    <Grid.Col span={6}>
      <HorizontalBarChart
        marginTop={10}
        height="180"
        data={data[0]}
        title={"title"}
      />
    </Grid.Col>
    <Grid.Col span={3}>
      <CardChart marginTop={10} value="0" height="180" title={"title"} />
    </Grid.Col>
  </Grid>
);

const CaseOverzicht: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default CaseOverzicht;
