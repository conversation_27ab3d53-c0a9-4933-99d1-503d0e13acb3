import { useLayoutVisibility } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { Loader } from "@mantine/core";
import {
  IconPlayerPlayFilled,
  IconPlayerStopFilled,
} from "@tabler/icons-react";
import { type MRT_RowData, type MRT_TableInstance } from "mantine-react-table";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

interface ButtonOmnichannelProps {
  table: MRT_TableInstance<MRT_RowData> | null;
}

export default function ButtonOmnichannel({ table }: ButtonOmnichannelProps) {
  const {
    isOmnichannelActive,
    OCLeadValue,
    setOCLeadValue,
    finalUser,
    UpdateOmnichannelUser,
    isLoading,
    setUserUpdating,
  } = useUserContext();
  const [isActive, setIsActive] = useState(isOmnichannelActive);
  const { t } = useTranslation("features");
  const { setVisibility } = useLayoutVisibility();
  const navigate = useNavigate();

  useEffect(() => {
    if (OCLeadValue !== "") {
      setOCLeadValue("");
    }
  }, [setOCLeadValue, OCLeadValue]);

  if (isLoading) {
    return <Loader />;
  }
  const userId = finalUser?.id;

  const onButtonClick = () => {
    setIsActive(!isActive);
    if (userId && UpdateOmnichannelUser) {
      UpdateOmnichannelUser(userId, !isActive, setUserUpdating, table);
    }
    if (setUserUpdating) setUserUpdating(true);
    if (isActive) {
      setVisibility({
        isHeaderVisible: true,
        isNavbarVisible: true,
        isOmnichannelVisible: false,
      });
      navigate("/app/omnichannel");
    } else {
      setVisibility({
        isHeaderVisible: true,
        isNavbarVisible: true,
        isOmnichannelVisible: true,
      });
    }
  };

  return (
    <ButtonMain
      label={
        isActive
          ? t("leads.omnichannelDeactivate")
          : t("leads.omnichannelActivate")
      }
      icon={
        isActive ? (
          <IconPlayerStopFilled size={18} />
        ) : (
          <IconPlayerPlayFilled size={18} />
        )
      }
      onClick={onButtonClick}
    />
  );
}
