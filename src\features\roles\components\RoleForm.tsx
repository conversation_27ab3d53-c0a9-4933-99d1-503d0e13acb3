import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Grid, Paper, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import "@mantine/tiptap/styles.css";
import { type Schemas, type PathKeys } from "@/types";
import { EntityLinkRenderer } from "@/components/Table/CellRenderers/EntityLinkRenderer";

const formSchema = z.object({
  name: z.string(),
  id: z.string(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface RoleFormProps {
  actionSection?: ReactNode;
  headerSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  roleId?: string;
  title: string;
}

export function RoleForm({
  actionSection = null,
  headerSection = null,
  initialValues,
  roleId,
  title,
}: RoleFormProps) {
  const { t } = useTranslation("features");

  return (
    <form>
      <EntityLayout
        title={title}
        actionSection={actionSection}
        headerSection={headerSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 3 }}>
            <Paper shadow="xs" p="xs" pt="">
              <TextInput
                label={t("roles.name")}
                disabled
                value={initialValues?.name}
              />
              <TextInput
                label={t("roles.id")}
                disabled
                value={initialValues?.id}
              />
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 9 }}>
            <EntityLayout stickyHeader={false}>
              <EntityLayout.TableMantine<
                Schemas["UserOutcomeDto"],
                Schemas["UserOutcomeDtoPagedList"]
              >
                resourcePath={`/auth/roles/${roleId}/users` as PathKeys}
                queryKey="roleUsers"
                entityPath="appUsers"
                redirectTo={window.location.pathname}
                columns={[
                  {
                    accessorKey: "name",
                    header: t("appUsers.name"),
                    filterVariant: "text",
                    Cell: (props) => EntityLinkRenderer(props, "appUsers"),
                  },
                  {
                    accessorKey: "email",
                    header: t("appUsers.email"),
                    filterVariant: "text",
                    Cell: (props) => EntityLinkRenderer(props, "appUsers"),
                  },
                  {
                    accessorKey: "last_login",
                    header: t("appUsers.last_login"),
                    filterVariant: "text",
                  },
                  {
                    accessorKey: "id",
                    header: t("appUsers.id"),
                    filterVariant: "text",
                  },
                ]}
                initialSorting={[
                  {
                    id: "createdOn",
                    desc: true,
                  },
                ]}
              />
            </EntityLayout>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
