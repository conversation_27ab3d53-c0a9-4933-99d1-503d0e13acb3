.rbc-header {
  color: white;
  font-size: 14px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 42px !important;
  background: #17405d;
}

.rbc-allday-cell {
  display: none;
}

.rbc-agenda-table thead tr {
  display: flex;
  flex-direction: row;
}

.rbc-agenda-table tbody {
  display: flex;
  flex-direction: column;
}

.rbc-agenda-table tbody tr {
  display: flex !important;
  flex-direction: row;
  border: 1px solid rgb(134, 128, 128) !important;
}
.rbc-agenda-table thead tr th {
  display: none;
}
.rbc-agenda-date-cell {
  display: none;
}

.rbc-agenda-time-cell {
  display: none;
}

.rbc-agenda-event-cell {
  width: 100% !important;
}

.rbc-time-header-cell .rbc-today {
  background-color: #04aa6d;
}

.rbc-time-slot {
  color: #74a4c3;
  z-index: 1;
}

.rbc-time-slot:not(.rbc-today .rbc-time-slot) {
  background-color: #eff4f7;
}

/*Event Styles*/
.rbc-event,
.rbc-background-event {
  z-index: 2;
  padding: 0px !important;
  border: none !important;
}

.rbc-event-label {
  display: none;
}

.rbc-events-container {
  width: 100%;
}

.rbc-month-row {
  display: inline-table !important;
  flex: 0 0 0 !important;
  min-height: 140px !important;
}

.rbc-timeslot-group {
  min-height: 100px;
}

.contactMoment-accordion-item:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.rbc-event-content {
}

.rbc-addons-dnd-resizable {
}

.rbc-event {
}

.calendar-toolbar-icon {
  color: rgb(84, 101, 133);
}
.calendar-toolbar-icon:hover {
  color: var(--mantine-color-primary-6);
  cursor: pointer;
}
