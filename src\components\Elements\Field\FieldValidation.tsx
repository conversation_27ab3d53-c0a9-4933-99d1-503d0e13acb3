import { Indicator } from "@mantine/core";
import { type ReactNode, type PropsWithChildren } from "react";

interface FieldValidationProps {
  isDirty: boolean;
  children: ReactNode;
}

export function FieldValidation({
  children,
  isDirty,
}: PropsWithChildren<FieldValidationProps>) {
  return (
    <Indicator
      size={10}
      processing
      position="middle-end"
      withBorder
      offset={5}
      color={"rgba(124, 229, 247, 1)"}
      disabled={!isDirty}
    >
      {children}
    </Indicator>
  );
}
