import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import {
  Button,
  Paper,
  Stack,
  Text,
  Group,
  TextInput,
  Select,
  Table,
  Checkbox,
  Badge,
  ActionIcon,
} from "@mantine/core";
import { IconArrowLeft, IconX, IconSearch } from "@tabler/icons-react";
import { useState } from "react";

interface PageAddProductProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  available: boolean;
}

const mockProducts: Product[] = [
  {
    id: "1",
    name: "Name of product",
    category: "Category",
    price: 100.0,
    available: true,
  },
  {
    id: "2",
    name: "Name of product",
    category: "Category",
    price: 100.0,
    available: true,
  },
  {
    id: "3",
    name: "Name of product",
    category: "Category",
    price: 100.0,
    available: true,
  },
  {
    id: "4",
    name: "Name of product",
    category: "Category",
    price: 100.0,
    available: true,
  },
];

export default function PageAddProduct({
  lead: _lead,
  setPages,
  pages,
}: PageAddProductProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  const handleProductToggle = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts((prev) => [...prev, productId]);
    } else {
      setSelectedProducts((prev) => prev.filter((id) => id !== productId));
    }
  };

  const handleAddSelectedProducts = () => {
    setPages([...pages, "PRODUCTS"]);
  };

  const handleBack = () => {
    setPages([...pages, "PRODUCTS"]);
  };

  const handleClose = () => {
    setPages([...pages, "PRODUCTS"]);
  };

  const filteredProducts = mockProducts.filter((product) => {
    const matchesSearch = product.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesCategory =
      !selectedCategory || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = Array.from(new Set(mockProducts.map((p) => p.category)));

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <Paper
        p="xl"
        radius="md"
        style={{
          width: "90%",
          maxWidth: 800,
          maxHeight: "90%",
          overflow: "auto",
          position: "relative",
        }}
      >
        <Stack gap="lg">
          <Group justify="space-between" align="center">
            <Group gap="md">
              <ActionIcon variant="subtle" onClick={handleBack}>
                <IconArrowLeft size={20} />
              </ActionIcon>
              <Text size="xl" fw={600}>
                Add product
              </Text>
            </Group>
            <ActionIcon variant="subtle" onClick={handleClose}>
              <IconX size={20} />
            </ActionIcon>
          </Group>

          <Group gap="md">
            <TextInput
              placeholder="Search..."
              value={searchTerm}
              onChange={(event) => setSearchTerm(event.currentTarget.value)}
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
            />
            <Select
              placeholder="Product category (1)"
              data={categories.map((cat) => ({ value: cat, label: cat }))}
              value={selectedCategory}
              onChange={(value) => setSelectedCategory(value || "")}
              clearable
              style={{ minWidth: 200 }}
            />
          </Group>

          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th style={{ width: 40 }}></Table.Th>
                <Table.Th>Name of product</Table.Th>
                <Table.Th>Category</Table.Th>
                <Table.Th>Price</Table.Th>
                <Table.Th>Status</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {filteredProducts.map((product, index) => (
                <Table.Tr
                  key={product.id}
                  style={{
                    backgroundColor: index === 1 ? "#e3f2fd" : undefined,
                    cursor: "pointer",
                  }}
                  onClick={() =>
                    handleProductToggle(
                      product.id,
                      !selectedProducts.includes(product.id),
                    )
                  }
                >
                  <Table.Td>
                    <Checkbox
                      checked={
                        selectedProducts.includes(product.id) || index === 1
                      }
                      onChange={(event) => {
                        event.stopPropagation();
                        handleProductToggle(
                          product.id,
                          event.currentTarget.checked,
                        );
                      }}
                    />
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{product.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text c="dimmed">{product.category}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>€{product.price.toFixed(2)} plus</Text>
                  </Table.Td>
                  <Table.Td>
                    {product.available && (
                      <Badge color="green" size="sm">
                        Available
                      </Badge>
                    )}
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {filteredProducts.length === 0 && (
            <Text c="dimmed" ta="center" py="xl">
              No products found matching your criteria
            </Text>
          )}

          <Group justify="flex-end" mt="xl">
            <Button
              leftSection={<IconSearch size={16} />}
              onClick={handleAddSelectedProducts}
              disabled={selectedProducts.length === 0}
            >
              Add selected products
            </Button>
          </Group>
        </Stack>
      </Paper>
    </div>
  );
}
