import { forwardRef, useImperativeHandle, useRef } from "react";
import LeadsQuickAddWizard from "./LeadsQuickAddWizard";
import { IconUserPlus } from "@tabler/icons-react";
import { Badge, Button } from "@mantine/core";
import WizardOverlay from "../Common/WizardOverlay";
import classes from "./LeadsQuickAdd.module.css";
import { useTranslation } from "react-i18next";

export interface LeadsQuickAddWizardButtonRef {
  triggerClick: () => void;
}
interface LeadsQuickAddWizardButtonProps {
  closeCombobox: () => void;
}
const LeadsQuickAddWizardButton = forwardRef<
  LeadsQuickAddWizardButtonRef,
  LeadsQuickAddWizardButtonProps
>(({ closeCombobox }, ref) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { t } = useTranslation("features");

  useImperativeHandle(ref, () => ({
    triggerClick: () => {
      buttonRef.current?.click();
      closeCombobox();
    },
  }));

  return (
    <WizardOverlay
      WizardContent={<LeadsQuickAddWizard />}
      WizardTarget={
        <Button
          ref={buttonRef}
          w={"100%"}
          justify="space-between"
          visibleFrom="md"
          className={classes.footerButton}
          variant="default"
          color={"gray"}
          size="xs"
          pl={16}
          leftSection={
            <span style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <IconUserPlus size={16} />
              {t("wizards.QuickAddLead.QuickButtonLabel")}
            </span>
          }
          rightSection={
            <Badge color="gray" variant="light" size="xs" ml={16}>
              {"ctrl + L"}
            </Badge>
          }
        />
      }
      onClick={() => {
        closeCombobox();
        // leave this as an example
        // pass this if you want to do something on child level
      }}
    />
  );
});

LeadsQuickAddWizardButton.displayName = "LeadsQuickAddWizardButton";

export default LeadsQuickAddWizardButton;
