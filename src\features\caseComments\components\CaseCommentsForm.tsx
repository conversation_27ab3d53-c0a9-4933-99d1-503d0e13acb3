import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { CaseLookup } from "@/components/Lookup/Features/Cases/CaseLookup";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";

const formSchema = z.object({
  text: z.string().nullable(),
  caseId: z.string().nullable(),
  case: z.object({}).nullable(),
  owner: z.object({}).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface CaseCommentsFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function CaseCommentsForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: CaseCommentsFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      text: initialValues?.text ?? "",
      caseId: initialValues?.caseId ?? "",
      case: initialValues?.case ?? null,
      owner: initialValues?.owner ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );

        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <FieldValidation isDirty={form.isDirty("text")}>
              <TextInput
                disabled={!isCreate}
                label={t("caseComments.text")}
                {...form.getInputProps("text")}
                {...{ labelProps: { style: { flex: 0.5 } } }}
              />
            </FieldValidation>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <FieldValidation isDirty={form.isDirty("case")}>
              <CaseLookup
                disabled={initialValues?.caseId != ""}
                label={t("caseComments.case")}
                initial={form.getValues().case}
                initialId={form.getValues().caseId}
                identifier="caseIdCaseComment"
                {...form.getInputProps("caseId")}
                {...{ labelProps: { style: { flex: 0.5 } } }}
              />
            </FieldValidation>
            <AppUserLookup
              disabled
              showFullName
              label={t("caseComments.owner")}
              initial={form.getValues().owner}
              identifier="ownerCaseComments"
              {...form.getInputProps("owner")}
              {...{ labelProps: { style: { flex: 0.5 } } }}
            />
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
