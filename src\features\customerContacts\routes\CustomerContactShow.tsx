import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { CustomerContactForm } from "../components/CustomerContactForm";
import { type SetNonNullable } from "type-fest";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function CustomerContactShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") ?? "/app/customerContacts";
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["CustomerContact"],
    Schemas["CustomerContactCreateDto"]
  >({
    resourcePath: "/api/CustomerContacts/{id}",
    resourceId: id!,
    queryKey: "customerContact",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/CustomerContacts/{id}",
    resourceId: id!,
    queryKey: "customerContact",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["CustomerContact"]>({
    resourcePath: "/api/CustomerContacts/{id}",
    resourceId: id!,
    queryKey: "customerContact",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("customerContact_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <CustomerContactForm
      isCreate={false}
      title={t("customerContacts.showTitle", { id })}
      initialValues={
        filterFalsyValues(data) as Required<
          SetNonNullable<Schemas["CustomerContact"]>
        >
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values, {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("customerContacts.delete", { id })}
            modalContent={t("customerContacts.deleteConfirmation", { id })}
            confirmLabel={t("customerContacts.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/customerContacts");
              }
            }}
          />
        </Group>
      }
    />
  );
}
