import type React from "react";
import { Grid } from "@mantine/core";
import HorizontalBarChart from "../common/HorizontalBarChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";

const metrics: MetricProps[] = [
  { metric: "TotalWalkIns" },
  { metric: "WalkInsByTimeSlot" },
  { metric: "WalkInConversionRate" },
];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={12}>
      <HorizontalBarChart
        data={data[0]}
        orientation="horizontal"
        height="180"
        title="Total Walk-Ins"
      />
    </Grid.Col>
    <Grid.Col span={6}>
      <HorizontalBarChart
        data={data[1]}
        orientation="vertical"
        height="300"
        title="Walk-Ins by Time Slot"
      />
    </Grid.Col>
    <Grid.Col span={6}>
      <HorizontalBarChart
        data={data[2]}
        orientation="vertical"
        height="300"
        title="Walk-In Conversion Rate"
        percentage
      />
    </Grid.Col>
  </Grid>
);

const WalkIn: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default WalkIn;
