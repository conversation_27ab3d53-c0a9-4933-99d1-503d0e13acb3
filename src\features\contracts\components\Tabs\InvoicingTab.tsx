import { Grid, NumberInput, Paper, Select, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useTranslation } from "react-i18next";
import { InvoicingType, PaymentMethod } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { useContractFormContext } from "../../providers/form";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

export function InvoicingTab() {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const form = useContractFormContext();

  return (
    <Grid mt="lg">
      <Grid.Col span={{ base: 12, md: 6 }}>
        <Paper shadow="xs" p="xs" pt="">
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            maxDate={
              form.getInputProps("invoicedUntil").value as Date | undefined
            }
            label={t("contracts.invoicePeriodFrom")}
            {...form.getInputProps("invoicePeriodFrom")}
          />
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            minDate={
              form.getInputProps("invoicePeriodFrom").value as Date | undefined
            }
            label={t("contracts.invoicedUntil")}
            {...form.getInputProps("invoicedUntil")}
          />
          <TextInput
            label={t("contracts.invoiceReference")}
            {...form.getInputProps("invoiceReference")}
          />
        </Paper>
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6 }}>
        <Paper shadow="xs" p="xs" pt="">
          <Select
            searchable
            clearable
            label={t("contracts.invoicingType")}
            data={InvoicingType.map((value) => ({
              value,
              label: t(getEnumTransKey("contracts", value)),
            }))}
            {...form.getInputProps("invoicingType")}
          />
          <NumberInput
            label={t("contracts.invoicingInterval")}
            {...form.getInputProps("invoicingInterval")}
            step={1}
            min={0}
          />
          <Select
            searchable
            clearable
            label={t("contracts.paymentMethod")}
            data={PaymentMethod.map((value) => ({
              value,
              label: t(getEnumTransKey("contracts", value)),
            }))}
            {...form.getInputProps("paymentMethod")}
          />
        </Paper>
      </Grid.Col>
    </Grid>
  );
}
