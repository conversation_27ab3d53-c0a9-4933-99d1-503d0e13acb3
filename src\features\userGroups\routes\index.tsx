import { Route, Routes } from "react-router-dom";
import { UserGroupList } from "./UserGroupList";
import { UserGroupShow } from "./UserGroupShow";
import { UserGroupCreate } from "./UserGroupCreate";

export default function UnitTypesRoutes() {
  return (
    <Routes>
      <Route index element={<UserGroupList />} />
      <Route path=":id" element={<UserGroupShow />} />
      <Route path="create" element={<UserGroupCreate />} />
    </Routes>
  );
}
