import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { type SetNonNullable } from "type-fest";
import { UnitForm } from "../components/UnitForm";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function UnitShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Unit"],
    Schemas["UnitCreateDto"]
  >({
    resourcePath: "/api/Units/{id}",
    resourceId: id!,
    queryKey: "unit",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Unit"]>({
    resourcePath: "/api/Units/{id}",
    resourceId: id!,
    queryKey: "unit",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("unit_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <UnitForm
      isCreate={false}
      title={t("units.showTitle", { id })}
      contextRecordId={id}
      initialValues={
        filterFalsyValues(data) as Required<SetNonNullable<Schemas["Unit"]>>
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/units");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/units");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
