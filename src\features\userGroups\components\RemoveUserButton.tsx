import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { useEntityLazyUpdateMutation } from "@/features/entity/mutations";
import { type Schemas } from "@/types";
import { But<PERSON>, Modal } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { IconMinus } from "@tabler/icons-react";
import { type MRT_RowData, type MRT_TableInstance } from "mantine-react-table";
import { useState, type MutableRefObject } from "react";
import { useTranslation } from "react-i18next";

interface RemoveUserButtonProps {
  table: MutableRefObject<MRT_TableInstance<MRT_RowData> | null> | null;
}

export function RemoveUserButton({ table }: RemoveUserButtonProps) {
  const { t } = useTranslation("features");
  const [showModal, setShowModal] = useState(false);
  const toggleModal = () => setShowModal(!showModal);

  const { mutate: update } = useEntityLazyUpdateMutation<
    Schemas["AppUser"],
    Schemas["AppUserPatchDto"]
  >({
    resourcePath: "/api/AppUsers/{id}",
    queryKey: "appUser",
  });

  function HandleUserRemoval() {
    const rows = table?.current?.getSelectedRowModel().rows;
    if (rows && rows.length > 0) {
      const users = rows.map((row) => {
        return row.original as Schemas["AppUser"];
      });

      users.forEach((user) => {
        update(
          {
            resourceId: user.id!,
            params: { userGroupId: null },
            queriesToInvalidate: ["groupUsers_list"],
          },
          {
            onSuccess: () => {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            },
            onSettled: () => {
              toggleModal();
            },
          },
        );
      });
    }
  }

  return (
    <>
      <ButtonMain
        label={t("userGroups.removeUser")}
        color="red"
        icon={<IconMinus size={18} />}
        onClick={() => {
          if (table?.current?.getSelectedRowModel().rows?.length === 0) {
            notifications.show({
              color: "red",
              title: t("userGroups.error"),
              message: t("userGroups.noRowsSelected"),
            });
            return;
          } else {
            toggleModal();
          }
        }}
      />
      <Modal
        opened={showModal}
        onClose={toggleModal}
        title={t("userGroups.deleteConfirm")}
        size="sm"
      >
        <Button
          variant="outline"
          color="red"
          w="100%"
          onClick={() => {
            HandleUserRemoval();
          }}
        >
          {t("userGroups.confirm")}
        </Button>
      </Modal>
    </>
  );
}
