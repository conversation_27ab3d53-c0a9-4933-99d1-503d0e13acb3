import {
  type MRT_ColumnFiltersState,
  type MRT_SortingState,
} from "mantine-react-table";
import { type ViewOption } from "@/components/Table/types"; // Define types in a separate file

export function GetParamsFromURL(
  searchParams: URLSearchParams,
  viewOptions: ViewOption[],
  defaultViewOption: ViewOption | undefined,
  initialSorting: MRT_SortingState | undefined,
): {
  columnFilters: MRT_ColumnFiltersState;
  sorting: MRT_SortingState;
  activeView: ViewOption | undefined;
} {
  const filtersParam = searchParams.get("filters");
  const sortingParam = searchParams.get("sorting");
  const activeViewParam = searchParams.get("activeView");

  let columnFilters: MRT_ColumnFiltersState = defaultViewOption?.filter ?? [];
  try {
    if (filtersParam) {
      columnFilters = JSON.parse(filtersParam) as MRT_ColumnFiltersState;
    }
  } catch (e) {
    console.error("Failed to parse filters from URL", e);
    // Keep default filters if parsing fails
  }

  let sorting: MRT_SortingState =
    defaultViewOption?.sorting ?? initialSorting ?? [];
  try {
    if (sortingParam) {
      sorting = JSON.parse(sortingParam) as MRT_SortingState;
    }
  } catch (e) {
    console.error("Failed to parse sorting from URL", e);
    // Keep default/initial sorting if parsing fails
  }

  const activeView =
    viewOptions.find((v) => v.value === activeViewParam) ?? defaultViewOption;

  return { columnFilters, sorting, activeView };
}
