import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { QuoteForm } from "../components/QuoteForm";
import { EntityLayout } from "@/features/entity";
import { Group, Loader } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";

export function QuoteCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Quote"],
    Schemas["QuoteCreateDto"]
  >({ resourcePath: "/api/Quotes", queryKey: "quote" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const leadId = searchParams.get("leadId");
  const redirectTo = searchParams.get("redirectTo") ?? "/app/quotes";
  const {
    data: leadRecord = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/Leads/{id}",
    resourceId: leadId!,
    queryKey: "lead",
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <QuoteForm
      initialValues={{
        leadId: leadId ?? "",
        lead: leadRecord,
      }}
      isCreate={true}
      title={t("quotes.createTitle")}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              let navigateTo = `/app/quotes/${data.data.id}`;
              navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
              navigate(navigateTo);
            }
          },
        });
      }}
    />
  );
}
