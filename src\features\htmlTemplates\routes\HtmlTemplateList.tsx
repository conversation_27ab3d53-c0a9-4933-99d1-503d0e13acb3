import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import { type ComboboxData, Group } from "@mantine/core";
import { TemplateType } from "@/types/enums";
import { lowerCaseNthLetter } from "@/utils/filters";

const PATH = "HtmlTemplates";

export function HtmlTemplateListInner({
  resourcePath,
  createPath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout title={t("htmlTemplates.title")} showBackButton={false}>
      <EntityLayout.TableMantine<
        Schemas["HtmlTemplateRetrieveDto"],
        Schemas["HtmlTemplateRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        queryKey="htmlTemplate"
        entityPath="htmlTemplates"
        title={t("htmlTemplates.title")}
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "name",
            header: t("htmlTemplates.name"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "htmlTemplates"),
          },
          {
            accessorKey: "templateType",
            header: t("htmlTemplates.templateType"),
            filterVariant: "select",
            Cell: ({ cell }) =>
              t("htmlTemplates." + lowerCaseNthLetter(cell.getValue<string>())),
            mantineFilterSelectProps: {
              data: TemplateType as ComboboxData | undefined,
            },
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function HtmlTemplateList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <HtmlTemplateListInner
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
