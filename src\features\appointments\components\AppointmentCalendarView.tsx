import { AppointmentCalendar } from "./AppointmentCalendar";
import { type Schemas } from "@/types";
import { useCallback } from "react";
import { useEntityLazyUpdateMutation } from "@/features/entity/mutations";
import { useQueryClient } from "react-query";
import { useDateRange } from "../hooks/useDateRange";
import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";

interface AppointmentCalendarViewProps {
  fromView?: string;
}

export function AppointmentCalendarView(
  { fromView }: AppointmentCalendarViewProps = { fromView: "calendar" },
) {
  const {
    dateRange,
    setDateRange,
    setCustomDateRange,
    setCalendarView,
    calendarView,
  } = useDateRange("week");

  const { finalUser, roles } = useUserContext();
  let userBusinessUnitId = finalUser?.businessUnitId ?? "";
  if (roles.includes("Sales")) userBusinessUnitId = "";
  const queryCache = useQueryClient();

  const refreshForm = async () => {
    await queryCache.invalidateQueries(
      `${dateRange.startDate}_${dateRange.endDate}_appointment_list`,
    );
  };

  const { mutate: update } = useEntityLazyUpdateMutation<
    Schemas["Appointment"],
    Schemas["AppointmentCreateDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    queryKey: "appointment",
  });

  const onDateChange = useCallback(
    (date: Date) => {
      setDateRange(date);
    },
    [setDateRange],
  );

  return (
    <>
      <AppointmentCalendar
        fromView={fromView}
        refreshForm={refreshForm}
        onDateChange={onDateChange}
        dateRange={dateRange}
        setDateRange={setCustomDateRange}
        calendarView={calendarView}
        setCalendarView={setCalendarView}
        defaultBU={userBusinessUnitId}
        values={[userBusinessUnitId]}
        onDrop={(range, resourceId) =>
          update({
            resourceId,
            params: range,
            queriesToInvalidate: [
              `${dateRange.startDate}_${dateRange.endDate}_appointment_list`,
            ],
          })
        }
        onResize={(range, resourceId) =>
          update({
            resourceId,
            params: range,
            queriesToInvalidate: [
              `${dateRange.startDate}_${dateRange.endDate}_appointment_list`,
            ],
          })
        }
      />
    </>
  );
}
