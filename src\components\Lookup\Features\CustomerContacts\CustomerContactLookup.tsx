import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { t } from "i18next";

const ENTITY = "customerContacts";
type ENTITY_TYPE = Schemas["CustomerContactRetrieveDto"];

type CustomerContactLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function CustomerContactLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  ...props
}: CustomerContactLookupProps) {
  const { searchTerm } = useLookup(
    ENTITY,
    initial?.contact?.fullName,
    initialId,
  );

  const queryParams: QueryParams = {
    pageSize: 50,
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["CustomerContactRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/CustomerContacts",
      queryKey: "customerContact",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.customer")}</Box>
      <Box>{t("common:lookup.contact")}</Box>
      <Box>{t("common:lookup.contactRole")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(
          entity.customer?.name,
          searchTerm,
          `customer_${entity.id}`,
        ),
        OptionRender(
          entity.contact?.fullName,
          searchTerm,
          `contact_${entity.id}`,
        ),
        OptionRender(
          entity.contactRole?.name,
          searchTerm,
          `contactRole_${entity.id}`,
        ),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.contact?.fullName ?? "",
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
