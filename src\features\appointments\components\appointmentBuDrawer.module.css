.scrollbar {
  &[data-orientation="vertical"] .thumb {
    background-color: #54a2de;
  }

  &[data-orientation="horizontal"] .thumb {
    background-color: #54a2de;
  }
}

.root {
  border: 1px solid
    light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
  border-radius: var(--mantine-radius-md);
  font-weight: 500;
  transition:
    color 100ms ease,
    background-color 100ms ease,
    border-color 100ms ease;
  cursor: pointer;

  &[data-checked] {
    background-color: var(--mantine-color-primary-filled);
    border-color: var(--mantine-color-primary-filled);
    color: var(--mantine-color-white);
  }
}

.selectedBuList {
  border: 1px solid
    light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
  padding: 4px;
  border-radius: var(--mantine-radius-md);
  font-weight: 500;
  transition:
    color 100ms ease,
    background-color 100ms ease,
    border-color 100ms ease;
  align-content: center;
  cursor: pointer;
}

.selectedBuListInput {
  align-content: center;
  height: auto;
}

.selectedBuListInput:hover {
  cursor: pointer;
}

.selectedBuListBody:hover {
  cursor: pointer;
}
