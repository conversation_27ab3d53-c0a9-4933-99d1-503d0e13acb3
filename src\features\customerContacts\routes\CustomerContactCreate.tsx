import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { type Schemas } from "@/types";
import { CustomerContactForm } from "../components/CustomerContactForm";
import { Group, Loader } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { useState } from "react";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";

interface CustomerContactCreateProps {
  parentEntityId?: string;
  parentEntityName?: string;
  redirectTo?: string;
  usingModal?: boolean;
  closeModal?: () => void;
}

export function CustomerContactCreate({
  parentEntityId: propParentEntityId,
  parentEntityName: propParentEntityName,
  redirectTo: propRedirectTo,
  usingModal: propUsingModal,
  closeModal, // Add closeModal to props
}: CustomerContactCreateProps) {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["CustomerContact"],
    Schemas["CustomerContactCreateDto"]
  >({ resourcePath: "/api/CustomerContacts", queryKey: "customerContact" });
  const { t } = useTranslation("features");

  const [searchParams] = useSearchParams();
  const customerId =
    propParentEntityId != null && propParentEntityName == "customers"
      ? propParentEntityId
      : searchParams.get("customerId");
  const contactId =
    propParentEntityId != null && propParentEntityName == "contacts"
      ? propParentEntityId
      : searchParams.get("contactId");
  const redirectTo =
    propRedirectTo ?? searchParams.get("redirectTo") ?? "/app/customerContacts";
  const usingModal = propUsingModal ?? false;

  const {
    data: customer = {},
    isLoading: isCustomerLoading,
    isFetching: isCustomerFetching,
  } = useEntityQuery<Schemas["Customer"]>({
    resourcePath: "/api/Customers/{id}",
    resourceId: customerId!,
    queryKey: ["customer", customerId],
  });

  const {
    data: contact = {},
    isLoading: isContactLoading,
    isFetching: isContactFetching,
  } = useEntityQuery<Schemas["Contact"]>({
    resourcePath: "/api/Contacts/{id}",
    resourceId: contactId!,
    queryKey: ["contact", contactId],
  });

  if (
    (customerId && (isCustomerLoading || isCustomerFetching)) ||
    (contactId && (isContactLoading || isContactFetching))
  ) {
    return <Loader />;
  }

  return (
    <CustomerContactForm
      isCreate={true}
      initialValues={{
        customerId: customerId ?? "",
        customer: customer,
        contactId: contactId ?? "",
        contact: contact,
      }}
      title={t("customerContacts.createTitle")}
      onSubmit={(values) => {
        mutate(values, {
          onSuccess: (data) => {
            if (usingModal) {
              if (close) {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
                closeModal?.();
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
              }
            } else {
              if (close) {
                navigate(redirectTo);
              } else {
                let navigateTo = `/app/customerContacts/${data.data.id}`;
                navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
                navigate(navigateTo);
              }
            }
          },
        });
      }}
      actionSection={
        <Group>
          {!usingModal && <EntityLayout.SaveButton setClose={setClose} />}
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
