import { PageLoader } from "@/components/PageLoader";
import { useEntityListQuery } from "@/features/entity/queries";
import { type PathKeys, type Schemas } from "@/types";
import { downloadFile } from "@/utils/downloadFile";
import {
  ActionIcon,
  Flex,
  Menu,
  Stack,
  Text,
  Center,
  Button,
  Group,
  ScrollArea,
  useMantineTheme,
} from "@mantine/core";
import {
  IconFile,
  IconPaperclip,
  IconChevronDown,
  IconDownload,
  IconFileTypePdf,
  IconFileTypeTxt,
  IconPhoto,
  IconFileZip,
  IconFileTypeJs,
  IconFileTypeHtml,
  IconFileSpreadsheet,
} from "@tabler/icons-react";
import { type PropsWithChildren } from "react";
import { useTranslation } from "react-i18next";

interface EmailAttachmentsListProps {
  emailId: string;
}

export function EmailAttachmentsList({
  emailId,
}: PropsWithChildren<EmailAttachmentsListProps>) {
  const theme = useMantineTheme();
  const { t } = useTranslation("features");
  const { data, isLoading } = useEntityListQuery<
    Schemas["AttachmentRetrieveDtoPagedList"]
  >({
    resourcePath: `/api/Emails/${emailId}/attachments` as PathKeys,
    queryKey: "attachment_" + emailId,
  });

  if (isLoading) {
    return <PageLoader />;
  }

  const handleDownloadAllAttachments = () => {
    attachments.forEach((attachment) => {
      void downloadFile({ id: attachment.id, fileName: attachment.name });
    });
  };

  const getFileIcon = (filename: string) => {
    const extension = filename.split(".").pop()?.toLowerCase();
    const defaultColor = theme.colors.primary?.[7] ?? "black";
    const iconMap: Record<string, React.ReactNode> = {
      pdf: <IconFileTypePdf size={20} stroke={1.5} color={defaultColor} />,
      txt: <IconFileTypeTxt size={20} stroke={1.5} color={defaultColor} />,
      jpg: <IconPhoto size={20} stroke={1.5} color={defaultColor} />,
      jpeg: <IconPhoto size={20} stroke={1.5} color={defaultColor} />,
      png: <IconPhoto size={20} stroke={1.5} color={defaultColor} />,
      zip: <IconFileZip size={20} stroke={1.5} color={defaultColor} />,
      js: <IconFileTypeJs size={20} stroke={1.5} color={defaultColor} />,
      html: <IconFileTypeHtml size={20} stroke={1.5} color={defaultColor} />,
      xlsx: <IconFileSpreadsheet size={20} stroke={1.5} color={defaultColor} />,
    };

    // Default icon if no match found
    return extension && iconMap[extension] ? (
      iconMap[extension]
    ) : (
      <IconFile size={20} stroke={1.5} color={defaultColor} />
    );
  };

  const attachments = (data?.data ?? []).filter(
    (item) => item.isEmailInlineImage === false,
  );

  const items = attachments.map((item) => (
    <Flex
      key={item.id}
      justify="space-between"
      gap={8}
      style={{
        border: `1px solid ${theme.colors.gray[3]}`,
        borderRadius: "8px",
        padding: "4px",
      }}
    >
      <Flex wrap="nowrap" gap={8}>
        <Center>{getFileIcon(item.name ?? "")}</Center>

        <Center>
          <Text size="xs" lineClamp={1} c={theme.colors.gray[9]}>
            {item.name}
          </Text>
        </Center>
      </Flex>

      <Menu trigger="click-hover" position="right-start">
        <Menu.Target>
          <Center>
            <ActionIcon variant="subtle" size="xs">
              <IconChevronDown stroke={1.5} />
            </ActionIcon>
          </Center>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Item
            leftSection={<IconDownload size={12} />}
            onClick={() => {
              void downloadFile({ id: item.id, fileName: item.name });
            }}
          >
            <Center>
              <Text size="xs">{t("attachments.downloadLabel")}</Text>
            </Center>
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </Flex>
  ));

  return (
    <>
      {items.length === 0 ? (
        <Stack>
          <Group gap={8}>
            <IconPaperclip size={16} />
            {t("attachments.noAttachments")}
          </Group>
        </Stack>
      ) : (
        <Stack gap={4} w={"100%"}>
          <Group justify="space-between" m={8}>
            <Group gap={8} align={"center"}>
              <IconPaperclip size={16} />
              {t("attachments.attachments")}
            </Group>
            <Button
              size="xs"
              variant="outline"
              leftSection={<IconDownload size={16} />}
              onClick={() => {
                handleDownloadAllAttachments();
              }}
            >
              {t("attachments.downloadAllLabel")}
            </Button>
          </Group>
          <ScrollArea.Autosize
            type="auto"
            mih={"4vh"}
            scrollbars="y"
            scrollbarSize={8}
            offsetScrollbars={true}
          >
            <Stack gap={4}>{items}</Stack>
          </ScrollArea.Autosize>
        </Stack>
      )}
    </>
  );
}
