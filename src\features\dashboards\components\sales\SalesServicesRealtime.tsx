import type React from "react";
import { Grid } from "@mantine/core";
import CustomBarChartVertical from "../common/CustomBarChartVertical";
import CardChart from "../common/CardChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";

const metrics: MetricProps[] = [
  { metric: "EditedLeadsSnS", sales: true },
  { metric: "ReachedLeadsSnS", sales: true },
  { metric: "BookedAppointmentsSnS", sales: true },
  { metric: "ConvertedCallsSnS", sales: true },
  { metric: "CustomLeadPriorityChange", sales: true },
];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical data={data[0]} title={"Edited Leads"} />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical data={data[1]} title={"Reached Leads"} />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical data={data[2]} title={"Booked Appointments"} />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical
          data={data[3]}
          percentage
          title={"Converted Calls"}
        />
      </Grid.Col>
    </Grid>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[0]![0]?.total.toString() ?? "0"}
          title={"Edited Leads"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[1]![0]?.total.toString() ?? "0"}
          title={"Reached Leads"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[2]![0]?.total.toString() ?? "0"}
          title={"Booked Appointments"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[3]![0]?.totalPercentage.toString() ?? "0"}
          percentage
          title={"Converted Calls"}
        />
      </Grid.Col>
    </Grid>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 12, lg: 12 }}>
        <CustomBarChartVertical
          marginTop={0}
          data={data[4]}
          title={"Lead Priority Changes"}
        />
      </Grid.Col>
    </Grid>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          percentage
          value={data[4]![0]?.percentage.toString() ?? "0"}
          title={"Priority 1"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          percentage
          value={data[4]![1]?.percentage.toString() ?? "0"}
          title={"Priority 2"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          percentage
          value={data[4]![2]?.percentage.toString() ?? "0"}
          title={"Priority 3"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          percentage
          value={data[4]![3]?.percentage.toString() ?? "0"}
          title={"Priority 4"}
        />
      </Grid.Col>
    </Grid>
  </>
);

const SalesServicesRealtime: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    report={props.report}
    timePeriod={"today"}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default SalesServicesRealtime;
