import { getEndOfTheMonth, getStartOfTheMonth } from "@/utils/date";
import { useState } from "react";

export function useMonthRange() {
  const startOfTheMonth = getStartOfTheMonth(new Date());
  const endOfTheMonth = getEndOfTheMonth(new Date());

  const [monthRange, setMonthRange] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: startOfTheMonth,
    endDate: endOfTheMonth,
  });

  return { monthRange, setMonthRange };
}
