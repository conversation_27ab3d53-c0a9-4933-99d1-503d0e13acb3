import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { UnitTypeForm } from "../components/UnitTypeForm";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { useState } from "react";
import { notifications } from "@mantine/notifications";

export function UnitTypeCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["UnitType"],
    Schemas["UnitTypeCreateDto"]
  >({ resourcePath: "/api/UnitTypes", queryKey: "unitType" });
  const { t } = useTranslation("features");

  return (
    <UnitTypeForm
      isCreate={true}
      title={t("unitTypes.createTitle")}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/unitTypes");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/unitTypes/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group mt={1}>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
