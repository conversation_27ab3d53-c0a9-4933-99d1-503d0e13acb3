import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { RentableItemForm } from "../components/RentableItemForm";
import { EntityLayout } from "@/features/entity";
import { Group } from "@mantine/core";
import { useQueryClient } from "react-query";

interface RentableItemCreateProps {
  redirectTo?: string;
  usingModal?: boolean;
}

export function RentableItemCreate({
  redirectTo: propRedirectTo,
}: RentableItemCreateProps) {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["RentableItem"],
    Schemas["RentableItemCreateDto"]
  >({ resourcePath: "/api/RentableItems", queryKey: "rentableItem" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const queryCache = useQueryClient();
  const redirectTo =
    propRedirectTo ?? searchParams.get("redirectTo") ?? "/app/rentableItems";

  return (
    <RentableItemForm
      isCreate={true}
      title={t("rentableItems.createTitle")}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);
        mutate(filteredValues, {
          onSuccess: (data) => {
            void queryCache.invalidateQueries("rentableItems_list");
            if (close) {
              navigate(redirectTo);
            } else {
              let navigateTo = `/app/rentableItems/${data.data.id}`;
              navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
              navigate(navigateTo);
            }
          },
        });
      }}
    />
  );
}
