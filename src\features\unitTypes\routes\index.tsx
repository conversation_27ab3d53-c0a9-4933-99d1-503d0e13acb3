import { Route, Routes } from "react-router-dom";
import { UnitTypeList } from "./UnitTypeList";
import { UnitTypeShow } from "./UnitTypeShow";
import { UnitTypeCreate } from "./UnitTypeCreate";

export default function UnitTypesRoutes() {
  return (
    <Routes>
      <Route index element={<UnitTypeList />} />
      <Route path=":id" element={<UnitTypeShow />} />
      <Route path="create" element={<UnitTypeCreate />} />
    </Routes>
  );
}
