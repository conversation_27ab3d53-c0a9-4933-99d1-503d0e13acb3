import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { ReservationForm } from "../components/ReservationForm";
import { EntityLayout } from "@/features/entity";
import { Group } from "@mantine/core";
import { useState } from "react";
import { notifications } from "@mantine/notifications";

export function ReservationCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Reservation"],
    Schemas["ReservationCreateDto"]
  >({ resourcePath: "/api/Reservations", queryKey: "reservation" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();

  const leadId = searchParams.get("leadId");

  return (
    <ReservationForm
      isCreate={true}
      initialValues={{
        leadId: leadId ?? "",
      }}
      title={t("reservations.createTitle")}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/reservations");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/reservations/" + data.data.id);
            }
          },
        });
      }}
    />
  );
}
