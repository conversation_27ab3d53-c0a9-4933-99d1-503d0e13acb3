import {
  Date<PERSON><PERSON><PERSON>,
  En<PERSON>ty<PERSON>ink<PERSON>enderer,
} from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { AppointmentStatus, AppointmentType } from "@/types/enums";
import { lowerCaseNthLetter } from "@/utils/filters";
import { type ComboboxData } from "@mantine/core";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function AppointmentsColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "appointmentType",
      header: t("appointments.appointmentType"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("appointments." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: AppointmentType as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "appointmentStatus",
      header: t("appointments.appointmentStatus"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("appointments." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: AppointmentStatus as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "description",
      header: t("appointments.description"),
      filterVariant: "text",
      Cell: (props) => EntityLinkRenderer(props, "appointments"),
    },
    {
      accessorKey: "lead",
      header: t("appointments.lead"),
      ...TableRenderer(LeadLookup, "leads", ["lastName", "firstName"]),
    },
    {
      accessorKey: "businessUnit",
      header: t("appointments.businessUnit"),
      ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
    },
    {
      accessorKey: "createdOn",
      header: t("appointments.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "startDate",
      header: t("appointments.startDate"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "endDate",
      header: t("appointments.endDate"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
  ];
  return columns;
}
