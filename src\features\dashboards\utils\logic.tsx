import { type QueryParams } from "@/features/entity/api";
import { api, type ApiResponse } from "@/lib/api";
import { type Statistic } from "./types";
import { type Schemas } from "@/types";

export interface DateCondition {
  Part: string;
  Value: number;
}

export interface MetricConditions {
  Start: DateCondition[];
  End: DateCondition[];
}
export interface StatisticsParams {
  filter: string;
  metric: string;
  startDate: string;
  endDate: string;
}

export async function GetMetric<T>(query: StatisticsParams): Promise<T> {
  const { data } = await api.post<T, ApiResponse<T>>(
    `/api/Reports/GetMetric`,
    query,
  );
  return data;
}

// eslint-disable-next-line react-refresh/only-export-components
export const formatCardValue = (value: Statistic[] | undefined) => {
  if (!value) return "0";
  if (value.length === 0) return "0";
  return value[0]?.value.toString() ?? "0";
};

export async function GetBusinessUnitName(
  businessUnitId: string,
): Promise<Schemas["BusinessUnitRetrieveDto"]> {
  const { data } = await api.get<
    Schemas["BusinessUnitRetrieveDto"],
    ApiResponse<Schemas["BusinessUnitRetrieveDto"]>,
    QueryParams
  >(`/api/BusinessUnits/${businessUnitId}`);
  return data;
}
