import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { type Schemas } from "@/types";
import { ContactForm } from "../components/ContactForm";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { useState } from "react";
import { notifications } from "@mantine/notifications";

export function ContactCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Contact"],
    Schemas["ContactCreateDto"]
  >({ resourcePath: "/api/Contacts", queryKey: "contact" });
  const { t } = useTranslation("features");

  return (
    <ContactForm
      isCreate={true}
      title={t("contacts.createTitle")}
      onSubmit={(values) => {
        mutate(values, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/contacts");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/contacts/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
