import type React from "react";
import { Center, Loader, Text } from "@mantine/core";
import useReport from "../utils/useReport";
import { type MetricProps } from "../utils/types";
import { type Statistic } from "../utils/types";
import { type DashboardContentProps } from "./index";

export interface BaseReportProps extends DashboardContentProps {
  metrics: MetricProps[];
  renderContent: (
    data: Statistic[][],
    cardData: Statistic[][],
  ) => React.ReactNode;
}
const BaseReport: React.FC<BaseReportProps> = ({
  timePeriod,
  businessUnit,
  metrics,
  renderContent,
}) => {
  const { data, isLoading } = useReport({
    timePeriod: timePeriod,
    businessUnit: businessUnit,
    metrics,
  });

  if (isLoading) {
    return (
      <Center mt={200}>
        <Loader size={200} color="primary" type="dots" />
      </Center>
    );
  }

  if (!data?.length) {
    return (
      <Center mt={20}>
        <Text size="xl" fw={900}>
          No data available
        </Text>
      </Center>
    );
  }

  return renderContent(
    data?.map((items: Statistic[]) => {
      const itemsToReturn: Statistic[] = [];
      items.forEach((item) => {
        if (item.display !== "Total") {
          itemsToReturn.push(item);
        }
      });
      return itemsToReturn;
    }),
    data?.map((items: Statistic[]) => {
      const itemsToReturn: Statistic[] = [];
      items.forEach((item) => {
        itemsToReturn.push(item);
      });
      return itemsToReturn;
    }),
  );
};

export default BaseReport;
