import { GetMetric } from "./logic";
import { type MetricProps, type Statistic, formatMetrics } from "./types";
import { useQuery } from "react-query";
import { DateTime } from "luxon";

interface UseReportProps {
  timePeriod: string | null;
  businessUnit?: string;
  metrics: MetricProps[];
}

export default function useReport({
  timePeriod,
  metrics,
  businessUnit,
}: UseReportProps) {
  const queryKey = ["report", timePeriod, businessUnit, metrics];
  const today = DateTime.now();
  let startDate = "";
  let endDate = "";
  switch (timePeriod) {
    case "today":
      {
        startDate = today.startOf("day").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today.endOf("day").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "yesterday":
      {
        startDate = today
          .minus({ days: 1 })
          .startOf("day")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today
          .minus({ days: 1 })
          .endOf("day")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "this_week":
      {
        startDate = today.startOf("week").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today.endOf("week").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "last_week":
      {
        startDate = today
          .minus({ week: 1 })
          .startOf("week")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today
          .minus({ week: 1 })
          .endOf("week")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "this_month":
      {
        startDate = today.startOf("month").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today.endOf("month").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "last_month":
      {
        startDate = today
          .minus({ month: 1 })
          .startOf("month")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today
          .minus({ month: 1 })
          .endOf("month")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "quarterly":
      {
        startDate = today
          .startOf("quarter")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today.endOf("quarter").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "this_year":
      {
        startDate = today.startOf("year").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today.endOf("quarter").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;

    case "last_year":
      {
        startDate = today
          .minus({ year: 1 })
          .startOf("year")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
        endDate = today
          .minus({ year: 1 })
          .endOf("quarter")
          .toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      }
      break;
    case "custom": {
      // does not matter
      startDate = today.startOf("day").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      endDate = today.endOf("day").toFormat("yyyy-MM-dd HH:mm:ss.SSS");
      break;
    }
    default:
      console.log("Invalid time period");
  }

  const queryFn = async () => {
    if (timePeriod == null) return [];
    const queries = formatMetrics(metrics, startDate, endDate, businessUnit);

    const results = await Promise.all(
      queries.map((query) => {
        return GetMetric<Statistic[]>(query);
      }),
    );
    return results;
  };

  const { data, isLoading } = useQuery(queryKey, queryFn, {
    refetchInterval: 10000,
  });
  return { data, isLoading };
}
