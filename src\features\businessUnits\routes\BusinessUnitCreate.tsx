import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { BusinessUnitForm } from "../components/BusinessUnitForm";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { useState } from "react";
import { notifications } from "@mantine/notifications";

export function BusinessUnitCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["BusinessUnit"],
    Schemas["BusinessUnitCreateDto"]
  >({ resourcePath: "/api/BusinessUnits", queryKey: "businessUnit" });
  const { t } = useTranslation("features");

  return (
    <BusinessUnitForm
      isCreate={true}
      title={t("businessUnits.createTitle")}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/businessUnits");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/businessUnits/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
