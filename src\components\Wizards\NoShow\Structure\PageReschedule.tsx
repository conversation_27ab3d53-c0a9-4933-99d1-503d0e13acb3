import {
  Box,
  Text,
  Center,
  <PERSON>lex,
  <PERSON><PERSON>,
  Badge,
  Button,
  ScrollArea,
  Loader,
  Paper,
} from "@mantine/core";
import { IconCalendarCancel, IconCalendarMonth } from "@tabler/icons-react";
import {
  formatDateTime,
  generateFullDayTimes,
  generateTimeSlotsFrom,
  type PageName,
  timeStringToMinutes,
} from "../Common/Common";

import { type Schemas } from "@/types";
import TimeSlotSelector from "../../Common/Time/TimeSlotSelector";
import { DatePicker, type DateValue } from "@mantine/dates";
import { useState } from "react";
import { useEntityListQuery } from "@/features/entity/queries";
import {
  useEntityCreateMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { notifications } from "@mantine/notifications";
import WizardSelect from "../../Common/Select";
import { RescheduleReason } from "@/types/enums";
import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";
import { type PageProps } from "../../Common/Header/WizardHeader";

interface PageRescheduleProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
  appointment?: Schemas["AppointmentRetrieveDto"];
}

export default function PageReschedule({
  setPages,
  pages,
  appointment,
  lead,
}: PageRescheduleProps) {
  const { t } = useTranslation("features");
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const startDate = appointment?.startDate ?? new Date();
  const endDate = appointment?.endDate ?? new Date();
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Appointment"],
    Schemas["AppointmentPatchDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: appointment?.id ?? "",
    queryKey: "appointment",
  });
  const { mutate: createCall } = useEntityCreateMutation<
    Schemas["PhoneCall"],
    Schemas["PhoneCallCreateDto"]
  >({ resourcePath: "/api/PhoneCalls", queryKey: "phoneCall" });

  const [newStartDate, setNewStartDate] = useState<Date | null>(null);
  const [newEndDate, setNewEndDate] = useState<Date | null>(null);
  const queryCache = useQueryClient();
  const formattedStart = newStartDate
    ? formatDateTime(newStartDate)
    : { date: "", time: "" };
  const formattedEnd = newEndDate ? formatDateTime(newEndDate) : { time: "" };
  const [datePickerValue, setDatePickerValue] = useState<DateValue>(
    new Date(startDate),
  );
  const start = new Date(datePickerValue ?? "");
  start.setHours(0, 0, 0, 0);
  const end = new Date(datePickerValue ?? "");
  end.setHours(23, 59, 59, 999);
  const { data, isLoading: appointmentsLoading } = useEntityListQuery<
    Schemas["AppointmentRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Appointments",
    queryKey: `appointmentsRescheduleWizard`,
    params: {
      filter: `businessUnitId == ${appointment?.businessUnitId} && startDate >= ${start.toISOString().replace("Z", "")} && endDate <= ${end.toISOString().replace("Z", "")} && appointmentStatus != 3`,
      orderBy: "startDate",
      desc: false,
    },
  });
  const startDateObj = new Date(startDate);
  const datePickerObj = datePickerValue ? new Date(datePickerValue) : null;

  const times =
    !datePickerObj ||
    (datePickerObj.getMonth() === startDateObj.getMonth() &&
      datePickerObj.getDate() === startDateObj.getDate())
      ? generateTimeSlotsFrom(startDateObj)
      : generateFullDayTimes();

  if (appointmentsLoading) {
    return (
      <Center h={"80%"}>
        <Loader size={"xl"} />
      </Center>
    );
  }

  const onConfirmClick = () => {
    update(
      {
        startDate: newStartDate!.toISOString(),
        endDate: newEndDate!.toISOString(),
        rescheduledFromWizard: true,
        appointmentStatus: "Rescheduled",
        rescheduleReason: selectedItem as (typeof RescheduleReason)[number],
      },
      {
        onSuccess: () => {
          createCall(
            {
              leadId: lead?.id,
              phoneCallStatus: "Reached",
              phoneCallType: "PhoneCall",
              startDate: new Date().toISOString().replace("Z", ""),
              endDate: new Date().toISOString().replace("Z", ""),
            },
            {
              onSuccess: () => {
                void queryCache.invalidateQueries(
                  "lead_" + appointment?.leadId,
                );
                notifications.show({
                  color: "green",
                  title: "Appointment updated",
                  message: "Appointment updated",
                });
                setPages([...pages, "COMPLETED"]);
                setIsLoading(false);
              },
              onError: (error) => {
                setIsLoading(false);
                console.error(error);
              },
            },
          );
        },
        onError: (error) => {
          setIsLoading(false);
          console.error(error);
        },
      },
    );
  };
  const appointments = data?.data ?? []; // Compute a list of disabled times from appointments
  const disabledTimes = appointments.reduce((acc: string[], appointment) => {
    const start = new Date(appointment.startDate!);
    const end = new Date(appointment.endDate!);
    const startMinutes = start.getHours() * 60 + start.getMinutes();
    const endMinutes = end.getHours() * 60 + end.getMinutes();

    // Filter times that fall within the appointment's time range
    const timesInRange = times.filter((time) => {
      const minutes = timeStringToMinutes(time);
      return minutes >= startMinutes && minutes < endMinutes;
    });
    return [...acc, ...timesInRange];
  }, []);
  // Remove duplicates if appointments overlap
  const uniqueDisabledTimes = Array.from(new Set(disabledTimes));
  return (
    <Box>
      <Center>
        <Text fz={16} fw={600} c={"#282828"}>
          {t("wizards.RescheduleAppointment.Title")}
        </Text>
      </Center>
      <Center>
        <Text fz={10} fw={300} c={"#ADADAD"}>
          {t("wizards.RescheduleAppointment.Label")}
        </Text>
      </Center>
      <Center mt={16}>
        <Box w={"25vw"}>
          <WizardSelect
            optionValues={RescheduleReason}
            selectedItem={selectedItem}
            setSelectedItem={setSelectedItem}
            leftSectionIcon={<IconCalendarCancel width={16} height={16} />}
            buttonLabel={t("appointments.rescheduleReasonLabel")}
          />
        </Box>
      </Center>
      <Center mt={40} w={"100%"}>
        <Flex
          direction={{
            base: "column",
            xs: "column",
            sm: "column",
            md: "row",
            lg: "row",
            xl: "row",
          }}
          w={"100%"}
          mr={32}
          ml={32}
          mb={16}
          gap={0}
        >
          <Stack flex={1} gap={0}>
            <Flex>
              <Text fz={16} fw={600} c={"#282828"} mr={16}>
                {appointment?.lead?.fullName}
              </Text>
            </Flex>
            <Flex mt={4} gap={8}>
              <Badge fz={8} fw={400}>
                {appointment?.appointmentType}
              </Badge>
              <Badge fz={8} fw={400} color="#ADADAD">
                {appointment?.appointmentStatus}
              </Badge>
            </Flex>
            <Flex>
              <Box mr={16}>
                <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
                  {t("appointments.TimeLabel")}
                </Text>
                <Text fz={14} fw={500} c={"#282828"}>
                  {`${formatDateTime(new Date(startDate)).date} ${formatDateTime(new Date(startDate)).time} - ${formatDateTime(new Date(endDate)).time}`}
                </Text>
              </Box>
              <Box>
                <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
                  {t("appointments.NewTimeLabel")}
                </Text>
                <Text fz={14} fw={500} c={"#282828"}>
                  {`${formattedStart.date} ${formattedStart.time} - ${formattedEnd.time}`}
                </Text>
              </Box>
            </Flex>
            <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
              {t("appointments.BusinessUnitLabel")}
            </Text>
            <Text fz={14} fw={500} c={"#282828"}>
              {appointment?.businessUnit?.code}
            </Text>
            <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
              {t("appointments.PhoneLabel")}
            </Text>
            <Text fz={14} fw={500} c={"#282828"}>
              {appointment?.lead?.phone}
            </Text>
            <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
              {t("appointments.MobileLabel")}
            </Text>
            <Text fz={14} fw={500} c={"#282828"}>
              {appointment?.lead?.mobile}
            </Text>
          </Stack>

          <Paper
            flex={2}
            p={16}
            bd={"2px solid #F6F6F6"}
            style={{ borderRadius: 0 }}
          >
            <Text fz={16} fw={600} c={"#282828"}>
              {t("wizards.RescheduleAppointment.NewTimeTitle")}
            </Text>
            <Text fz={10} fw={400} c={"#ADADAD"}>
              {t("wizards.RescheduleAppointment.NewTimeLabel")}
            </Text>
            <Flex w={"100%"} direction={"row"} gap={0} justify={"left"}>
              <Box flex={1} ta={"center"}>
                <Center>
                  <DatePicker
                    size="md"
                    hideOutsideDates
                    minDate={new Date(startDate)}
                    value={datePickerValue}
                    onChange={(event: DateValue) => {
                      setDatePickerValue(event);
                    }}
                  />
                </Center>
              </Box>

              <ScrollArea h={"36vh"} flex={1} type="always" mt={8}>
                <Box w={"96%"}>
                  <TimeSlotSelector
                    times={times}
                    selectedDate={new Date(datePickerValue ?? "")}
                    disabledTimes={uniqueDisabledTimes}
                    onSelectTime={(startDate, endDate) => {
                      if (startDate instanceof Date) {
                        setNewStartDate(startDate);
                      }
                      if (endDate instanceof Date) {
                        setNewEndDate(endDate);
                      }
                    }}
                  />
                </Box>
              </ScrollArea>
            </Flex>
          </Paper>
          <Paper
            flex={1}
            p={16}
            style={{
              border: "2px solid #F6F6F6",
              borderLeft: "none",
              borderRadius: 0,
            }}
          >
            <Text fz={16} fw={600} c={"#282828"}>
              {t("wizards.RescheduleAppointment.AgendaTitle")}
            </Text>
            <Text fz={10} fw={400} c={"#ADADAD"}>
              {t("wizards.RescheduleAppointment.AgendaLabel")}
              {`${appointment?.businessUnit?.code}`}
            </Text>
            <Flex direction={"row"} justify={"left"}>
              <ScrollArea h={"32vh"} w={"100%"} type="always">
                {appointments.map((appointment) => (
                  <Box
                    key={appointment.id}
                    style={{ borderRadius: 8 }}
                    p={12}
                    m={8}
                    mr={16}
                    bg={"#F6F6F6"}
                  >
                    <Flex w={"100%"} justify={"space-between"}>
                      <Text fz={14} fw={400} c={"#282828"}>
                        {appointment.lead?.fullName}
                      </Text>
                      <Text fz={12} fw={400} c={"#ADADAD"}>
                        {appointment.appointmentType}
                      </Text>
                    </Flex>
                    <Flex w={"100%"} justify={"space-between"}>
                      <Text fz={14} fw={500} c={"#282828"}>
                        {`${new Date(appointment.startDate!).toLocaleTimeString(
                          [],
                          {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: false,
                          },
                        )} - ${new Date(
                          appointment.endDate!,
                        ).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                          hour12: false,
                        })}`}
                      </Text>
                      <Text fz={14} fw={500} c={"#282828"}>
                        {appointment?.businessUnit?.code}
                      </Text>
                    </Flex>
                  </Box>
                ))}
              </ScrollArea>
            </Flex>
          </Paper>
        </Flex>
      </Center>
      <Center mt={16}>
        <Button
          loading={isLoading}
          disabled={selectedItem == null}
          leftSection={<IconCalendarMonth width={20} height={20} />}
          fz={14}
          fw={400}
          style={{ borderRadius: 8 }}
          onClick={() => {
            setIsLoading(true);
            onConfirmClick();
          }}
        >
          {t("wizards.RescheduleAppointment.ConfirmButton")}
        </Button>
      </Center>
    </Box>
  );
}
