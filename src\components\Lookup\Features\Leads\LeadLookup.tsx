import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { t } from "i18next";

const ENTITY = "leads";
type ENTITY_TYPE = Schemas["LeadRetrieveDto"];

type LeadLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function LeadLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  ...props
}: LeadLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.fullName,
    initialId,
    initial,
    true,
  );

  const queryParams: QueryParams = {
    pageSize: 50,
    filter: "RecordState==0",
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["LeadRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/Leads",
      queryKey: "lead",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.fullName")}</Box>
      <Box>{t("common:lookup.email")}</Box>
      <Box>{t("common:lookup.type")}</Box>
    </React.Fragment>
  );
  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.fullName, searchTerm, `fullName_${entity.id}`),
        OptionRender(entity.email, searchTerm, `email_${entity.id}`),
        OptionRender(entity.type, searchTerm, `type_${entity.id}`),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.fullName!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
