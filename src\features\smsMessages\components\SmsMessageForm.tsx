import { useState } from "react";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  Box,
  Flex,
  ScrollArea,
  Textarea,
  TextInput,
  Title,
} from "@mantine/core";
import { MessageStatus } from "@/types/enums";
import { type ReactNode } from "react";

const smsMessageStatusEnum = z.enum(MessageStatus as [string]);

const formSchema = z.object({
  id: z.string(),
  body: z.string(),
  recipients: z.string(),
  externalId: z.string(),
  endDate: z.date().nullable(),
  smsMessageStatus: smsMessageStatusEnum.refine((value) => !!value),
});

export type FormSchema = z.infer<typeof formSchema>;

interface SmsMessageFormProps {
  onDelete?: VoidFunction;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  isCreate: boolean;
  showBackButton?: boolean;
  closeModal?: () => void;
  isModal?: boolean;
}

export function SmsMessageForm({
  initialValues,
  isCreate,
  isModal,
}: SmsMessageFormProps) {
  const { t } = useTranslation("features");
  const [editableForm] = useState(isCreate ? true : false);
  const [formKey] = useState(0);
  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      body: initialValues?.body ?? "",
      recipients: initialValues?.recipients ?? "",
      externalId: initialValues?.externalId ?? "",
      endDate: initialValues?.endDate ?? null,
      smsMessageStatus: initialValues?.smsMessageStatus ?? "",
    },
    initialDirty: { startDate: true, endDate: true },
    validate: zodResolver(formSchema),
  });

  return (
    <form key={formKey}>
      <Flex justify="space-between" align="center" direction="row" mt={6}>
        {isCreate ? (
          <Title order={3} mt={20} mb={20}>
            {t("smsMessages.createSmsMessage")}
          </Title>
        ) : (
          <Title order={3} mt={20} mb={20}>
            {t("smsMessages.sms")}
          </Title>
        )}
      </Flex>
      <ScrollArea
        scrollbarSize={4}
        type="hover"
        mah={
          document.location.pathname.includes("app/smsMessages")
            ? "74vh"
            : isModal
              ? "74vh"
              : "56vh"
        }
      >
        <Box
          mr={10}
          style={!editableForm ? { pointerEvents: "none", opacity: "0.6" } : {}}
        >
          <TextInput
            mt="sm"
            label={t("smsMessages.subject")}
            {...form.getInputProps("subject")}
          />
          <TextInput
            mt="sm"
            label={t("smsMessages.recipients")}
            {...form.getInputProps("recipients")}
          />
          <TextInput
            mt="sm"
            label={t("smsMessages.externalId")}
            {...form.getInputProps("externalId")}
          />
          <Textarea
            mt="sm"
            label={t("smsMessages.body")}
            {...form.getInputProps("body")}
            minRows={4}
            autosize
          />
        </Box>
      </ScrollArea>
      <Flex
        mt={10}
        mr={10}
        gap="xs"
        justify="center"
        align="center"
        direction="row"
        wrap="nowrap"
      ></Flex>
    </form>
  );
}
