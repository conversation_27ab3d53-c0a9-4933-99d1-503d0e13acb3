import { useMemo, useCallback } from "react";
import { useSearchParams } from "react-router-dom";
import {
  type MRT_ColumnFiltersState,
  type MRT_SortingState,
} from "mantine-react-table";
import { type ViewOption } from "../types";
import { GetParamsFromURL } from "@/utils/urlParamsUtils";

interface UseTableUrlSyncArgs {
  viewOptions?: ViewOption[];
  defaultViewOption?: ViewOption;
  initialSorting?: MRT_SortingState;
  isListView: boolean; // Only sync URL on list views
}

export function useTableUrlSync({
  viewOptions = [],
  defaultViewOption,
  initialSorting = [],
  isListView,
}: UseTableUrlSyncArgs) {
  const [searchParams, setSearchParams] = useSearchParams();

  const initialStateFromUrl = useMemo(() => {
    return GetParamsFromURL(
      searchParams,
      viewOptions,
      defaultViewOption,
      initialSorting,
    );
  }, [searchParams, viewOptions, defaultViewOption, initialSorting]); // Recalculate only if params or options change

  const updateUrlParams = useCallback(
    (
      filters: MRT_ColumnFiltersState,
      sorting: MRT_SortingState,
      activeViewValue: string | undefined,
    ) => {
      if (!isListView) return; // Don't update URL if not on the main list view

      setSearchParams(
        (prev) => {
          prev.set("filters", JSON.stringify(filters ?? []));
          prev.set("sorting", JSON.stringify(sorting ?? []));
          if (activeViewValue) {
            prev.set("activeView", activeViewValue);
          } else {
            prev.delete("activeView");
          }
          return prev;
        },
        { replace: true }, // Use replace to avoid polluting browser history
      );
    },
    [setSearchParams, isListView],
  );

  return {
    initialStateFromUrl,
    updateUrlParams,
  };
}
