import { useState } from "react";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  Box,
  Flex,
  ScrollArea,
  TextInput,
  Title,
  Textarea,
  Grid,
  Switch,
  Paper,
} from "@mantine/core";

const formSchema = z.object({
  id: z.string(),
  entryId: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string(),
  mobile: z.string(),
  comments: z.string(),
  webformTitle: z.string(),
  webformDetails: z.string(),
  leadSource: z.string(),
  productType: z.string(),
  transportation: z.string(),
  startWithin: z.string(),
  price: z.string(),
  step: z.string(),
  unitSize: z.string(),
  preferredLanguage: z.string(),
  createdOn: z.date().nullable(),
  transportationRequest: z.boolean(),
  utm_campaign: z.string(),
  utm_content: z.string(),
  utm_medium: z.string(),
  utm_source: z.string(),
  utm_term: z.string(),
  gclid: z.string(),
  clientID: z.string(),
  trackingId: z.string(),
  referrer: z.string(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface WebformEntryFormProps {
  initialValues?: FormSchema;
  isModal?: boolean;
  closeModal?: () => void;
}

export function WebformEntryForm({ initialValues }: WebformEntryFormProps) {
  const { t } = useTranslation("features");
  const [formKey] = useState(0);
  const form = useForm<FormSchema>({
    initialValues,
    validate: zodResolver(formSchema),
  });
  const isFormDisabled = form.getInputProps("recordState").value === "Inactive";
  return (
    <form key={formKey}>
      <Flex justify="space-between" align="center" direction="row">
        <Title
          order={4}
        >{`${t("webformEntries.webformEntry")} - ${form.getValues().webformTitle}`}</Title>
      </Flex>
      <ScrollArea scrollbarSize={4} type="hover" mah={"74vh"}>
        <Box
          mr={10}
          style={
            isFormDisabled ? { pointerEvents: "none", opacity: "0.6" } : {}
          }
        >
          <Grid m={16}>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Paper shadow="xs" p="lg">
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.entryId")}
                  {...form.getInputProps("entryId")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.firstName")}
                  {...form.getInputProps("firstName")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.lastName")}
                  {...form.getInputProps("lastName")}
                />

                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.email")}
                  {...form.getInputProps("email")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.mobile")}
                  {...form.getInputProps("mobile")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.webformTitle")}
                  {...form.getInputProps("webformTitle")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.webformDetails")}
                  {...form.getInputProps("webformDetails")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.leadSource")}
                  {...form.getInputProps("leadSource")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.productType")}
                  {...form.getInputProps("productType")}
                />
              </Paper>
            </Grid.Col>
            <Grid.Col span={{ base: 6, md: 4 }}>
              <Paper shadow="xs" p="lg">
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.transportation")}
                  {...form.getInputProps("transportation")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.startWithin")}
                  {...form.getInputProps("startWithin")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.price")}
                  {...form.getInputProps("price")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.step")}
                  {...form.getInputProps("step")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.unitSize")}
                  {...form.getInputProps("unitSize")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.preferredLanguage")}
                  {...form.getInputProps("preferredLanguage")}
                />
                <Textarea
                  mt="sm"
                  disabled
                  label={t("webformEntries.comments")}
                  {...form.getInputProps("comments")}
                  minRows={4}
                  autosize
                />
                <Flex>
                  <Switch
                    mt="sm"
                    disabled
                    labelPosition="left"
                    checked={form.getValues().transportationRequest}
                    label={t("webformEntries.transportationRequest")}
                    {...form.getInputProps("transportationRequest")}
                  />
                </Flex>
              </Paper>
            </Grid.Col>
            <Grid.Col span={{ base: 6, md: 4 }}>
              <Paper shadow="xs" p="lg">
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.utm_campaign")}
                  {...form.getInputProps("utm_campaign")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.utm_content")}
                  {...form.getInputProps("utm_content")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.utm_medium")}
                  {...form.getInputProps("utm_medium")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.utm_source")}
                  {...form.getInputProps("utm_source")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.utm_term")}
                  {...form.getInputProps("utm_term")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.gclid")}
                  {...form.getInputProps("gclid")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.clientID")}
                  {...form.getInputProps("clientID")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.trackingId")}
                  {...form.getInputProps("trackingId")}
                />
                <TextInput
                  mt="sm"
                  disabled
                  label={t("webformEntries.referrer")}
                  {...form.getInputProps("referrer")}
                />
              </Paper>
            </Grid.Col>
          </Grid>
        </Box>
      </ScrollArea>
    </form>
  );
}
