import {
  Text,
  Center,
  Flex,
  Button,
  Textarea,
  useCombobox,
} from "@mantine/core";
import { IconCalendarCancel, IconCalendarMonth } from "@tabler/icons-react";

import classes from "../NoShow.module.css";
import { useState } from "react";
import { AppointmentCancelReason } from "@/types/enums";
import {
  useEntityCreateMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { type Schemas } from "@/types";
import { notifications } from "@mantine/notifications";
import WizardSelect from "../../Common/Select";
import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../Common/Common";

interface PageRescheduleProps extends PageProps<PageName> {
  appointment?: Schemas["AppointmentRetrieveDto"];
  lead?: Schemas["LeadRetrieveDto"];
}

export default function PageCancel({
  appointment,
  lead,
  pages,
  setPages,
}: PageRescheduleProps) {
  const { t } = useTranslation("features");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [notes, setNotes] = useState("");
  const queryCache = useQueryClient();

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const { mutate: createCall } = useEntityCreateMutation<
    Schemas["PhoneCall"],
    Schemas["PhoneCallCreateDto"]
  >({ resourcePath: "/api/PhoneCalls", queryKey: "phoneCall" });

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Appointment"],
    Schemas["AppointmentPatchDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: appointment?.id ?? "",
    queryKey: "appointment",
  });

  const onConfirmClick = () => {
    update(
      {
        appointmentStatus: "NoShow",
        noShowNotes: notes,
        cancelReason: selectedItem as (typeof AppointmentCancelReason)[number],
      },
      {
        onSuccess: () => {
          void queryCache.invalidateQueries("lead_" + appointment?.leadId);
          createCall(
            {
              leadId: lead?.id,
              phoneCallStatus: "Reached",
              phoneCallType: "PhoneCall",
              startDate: new Date().toISOString().replace("Z", ""),
              endDate: new Date().toISOString().replace("Z", ""),
            },
            {
              onSuccess: () => {
                notifications.show({
                  color: "green",
                  title: "Appointment updated",
                  message: "Appointment updated",
                });
                void queryCache.invalidateQueries(
                  "lead_" + appointment?.leadId,
                );
                setPages([...pages, "COMPLETED"]);
                setIsLoading(false);
              },
              onError: (error) => {
                setIsLoading(false);
                console.error(error);
              },
            },
          );
        },
        onError: (error) => {
          setIsLoading(false);
          console.error(error);
        },
      },
    );
  };
  return (
    <Center>
      <Flex
        direction={"column"}
        justify={"center"}
        align={"center"}
        w={{ base: "100%", md: "30%" }}
      >
        <Center>
          <Text fz={16} fw={600} c={"#282828"}>
            {t(`wizards.CancelAppointment.Title`)}
          </Text>
        </Center>
        <Center>
          <Text className={classes.listItemLabel}>
            {t(`wizards.CancelAppointment.Label`)}
          </Text>
        </Center>
        <Center mt={16} w={"100%"}>
          <WizardSelect
            optionValues={AppointmentCancelReason}
            selectedItem={selectedItem}
            setSelectedItem={setSelectedItem}
            leftSectionIcon={<IconCalendarCancel width={16} height={16} />}
            buttonLabel={t("appointments.cancelReasonLabel")}
          />
        </Center>
        <Center mt={16} w={"100%"}>
          <Textarea
            value={notes}
            onChange={(event) => setNotes(event.currentTarget.value)}
            w={"100%"}
            mt={0}
            minRows={4}
            autosize
            placeholder={t("appointments.cancelNotesPlaceholder")}
            classNames={{ input: classes.textareaWizardInput }}
          />
        </Center>
        <Center mt={56} w={"100%"}>
          <Button
            w={"100%"}
            disabled={selectedItem == null}
            loading={isLoading}
            leftSection={<IconCalendarMonth width={20} height={20} />}
            fz={14}
            fw={400}
            style={{ borderRadius: 8 }}
            onClick={() => {
              setIsLoading(true);
              onConfirmClick();
            }}
          >
            {t(`wizards.CancelAppointment.ConfirmButton`)}
          </Button>
        </Center>
      </Flex>
    </Center>
  );
}
