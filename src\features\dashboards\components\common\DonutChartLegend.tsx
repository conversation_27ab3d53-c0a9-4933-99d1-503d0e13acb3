import type React from "react";
import { Text, Box, rem, Flex, ScrollArea, Center } from "@mantine/core";
import { type DonutChartCell } from "@mantine/charts";
import classes from "./report.module.css";

interface LegendTableProps {
  data: DonutChartCell[];
  height: number;
}

const LegendTable: React.FC<LegendTableProps> = ({ data, height }) => {
  if (!data || data.length === 0) {
    return <Text size="xs">No data available</Text>;
  }
  return (
    <Box h={height}>
      <Text fw={400} mr={14} mb={4} ta={"right"} size="xs">
        Edited by
      </Text>
      <ScrollArea
        mr={4}
        h={"60%"}
        type="always"
        classNames={classes}
        scrollbarSize={6}
      >
        {data.map((item, index) => (
          <Flex h={30} mr={8} direction={"row"} key={"legend-" + index}>
            <Box h={30} flex={10} mr={10}>
              <Flex direction={"row"} gap={0} h={30}>
                <Center flex={10}>
                  <Text size="md" fw={500}>
                    {item.name}
                  </Text>
                </Center>
                <Center flex={2} ta={"right"}>
                  <Text size="md" fw={500}>
                    {item.value}
                  </Text>
                </Center>
              </Flex>
            </Box>
            <Center flex={1}>
              <Box
                style={{
                  width: rem(18),
                  height: rem(18),
                  backgroundColor: item.color,
                  borderRadius: "50%",
                }}
              />
            </Center>
          </Flex>
        ))}
      </ScrollArea>
    </Box>
  );
};

export default LegendTable;
