import { useTranslation } from "react-i18next";
import {
  type MRT_Cell,
  type MRT_Column,
  type MRT_Row,
  type MRT_TableInstance,
  type MRT_RowData,
} from "mantine-react-table";
import { type ReactNode, type RefObject } from "react";

export function <PERSON><PERSON>anRenderer(props: {
  cell: MRT_Cell<MRT_RowData, unknown>;
  column: MRT_Column<MRT_RowData, unknown>;
  renderedCellValue: ReactNode | number | string;
  renderedColumnIndex?: number;
  renderedRowIndex?: number;
  row: MRT_Row<MRT_RowData>;
  rowRef?: RefObject<HTMLTableRowElement>;
  table: MRT_TableInstance<MRT_RowData>;
}) {
  const { t } = useTranslation("features");
  const cellValue = props.cell.getValue<boolean>();

  return cellValue ? t("common.yes") : t("common.no");
}
