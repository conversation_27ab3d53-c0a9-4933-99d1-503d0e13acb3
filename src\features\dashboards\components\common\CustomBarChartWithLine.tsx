import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  ComposedChart,
  Line,
  type TooltipProps,
} from "recharts";
import { type Statistic } from "../../utils/types";

import { Box, Flex, ScrollArea, Text, Container, Paper } from "@mantine/core";
import { useCallback, useState } from "react";
import { IconArrowsMaximize, IconArrowsMinimize } from "@tabler/icons-react";
import classes from "./report.module.css";
import {
  type NameType,
  type ValueType,
} from "recharts/types/component/DefaultTooltipContent";

interface CustomBarChartVerticalProps {
  data: Statistic[] | undefined;
  title: string;
  marginTop?: number;
  height?: number;
  width?: string;
  percentage?: boolean;
  lineLabel?: string;
  barLabel?: string;
}
interface CustomizedTickPayload {
  value: string | number;
  // Include other properties if needed
}

interface CustomizedAxisTickProps {
  x: number;
  y: number;
  stroke: string;
  payload: CustomizedTickPayload;
}

interface CustomTooltipProps extends TooltipProps<ValueType, NameType> {
  lineLabel: string;
  barLabel: string;
}

export default function CustomBarChartWithLine({
  data,
  title,
  marginTop = 10,
  height = 270,
  width = "100%",
  lineLabel = "Label Undefined",
  barLabel = "Label Undefined",
}: CustomBarChartVerticalProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const expandedStyle = {
    position: "fixed" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: "white",
    padding: "20px",
    overflow: "hidden" as const,
  };

  const normalStyle = {
    border: "2px solid #e0e0e0",
    borderRadius: 4,
    height: height + 20,
    width,
  };

  const calculateBarchartWidth = (numberOfItem = 9) => {
    if (numberOfItem > 9) return numberOfItem * 60;
    return 900;
  };

  const CustomTooltip = ({
    active,
    payload,
    label,
    barLabel,
    lineLabel,
  }: CustomTooltipProps) => {
    if (active) {
      return (
        <Paper px="md" py="sm" withBorder shadow="md" radius="md">
          <Text fz="h6" fw={700}>
            {label}
          </Text>
          {payload!.map((item, index) => (
            <Text key={`${item.name}_${label}_${index}`} fz="h6" fw={700}>
              {index == 0 ? barLabel : lineLabel}: {item.value}
            </Text>
          ))}
        </Paper>
      );
    }

    return null;
  };

  return (
    <Box
      style={isExpanded == true ? expandedStyle : normalStyle}
      mt={isExpanded ? 0 : marginTop}
      w={width}
      h={isExpanded ? "100%" : height}
    >
      <Flex dir="row" mb={10}>
        <Text fw={600} ml={4} flex={3} ta={"left"} size="sm">
          {title}
        </Text>
        <Box
          ta="right"
          mr={4}
          style={{ cursor: "pointer" }}
          onClick={toggleExpand}
        >
          {isExpanded ? (
            <IconArrowsMinimize size={14} />
          ) : (
            <IconArrowsMaximize size={14} />
          )}
        </Box>
      </Flex>

      <ScrollArea
        scrollbars={"x"}
        classNames={classes}
        scrollbarSize={6}
        type="always"
        h={isExpanded ? "100%" : height - 40}
      >
        <Container w={"100%"} h={isExpanded ? "100%" : height} mr={10} ml={-40}>
          <ComposedChart
            data={data}
            barSize={40}
            barCategoryGap={"5%"}
            barGap={2}
            width={calculateBarchartWidth(data?.length ?? 1)}
            height={height}
            margin={{ left: 20 }}
          >
            <XAxis
              dataKey={"display"}
              interval={0}
              angle={-35}
              height={160}
              axisLine={false}
              tickLine={false}
              tick={({ x, y, payload }: CustomizedAxisTickProps) => {
                return (
                  <g transform={`translate(${x},${y})`}>
                    <text
                      style={{ textAlign: "left" }}
                      x={0}
                      y={0}
                      dy={16}
                      fontSize={10}
                      fontWeight={600}
                      textAnchor="end"
                      fill="#666"
                      transform="rotate(-35)"
                    >
                      {payload.value}
                    </text>
                  </g>
                );
              }}
            />
            <YAxis axisLine={false} tickLine={false} minTickGap={1} />
            <Tooltip
              content={
                <CustomTooltip barLabel={barLabel} lineLabel={lineLabel} />
              }
              cursor={{ fill: "transparent" }}
            />
            <CartesianGrid stroke="#f5f5f5" />
            <Bar cursor={"pointer"} dataKey={"value"} fill="#228be6" />
            <Line type="monotone" dataKey={"secondValue"} stroke="red" />
          </ComposedChart>
        </Container>
      </ScrollArea>
    </Box>
  );
}
