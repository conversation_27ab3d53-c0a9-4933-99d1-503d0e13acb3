import { <PERSON>, Button, Flex, Text } from "@mantine/core";
import {
  IconCalendar<PERSON>onth,
  IconFileAlert,
  IconUserPlus,
} from "@tabler/icons-react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";

interface HomeHeaderProps {
  username: string;
}

export function HomeHeader({ username }: HomeHeaderProps) {
  const navigate = useNavigate();
  const { t } = useTranslation("features");
  const { t: qt } = useTranslation("quotes");

  const quotesArray: string[] = qt("homeQuote", { returnObjects: true });

  const todayIndex = useMemo(() => {
    const today = new Date();
    const dateSeed = today.getFullYear() + today.getMonth() + today.getDate();
    return dateSeed % quotesArray.length;
  }, [quotesArray.length]);

  return (
    <Box ta={"center"} p={16} pt={64} pb={64}>
      <Text fz={16} fw={600}>
        {t(`home.hello`)} {`${username}`}
      </Text>
      <Text fz={12} fw={400} c={"neutral.6"}>
        {t(`home.welcome`)}
      </Text>
      <Text mt={16} fw={500} fz={12} fs={"italic"} c="primary">
        {quotesArray[todayIndex]}
      </Text>
      <Flex
        mt={24}
        gap="md"
        justify="center"
        align="center"
        direction="row"
        wrap="wrap"
      >
        <Button
          variant="light"
          leftSection={<IconCalendarMonth width={20} height={20} />}
          onClick={() => {
            void navigate("/app/appointments");
          }}
        >
          {t(`home.scheduleAppointment`)}
        </Button>
        <Button
          variant="light"
          leftSection={<IconUserPlus width={20} height={20} />}
          onClick={() => {
            void navigate("/app/leads/create");
          }}
        >
          {t(`home.newLead`)}
        </Button>
        <Button
          variant="light"
          leftSection={<IconFileAlert width={20} height={20} />}
          onClick={() => {
            void navigate("/app/cases/create");
          }}
        >
          {t(`home.newCase`)}
        </Button>
      </Flex>
    </Box>
  );
}
