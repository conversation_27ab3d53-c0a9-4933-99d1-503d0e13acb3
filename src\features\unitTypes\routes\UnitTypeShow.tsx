import { <PERSON>Loader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { type SetNonNullable } from "type-fest";
import { UnitTypeForm } from "../components/UnitTypeForm";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function UnitTypeShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["UnitType"],
    Schemas["UnitTypeCreateDto"]
  >({
    resourcePath: "/api/UnitTypes/{id}",
    resourceId: id!,
    queryKey: "unitType",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/UnitTypes/{id}",
    resourceId: id!,
    queryKey: "unitType",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["UnitType"]>({
    resourcePath: "/api/UnitTypes/{id}",
    resourceId: id!,
    queryKey: "unitType",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("unitType_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <UnitTypeForm
      isCreate={false}
      title={t("unitTypes.showTitle", { id })}
      contextRecordId={id}
      initialValues={
        filterFalsyValues(data) as Required<SetNonNullable<Schemas["UnitType"]>>
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/unitTypes");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/unitTypes");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("unitTypes.delete", { id })}
            modalContent={t("unitTypes.deleteConfirmation", { id })}
            confirmLabel={t("unitTypes.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/unitTypes");
              }
            }}
          />
        </Group>
      }
    />
  );
}
