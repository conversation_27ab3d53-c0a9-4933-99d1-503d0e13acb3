import {
  type MRT_Column,
  type MRT_Row,
  type MRT_TableInstance,
  type MRT_Cell,
  type MRT_RowData,
} from "mantine-react-table";
import { type ReactNode, type RefObject } from "react";
import { config } from "@/config";

export function Currency<PERSON>enderer(props: {
  cell: MRT_Cell<MRT_RowData, unknown>;
  column: MRT_Column<MRT_RowData, unknown>;
  renderedCellValue: ReactNode | number | string;
  renderedColumnIndex?: number;
  renderedRowIndex?: number;
  row: MRT_Row<MRT_RowData>;
  rowRef?: RefObject<HTMLTableRowElement>;
  table: MRT_TableInstance<MRT_RowData>;
}) {
  const value = props.cell.getValue<number>();

  if (!value) {
    return "";
  }

  const formattedValue = new Intl.NumberFormat(config.CURRENCY.locale, {
    style: "currency",
    currency: config.CURRENCY.code,
    minimumFractionDigits: config.CURRENCY.decimals,
    maximumFractionDigits: config.CURRENCY.decimals,
  }).format(value);

  return props.cell.getValue<number> !== null ? `${formattedValue}` : "";
}
