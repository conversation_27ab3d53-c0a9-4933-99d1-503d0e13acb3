import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { RentableItemCreate } from "./RentableItemCreate";
import { RentableItemsColumns } from "../table/RentableItemsColumns";

const PATH = "RentableItems";
export function RentableItemListInner({
  resourcePath,
  createPath,
  parentEntityId,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["RentableItemRetrieveDto"],
        Schemas["RentableItemRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="rentableItem"
        entityPath="RentableItems"
        title={t("rentableItems.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton
              to={createPath}
              FormComponent={parentEntityId ? RentableItemCreate : undefined}
            />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={RentableItemsColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function RentableItemList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <RentableItemListInner
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
      />
    </ListCommandsProvider>
  );
}
