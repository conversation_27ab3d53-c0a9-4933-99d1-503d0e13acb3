import { Route, Routes } from "react-router-dom";
import { AppointmentTypeTemplateMappingList } from "./AppointmentTypeTemplateMappingList";
import { AppointmentTypeTemplateMappingShow } from "./AppointmentTypeTemplateMappingShow";
import { AppointmentTypeTemplateMappingCreate } from "./AppointmentTypeTemplateMappingCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<AppointmentTypeTemplateMappingList />} />
      <Route path=":id" element={<AppointmentTypeTemplateMappingShow />} />
      <Route path="create" element={<AppointmentTypeTemplateMappingCreate />} />
    </Routes>
  );
}
