import type React from "react";
import { Grid } from "@mantine/core";
import HorizontalBarChart from "../common/HorizontalBarChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import CardChart from "../common/CardChart";

const metrics: MetricProps[] = [
  { metric: "AllAppointments" },
  { metric: "BookedAppointments" },
  { metric: "EditedLeads" },
  { metric: "TestMetric" },
];

const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={12}>
      <Grid mt={20}>
        <Grid.Col span={2}>
          <CardChart value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart value="0" title={"title"} />
        </Grid.Col>
      </Grid>
    </Grid.Col>
    <Grid.Col span={12}>
      <Grid>
        <Grid.Col span={2}>
          <HorizontalBarChart
            marginTop={0}
            height="620"
            data={data[0]}
            title={"title"}
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <HorizontalBarChart
            marginTop={0}
            height="620"
            data={data[0]}
            title={"title"}
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <HorizontalBarChart
            marginTop={0}
            height="620"
            data={data[0]}
            title={"title"}
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <HorizontalBarChart
            marginTop={0}
            height="620"
            data={data[0]}
            title={"title"}
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <HorizontalBarChart
            marginTop={0}
            height="620"
            data={data[0]}
            title={"title"}
          />
        </Grid.Col>
      </Grid>
    </Grid.Col>
  </Grid>
);

const AfgelegdeOpportunities: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default AfgelegdeOpportunities;
