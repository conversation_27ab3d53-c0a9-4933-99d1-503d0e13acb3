import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { HtmlTemplateForm } from "../components/HtmlTemplateForm";
import { type SetNonNullable } from "type-fest";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function HtmlTemplateShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["HtmlTemplate"],
    Schemas["HtmlTemplateCreateDto"]
  >({
    resourcePath: "/api/HtmlTemplates/{id}",
    resourceId: id!,
    queryKey: "htmlTemplate",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/HtmlTemplates/{id}",
    resourceId: id!,
    queryKey: "htmlTemplate",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["HtmlTemplate"]>({
    resourcePath: "/api/HtmlTemplates/{id}",
    resourceId: id!,
    queryKey: "htmlTemplate",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("htmlTemplate_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <HtmlTemplateForm
      isCreate={false}
      title={t("htmlTemplates.showTitle", { id })}
      initialValues={
        filterFalsyValues(data) as Required<
          SetNonNullable<Schemas["HtmlTemplate"]>
        >
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/htmlTemplates");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/htmlTemplates");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("htmlTemplates.delete", { id })}
            modalContent={t("htmlTemplates.deleteConfirmation", { id })}
            confirmLabel={t("htmlTemplates.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/htmlTemplates");
              }
            }}
          />
        </Group>
      }
    />
  );
}
