import { type Schemas } from "@/types";

export function addLeadToRecent(newLead: Schemas["LeadRetrieveDto"]): void {
  const storedLeads = localStorage.getItem("recentVisitedLeads");
  let leads: Schemas["LeadRetrieveDto"][] = [];

  if (storedLeads) {
    try {
      const parsed = JSON.parse(storedLeads);
      if (Array.isArray(parsed)) {
        leads = parsed as Schemas["LeadRetrieveDto"][];
      }
    } catch (error) {
      leads = [];
    }
  }
  leads = leads.filter((lead) => lead.id !== newLead.id);
  leads.unshift(newLead);
  if (leads.length > 10) {
    leads = leads.slice(0, 10);
  }
  localStorage.setItem("recentVisitedLeads", JSON.stringify(leads));
}

export function readRecentLeads(): Schemas["LeadRetrieveDto"][] {
  const storedLeads = localStorage.getItem("recentVisitedLeads");

  if (storedLeads) {
    try {
      const parsed = JSON.parse(storedLeads);
      if (Array.isArray(parsed)) {
        return parsed as Schemas["LeadRetrieveDto"][];
      }
    } catch (error) {
      return [];
    }
  }

  return [];
}
