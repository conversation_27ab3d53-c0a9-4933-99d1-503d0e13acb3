import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { type Schemas } from "@/types";
import { Box, Flex, ScrollArea, Tooltip } from "@mantine/core";
import { DateTime } from "luxon";
interface EventProps {
  event: Schemas["Appointment"];
  calendarView: string;
}
export function PhantomEvent({ event }: EventProps) {
  const { dateFormat } = useSettingsContext();
  const duration =
    new Date(event.endDate ?? "").getTime() -
    new Date(event.startDate ?? "").getTime();
  const hours = Math.floor(duration / (1000 * 60 * 60));
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
  const durationString = `${hours}H:${minutes}M`;
  return (
    <Tooltip.Floating
      multiline
      position="top-start"
      color={"#468ecb"}
      w={"14%"}
      label={
        <>
          {"Phantom Event"}
          <br />
          {`Start: ${DateTime.fromJSDate(new Date(event.startDate ?? "")).toFormat(dateFormat + " HH:mm")}`}
          <br />
          {`End: ${DateTime.fromJSDate(new Date(event.endDate ?? "")).toFormat(dateFormat + " HH:mm")}`}
          <br />
          {`Duration: ${durationString}`}
        </>
      }
    >
      <Box
        w="100%"
        h="100%"
        style={{
          opacity: 1,
          backgroundColor: "rgba(65, 81, 259, 0.2)",
          border: "2px solid red",
        }}
      >
        <ScrollArea h="100%" w="100%" scrollbars="y" type="always">
          <Flex
            justify="center"
            w="100%"
            h="100%"
            direction="column"
            wrap="nowrap"
          >
            <Box w="100%" h="50%" p={5}>
              <>
                <b>
                  {`${DateTime.fromJSDate(new Date(event.startDate ?? "")).toFormat("HH:mm")}`}
                  {" - "}
                  {`${DateTime.fromJSDate(new Date(event.endDate ?? "")).toFormat("HH:mm")}`}
                </b>
              </>
            </Box>
          </Flex>
        </ScrollArea>
      </Box>
    </Tooltip.Floating>
  );
}
