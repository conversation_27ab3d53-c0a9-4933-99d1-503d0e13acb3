import { useDisclosure } from "@mantine/hooks";
import { Modal, Button, Text, LoadingOverlay, Switch } from "@mantine/core";
import { modals } from "@mantine/modals";
import { useTranslation } from "react-i18next";
import { IconCalendarCheck } from "@tabler/icons-react";
import { type PathKeys, type Schemas } from "@/types";
import {
  useEntityUpdateMutation,
  useEntityCreateMutation,
} from "@/features/entity/mutations";
import { useParams } from "react-router-dom";
import { notifications } from "@mantine/notifications";
import { DatePickerInput } from "@mantine/dates";
import { useState, useEffect } from "react";
import { EntityLayout } from "@/features/entity";
import TableRenderer from "@/components/Table/renderers/TableRenderer";
import { formatDateTimeForBackend } from "@/utils/date";
import { useEntityListQuery } from "@/features/entity/queries";
import { useQueryClient } from "react-query";
import { addDays, format } from "date-fns";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { UnitLookup } from "@/components/Lookup/Features/Units/UnitLookupField";
import { ReservationLookup } from "@/components/Lookup/Features/Reservations/ReservationLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

export function ReservationButton() {
  const { dateFormat } = useSettingsContext();
  const [opened, { open, close }] = useDisclosure(false);
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [start, setStart] = useState<Date | null>(null);
  const [end, setEnd] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);
  const [sendEmail, setSendEmail] = useState(true);
  const queryCache = useQueryClient();

  const { data: reservations } = useEntityListQuery<
    Schemas["ReservationRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Reservations",
    queryKey: `leadReservations`,
    params: {
      filter: `leadId == ${id} && (reservationStatus == Active || reservationStatus == Extended)`,
    },
  });

  const { data: advisedUnits, isLoading } = useEntityListQuery<
    Schemas["ReservationRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/AdvisedUnits",
    queryKey: `advisedUnits`,
    params: {
      filter: `leadId == ${id}`,
    },
  });

  const { mutate: createReservation } = useEntityCreateMutation<
    Schemas["Reservation"],
    Schemas["ReservationCreateDto"]
  >({
    resourcePath: "/api/Reservations",
    queryKey: "reservation",
  });
  const { mutate: updateReservation } = useEntityUpdateMutation<
    Schemas["Reservation"],
    Schemas["ReservationPatchDto"]
  >({
    resourcePath: "/api/Reservations/{id}",
    resourceId: reservations?.data?.[0]?.id ?? "",
    queryKey: "reservation",
  });
  const reservedUntil = reservations?.data?.[0]?.reservedUntil ?? null;
  const reservationStart = reservations?.data?.[0]?.start ?? null;

  const reservationStartFormatted = reservationStart
    ? format(new Date(reservationStart), dateFormat)
    : null;
  const hasActiveReservation = reservations?.totalCount
    ? reservations.totalCount > 0
    : false;

  const hasAdvisedUnits = advisedUnits?.totalCount
    ? advisedUnits.totalCount > 0
    : false;

  useEffect(() => {
    if (!hasActiveReservation) {
      const today = new Date();
      setStart(today);
      setEnd(addDays(today, 14));
    } else if (reservedUntil) {
      setStart(new Date(reservationStart!));
      setEnd(addDays(new Date(reservedUntil), 1));
    }
  }, [hasActiveReservation, reservedUntil, reservationStart]);
  const refreshForm = async () => {
    await queryCache.invalidateQueries("lead_" + id);
  };
  const handleReservation = () => {
    if (start != null && end != null && start < end) {
      modals.openConfirmModal({
        title: t("leads.confirmReservationTitle"),
        size: "sm",
        centered: true,
        children: <Text size="sm">{t("leads.confirmReservation")}</Text>,
        labels: {
          confirm: t("leads.confirm"),
          cancel: t("leads.close"),
        },
        onConfirm: () => {
          setLoading(true);
          createReservation(
            {
              start: formatDateTimeForBackend(new Date(start)),
              reservedUntil: formatDateTimeForBackend(new Date(end)),
              sendEmail: sendEmail,
              leadId: id,
            },
            {
              onSuccess: () => {
                notifications.show({
                  color: "green",
                  title: t("leads.reservationSuccessTitle"),
                  message: t("leads.reservationSuccessMessage"),
                });
                void refreshForm();
              },
              onError: () => {
                notifications.show({
                  color: "red",
                  title: t("leads.reservationErrorTitle"),
                  message: t("leads.reservationErrorMessage"),
                });
              },
              onSettled: () => {
                setLoading(false);
              },
            },
          );
        },
      });
    } else {
      notifications.show({
        color: "red",
        title: t("leads.reservationFieldsRequiredTitle"),
        message: t("leads.reservationFieldsRequiredMessage"),
      });
    }
  };
  const cancelReservation = () => {
    modals.openConfirmModal({
      title: t("leads.cancelReservation"),
      size: "sm",
      centered: true,
      children: <Text size="sm">{t("leads.confirmReservationCancel")}</Text>,
      labels: {
        confirm: t("leads.confirm"),
        cancel: t("leads.close"),
      },
      onConfirm: () => {
        setLoading(true);
        updateReservation(
          {
            reservationStatus: "Cancelled",
          },
          {
            onSuccess: () => {
              setStart(new Date());
              setEnd(addDays(new Date(), 14));
              notifications.show({
                color: "green",
                title: t("leads.reservationCancelSuccessTitle"),
                message: t("leads.reservationCancelSuccessMessage"),
              });
              void refreshForm();
            },
            onError: () => {
              notifications.show({
                color: "red",
                title: t("leads.reservationCancelErrorTitle"),
                message: t("leads.reservationCancelErrorMessage"),
              });
            },
            onSettled: () => {
              setLoading(false);
            },
          },
        );
      },
    });
  };

  const extendReservation = () => {
    if (
      reservations?.data?.[0]?.reservationStatus === "Active" ||
      reservations?.data?.[0]?.reservationStatus === "AboutToExpire"
    ) {
      modals.openConfirmModal({
        title: t("leads.extend"),
        size: "sm",
        centered: true,
        children: <Text size="sm">{t("leads.confirmReservationExtend")}</Text>,
        labels: {
          confirm: t("leads.confirm"),
          cancel: t("leads.close"),
        },
        onConfirm: () => {
          setLoading(true);
          updateReservation(
            {
              reservedUntil: formatDateTimeForBackend(new Date(end!)),
            },
            {
              onSuccess: () => {
                setStart(null);
                setEnd(null);
                notifications.show({
                  color: "green",
                  title: t("leads.reservationExtendSuccessTitle"),
                  message: t("leads.reservationExtendSuccessMessage"),
                });
                void refreshForm();
              },
              onError: () => {
                notifications.show({
                  color: "red",
                  title: t("leads.reservationExtendErrorTitle"),
                  message: t("leads.reservationExtendErrorMessage"),
                });
              },
              onSettled: () => {
                setLoading(false);
              },
            },
          );
        },
      });
    } else if (reservations?.data?.[0]?.reservationStatus === "Extended") {
      notifications.show({
        color: "red",
        title: t("leads.reservationAlreadyExtendedTitle"),
        message: t("leads.reservationAlreadyExtendedMessage"),
      });
    } else {
      notifications.show({
        color: "red",
        title: t("leads.cannotExtendResFromCurrentStatusTitle"),
        message: t("leads.cannotExtendResFromCurrentStatusMessage"),
      });
    }
  };

  return (
    <>
      <ButtonMain
        onClick={open}
        loading={isLoading}
        disabled={!hasAdvisedUnits}
        label={t("leads.reserveUnits")}
        icon={<IconCalendarCheck size={18} />}
      />

      <Modal
        opened={opened}
        onClose={close}
        centered
        title={t("leads.reserveUnits")}
      >
        <LoadingOverlay visible={loading} />
        <DatePickerInput
          mt="sm"
          valueFormat={dateFormat.toUpperCase()}
          required
          minDate={new Date()}
          disabled={hasActiveReservation}
          placeholder={reservationStartFormatted ?? ""}
          clearable
          label={t("leads.reservationStart")}
          value={start}
          onChange={setStart}
        />
        <DatePickerInput
          mt="sm"
          valueFormat={dateFormat.toUpperCase()}
          required
          minDate={
            reservedUntil != null
              ? addDays(new Date(reservedUntil), 1)
              : start != null
                ? addDays(new Date(start), 1)
                : new Date()
          }
          clearable
          label={
            hasActiveReservation
              ? t("leads.extendUntil")
              : t("leads.reservedUntil")
          }
          value={end}
          onChange={setEnd}
        />
        <br />
        {!hasActiveReservation ? (
          <Switch
            checked={sendEmail}
            label={t("reservations.sendEmail")}
            labelPosition="left"
            onChange={(event) => setSendEmail(event.currentTarget.checked)}
          />
        ) : null}

        <br />
        <EntityLayout.TableMantine<
          Schemas["AdvisedUnitRetrieveDto"],
          Schemas["AdvisedUnitRetrieveDtoPagedList"]
        >
          resourcePath={`/api/Leads/${id}/advisedUnits` as PathKeys}
          queryKey="advisedUnit"
          entityPath="advisedUnits"
          redirectTo={window.location.pathname}
          toolbarEnabled={false}
          selectionEnabled={false}
          disableNavigation
          columns={[
            {
              accessorKey: "unit",
              header: t("advisedUnits.unit"),
              ...TableRenderer(UnitLookup, "units", ["unitCode"]),
            },
            {
              accessorKey: "reservation",
              header: t("reservations.start"),
              ...TableRenderer(ReservationLookup, "reservations", ["start"]),
            },
            {
              accessorKey: "reservation",
              header: t("reservations.until"),
              ...TableRenderer(ReservationLookup, "reservations", [
                "reservedUntil",
              ]),
            },
          ]}
        />
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          {hasActiveReservation ? (
            <>
              <Button
                onClick={cancelReservation}
                w={end != null ? "33%" : "48%"}
                mt={"xs"}
                size="xs"
              >
                {t("leads.cancelReservation")}
              </Button>
              {end != null ? (
                <Button
                  onClick={extendReservation}
                  w={"33%"}
                  mt={"xs"}
                  size="xs"
                >
                  {t("leads.extend")}
                </Button>
              ) : null}
              <Button
                onClick={close}
                variant="light"
                w={end != null ? "33%" : "48%"}
                mt={"xs"}
                size="xs"
              >
                {t("leads.close")}
              </Button>
            </>
          ) : (
            <>
              <Button onClick={close} variant="light" w={"48%"} mt={"xs"}>
                {t("leads.close")}
              </Button>
              <Button onClick={handleReservation} w={"48%"} mt={"xs"}>
                {t("leads.confirm")}
              </Button>
            </>
          )}
        </div>
      </Modal>
    </>
  );
}
