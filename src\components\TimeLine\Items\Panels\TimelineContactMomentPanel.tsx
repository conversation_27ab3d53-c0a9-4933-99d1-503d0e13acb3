import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Anchor, Box, Center, Flex, Loader, Stack } from "@mantine/core";
import { DateTime } from "luxon";
import { useTranslation } from "react-i18next";
interface TimelineContactMomentPanelProps {
  contactMomentId: string;
}

export function TimelineContactMomentPanel({
  contactMomentId,
}: TimelineContactMomentPanelProps) {
  const { t } = useTranslation("features");
  const { dateFormat } = useSettingsContext();
  const { data, isLoading } = useEntityQuery<
    Schemas["ContactMomentRetrieveDto"]
  >({
    resourcePath: `/api/ContactMoments/{id}`,
    resourceId: contactMomentId,
    queryKey: ["contactMoment", contactMomentId],
  });

  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <Box ml={8}>
      <Stack gap="xs">
        <Flex>
          {t("common.owner")}:
          <Anchor
            href={`/app/appUsers/${data?.ownerId}`}
            style={{ cursor: "pointer" }}
            ml={8}
          >
            {data?.owner?.name ?? data?.owner?.email}
          </Anchor>
        </Flex>
        <Flex>
          {t("common.createdOn")}:
          <Box ml={8}>
            {DateTime.fromJSDate(new Date(data?.createdOn ?? "")).toFormat(
              dateFormat + " HH:mm:ss",
            )}
          </Box>
        </Flex>
        <Flex align="flex-start">
          <Box miw={80}> {t("contactMoments.comment")}:</Box>
          <Box ml={8}>{data?.comment}</Box>
        </Flex>
      </Stack>
    </Box>
  );
}
