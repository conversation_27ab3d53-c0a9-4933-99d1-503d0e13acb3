import { DatesProvider } from "@mantine/dates";
import { type PropsWithChildren } from "react";
import "dayjs/locale/nl";
import "dayjs/locale/en";

interface CustomDatesProviderProps extends PropsWithChildren {
  language: string;
}

export function CustomDatesProvider({
  language,
  children,
}: CustomDatesProviderProps) {
  const locale = language === "Dutch" ? "nl" : "en";
  return (
    <DatesProvider settings={{ locale: locale }}>{children}</DatesProvider>
  );
}
