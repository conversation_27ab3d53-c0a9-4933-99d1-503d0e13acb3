// LookupTarget.tsx
import type React from "react";
import { forwardRef } from "react";
import { TextInput } from "@mantine/core";
import { LookupLeftSection, LookupRightSection } from "./LookupInputSections";
import { type UseFormReturnType } from "@mantine/form"; // Adjusted import
import { useLookup } from "@/components/Lookup/Context/useLookup";

type LookupTargetProps<T> = ReturnType<
  UseFormReturnType<T>["getInputProps"]
> & {
  required: boolean;
  disabled: boolean;
  entity: string;
  identifier: string;
  navigateEntity?: string;
  onClick: () => void;
  onRightSectionClick: () => void;
};

export const LookupTarget = forwardRef<
  HTMLInputElement,
  LookupTargetProps<unknown>
>(
  <T,>(
    {
      required,
      disabled,
      entity,
      identifier,
      navigateEntity,
      onClick,
      onRightSectionClick,
      ...props
    }: LookupTargetProps<T>,
    ref: React.Ref<HTMLInputElement>,
  ) => {
    const { searchTerm, setSearchTerm, lookupValue, setLookupValue, lookupId } =
      useLookup(identifier);

    return (
      <TextInput
        ref={ref}
        required={required}
        disabled={disabled}
        {...props}
        value={
          lookupValue != null && lookupValue !== "" ? lookupValue : searchTerm
        }
        onChange={(event) => {
          setLookupValue(event.currentTarget.value);
          setSearchTerm(event.currentTarget.value);
        }}
        leftSection={
          lookupId ? (
            <LookupLeftSection
              entity={entity}
              navigateEntity={navigateEntity}
              lookupId={lookupId}
            />
          ) : undefined
        }
        rightSection={
          <LookupRightSection
            disabled={disabled}
            lookupId={lookupId}
            onClick={onRightSectionClick}
          />
        }
        onClick={onClick}
      />
    );
  },
);

LookupTarget.displayName = "LookupTarget";
