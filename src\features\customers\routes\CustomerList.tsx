import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { CustomerColumns } from "../table/CustomerColumns";

const PATH = "Customers";
function CustomerListInner({ resourcePath, createPath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["CustomerRetrieveDto"],
        Schemas["CustomerRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="customer"
        entityPath="customers"
        title={t("customers.title")}
        toolbar={
          <>
            <EntityLayout.CreateButton to={createPath} />
          </>
        }
        redirectTo={window.location.pathname}
        visibleColumns={["name", "email", "mobile", "phone", "createdOn"]}
        columns={CustomerColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function CustomerList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <CustomerListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
