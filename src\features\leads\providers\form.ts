import { z } from "zod";
import { createFormContext } from "@mantine/form";
import {
  Discount,
  ProcessStage,
  ProductType,
  SizeOfUnit,
  RentAsBusiness,
  StorageDuration,
  StorageUnitReason,
  Transportation,
  LeadType,
  Salutation,
  StartWithin,
  LeadSource,
  PreferredLanguage,
  OptInType,
} from "@/types/enums";
import * as validator from "validator";
import { t, type TFunction } from "i18next";

export const getLeadFormSchema = (t: TFunction) =>
  z
    .object({
      firstName: z.string(),
      lastName: z.string(),
      email: z
        .string()
        .min(1, { message: t("common:validation.emailRequired") })
        .refine(
          (value) => {
            if (value) {
              return validator.isEmail(value);
            } else {
              return true;
            }
          },
          { message: t("common:validation.invalidEmail") },
        ),
      mobile: z
        .string()
        .min(1, { message: t("common:validation.phoneRequired") })
        .refine(
          (value) => {
            if (value) {
              return validator.isMobilePhone(value);
            } else {
              return true;
            }
          },
          { message: t("common:validation.invalidPhone") },
        ),
      phone: z.string().refine(
        (value) => {
          if (value) {
            return validator.isMobilePhone(value);
          } else {
            return true;
          }
        },
        { message: t("common:validation.invalidPhone") },
      ),
      salutation: z.enum(Salutation as [string]).nullable(),
      companyName: z.string(),
      discountText: z.string(),
      webformTitle: z.string(),
      webformDetails: z.string(),
      businessUnitId: z
        .preprocess(
          (val) => (val === null ? "" : val),
          z
            .string()
            .min(1, { message: t("common:validation.invalidBusinessUnit") }),
        )
        .refine((value) => value !== null && value !== "", {
          message: t("common:validation.invalidBusinessUnit"),
        })
        .refine((value) => value !== null, {
          message: t("common:validation.invalidBusinessUnit"),
        }),
      businessUnit: z.object({}).nullable(),
      storageValue: z.coerce.number().nullable(),
      spaceTourRemarks: z.string(),
      callCount: z.number().nullable(),
      callCountReached: z.number().nullable(),
      callCountNotReached: z.number().nullable(),
      quotationEmailSent: z.boolean().default(false),
      moveInDate: z.date().nullable(),
      nextCallback: z.date().nullable(),
      reservationStart: z.date().nullable(),
      reservedUntil: z.date().nullable(),
      rentAsBusiness: z.enum(RentAsBusiness as [string]).nullable(),
      transportation: z.enum(Transportation as [string]).nullable(),
      discount: z.enum(Discount as [string]).nullable(),
      storageDuration: z.enum(StorageDuration as [string]).nullable(),
      storageUnitReason: z.enum(StorageUnitReason as [string]).nullable(),
      processStage: z.enum(ProcessStage as [string]),
      type: z.enum(LeadType as [string]),
      startWithin: z.enum(StartWithin as [string]).nullable(),
      productType: z.enum(ProductType as [string]),
      sizeOfUnit: z.enum(SizeOfUnit as [string]).nullable(),
      optOutPhone: z.boolean().default(false),
      description: z.string(),
      existingContactId: z.string().nullable(),
      existingContact: z.object({}).nullable(),
      existingCustomerId: z.string().nullable(),
      existingCustomer: z.object({}).nullable(),
      contractId: z.string().nullable(),
      contract: z.object({}).nullable(),
      recordState: z.string(),
      comments: z.string(),
      promotionCode: z.string(),
      fax: z.string(),
      city: z.string(),
      zip: z.string(),
      street: z.string(),
      countryId: z.string().nullable(),
      country: z.object({}).nullable(),
      approvalForAddressUsage: z.boolean().default(false),
      storageTypeId: z.string().nullable(),
      storageType: z.object({}).nullable(),
      leadSource: z
        .string()
        .nullable()
        .refine((value) => value !== null, {
          message: t("common:validation.invalidLeadSource"),
        })
        .refine(
          (value) =>
            value !== null && Object.values(LeadSource).includes(value),
          {
            message: t("common:validation.invalidLeadSource"),
          },
        ),
      preferredLanguage: z.enum(PreferredLanguage as [string]).nullable(),
      optInType: z.enum(OptInType as [string]).nullable(),
      step: z.string().nullable(),
      price: z.string().nullable(),
      unitSize: z.string().nullable(),
      volume: z.coerce.number().nullable(),
      amount: z.coerce.number().nullable(),
      lossReasonId: z.string().nullable(),
      lossReason: z.object({}).nullable(),
      makeAnAppointment: z.date().nullable(),
    })
    .refine((data) => data.firstName && data.lastName, {
      message: t("common:validation.requiredLeadName"),
      path: ["firstName"],
    })
    .refine((data) => data.firstName && data.lastName, {
      message: t("common:validation.requiredLeadName"),
      path: ["lastName"],
    })
    .refine(
      (data) => {
        if (data.type === "Business" && !data.companyName) {
          return false; // Return false if companyName is missing when type is "Business"
        }
        return true; // Otherwise, validation passes
      },
      {
        message: t("common:validation.requiredCompanyName"),
        path: ["companyName"], // Specify the path of the field with the error
      },
    );

export const leadFormSchema = getLeadFormSchema(t);

export type LeadFormSchema = z.infer<typeof leadFormSchema>;

export const [LeadFormProvider, useLeadFormContext, useLeadForm] =
  createFormContext<LeadFormSchema>();
