import { useDisclosure } from "@mantine/hooks";
import {
  Modal,
  Button,
  Notification,
  NumberInput,
  Text,
  LoadingOverlay,
  Select,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconFileDatabase } from "@tabler/icons-react";
import { type Schemas } from "@/types";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useParams } from "react-router-dom";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";
import { config } from "@/config";
import { DatePickerInput } from "@mantine/dates";
import { useState } from "react";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { type components } from "@/types/api.generated";
import { useQueryClient } from "react-query";
import { QuoteStatus } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

export function SendQuoteButton() {
  const { dateFormat } = useSettingsContext();
  const [opened, { open, close }] = useDisclosure(false);
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const queryCache = useQueryClient();
  const [loading, setLoading] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Lead"],
    Schemas["LeadPatchDto"]
  >({
    resourcePath: "/api/Leads/{id}",
    resourceId: id!,
    queryKey: "lead_" + id!,
  });

  const {
    data: quoteDetails,
    isLoading,
    isError,
    refetch,
  } = useEntityQuery<Schemas["LeadQuoteDetailsDto"]>({
    resourcePath: "/api/Leads/{id}/leadQuoteDetails",
    resourceId: id!,
    queryKey: [`quoteDetails`, id],
  });

  const { mutate: updateCurrentQuote } = useEntityUpdateMutation<
    Schemas["Quote"],
    Schemas["QuotePatchDto"]
  >({
    resourcePath: "/api/Quotes/{id}",
    resourceId: quoteDetails?.currentQuoteId ?? "",
    queryKey: "quote_" + quoteDetails?.currentQuoteId,
  });

  const handleOpen = () => {
    void refetch();
    open();
  };

  const handleSendQuote = () => {
    update(
      { quotationEmailSent: true },
      {
        onSuccess: () => {
          notifications.show({
            color: "green",
            title: t("leads.sendQuoteSuccessTitle"),
            message: t("leads.sendQuoteSuccessMessage"),
          });
          void queryCache.invalidateQueries("quote_list");
          void queryCache.invalidateQueries("lead_" + id);
        },
        onError: () => {
          notifications.show({
            color: "red",
            title: t("leads.sendQuoteErrorTitle"),
            message: t("leads.sendQuoteErrorMessage"),
          });
        },
        onSettled: () => {
          setLoading(false);
        },
      },
    );
  };

  const handleUpdateCurrentQuote = (
    quoteStatus: components["schemas"]["QuoteStatusEnum"],
  ) => {
    updateCurrentQuote(
      { status: quoteStatus },
      {
        onSuccess: () => {
          notifications.show({
            color: "green",
            title: t("leads.updateQuoteSuccessTitle"),
            message: t("leads.updateQuoteSuccessMessage"),
          });
          void queryCache.invalidateQueries("quote_list");
          void queryCache.invalidateQueries("lead_" + id);
        },
        onError: () => {
          notifications.show({
            color: "red",
            title: t("leads.updateQuoteErrorTitle"),
            message: t("leads.updateQuoteErrorMessage"),
          });
        },
        onSettled: () => {
          setLoading(false);
          close();
        },
      },
    );
  };

  return (
    <>
      <ButtonMain
        label={t("leads.manageQuotes")}
        icon={<IconFileDatabase size={18} />}
        onClick={handleOpen}
      />
      <Modal
        opened={opened}
        onClose={close}
        size={"lg"}
        withCloseButton={false}
        centered
      >
        {isLoading ? (
          <LoadingOverlay visible={loading} />
        ) : isError ? (
          <Notification color="red">
            {t("leads.quoteDetailsErrorMessage")}
          </Notification>
        ) : (
          quoteDetails && (
            <div>
              <Text fz={18} fw={500} mb={16}>
                {t("leads.sendNewQuote")}
              </Text>
              <NumberInput
                disabled
                label={t("quotes.totalOneTimeFee")}
                value={quoteDetails.totalOneTimeFee}
                leftSection={config.CURRENCY.symbol}
                styles={{ label: { whiteSpace: "nowrap" } }}
              />
              <NumberInput
                disabled
                label={t("quotes.totalMonthlyPrice")}
                value={quoteDetails.totalMonthlyPrice}
                leftSection={config.CURRENCY.symbol}
                styles={{ label: { whiteSpace: "nowrap" } }}
              />
              <DatePickerInput
                disabled
                valueFormat={dateFormat.toUpperCase()}
                value={new Date(quoteDetails.expirationDate ?? "")}
                label={t("quotes.expirationDate")}
                styles={{ label: { whiteSpace: "nowrap" } }}
              />
            </div>
          )
        )}
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <Button
            loading={loading}
            onClick={() => {
              setLoading(true);
              handleSendQuote();
            }}
            w={"48%"}
            mt={"xs"}
            size="xs"
          >
            {t("leads.send")}
          </Button>
          <Button
            loading={loading}
            onClick={close}
            variant="light"
            w={"48%"}
            mt={"xs"}
            size="xs"
          >
            {t("leads.cancel")}
          </Button>
        </div>
        {quoteDetails?.currentQuoteId != null && (
          <div>
            <div>
              <Text fz={18} fw={500} mb={16} mt={16}>
                {t("leads.currentQuote")}
              </Text>
              <NumberInput
                disabled
                label={t("quotes.totalOneTimeFee")}
                value={quoteDetails.currentQuoteTotalOneTimeFee}
                leftSection={config.CURRENCY.symbol}
                styles={{ label: { whiteSpace: "nowrap" } }}
              />
              <NumberInput
                disabled
                label={t("quotes.totalMonthlyPrice")}
                value={quoteDetails.currentQuoteTotalMonthlyPrice}
                leftSection={config.CURRENCY.symbol}
                styles={{ label: { whiteSpace: "nowrap" } }}
              />
              <DatePickerInput
                disabled
                valueFormat={dateFormat.toUpperCase()}
                value={new Date(quoteDetails.currentQuoteExpirationDate ?? "")}
                label={t("quotes.expirationDate")}
                styles={{ label: { whiteSpace: "nowrap" } }}
              />
              <Select
                searchable
                disabled
                label={t("quotes.status")}
                value={t(
                  getEnumTransKey(
                    "quotes",
                    quoteDetails.currentQuoteStatus ?? "",
                  ),
                )}
                data={QuoteStatus.map((value) => ({
                  value,
                  label: t(getEnumTransKey("quotes", value)),
                }))}
              />
            </div>
            {quoteDetails?.currentQuoteStatus != "Accepted" && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginTop: "10px",
                }}
              >
                <Button
                  loading={loading}
                  onClick={() => {
                    setLoading(true);
                    handleUpdateCurrentQuote("Cancelled");
                  }}
                  w={"48%"}
                  mt={"xs"}
                  size="xs"
                >
                  {t("quotes.cancelQuote")}
                </Button>
                <Button
                  loading={loading}
                  onClick={() => {
                    setLoading(true);
                    handleUpdateCurrentQuote("Denied");
                  }}
                  w={"48%"}
                  mt={"xs"}
                  size="xs"
                >
                  {t("quotes.denyQuote")}
                </Button>
              </div>
            )}
          </div>
        )}
      </Modal>
    </>
  );
}
