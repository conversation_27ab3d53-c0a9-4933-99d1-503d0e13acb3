import type React from "react";
import { createContext, useState, useContext } from "react";

interface RouteBlockerContextValue {
  unsaved: boolean;
  setUnsaved?: (value: boolean) => void;
}

const RouteBlockerContext = createContext<RouteBlockerContextValue>({
  unsaved: false,
  setUnsaved: undefined,
});

export const RouteBlockerProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [unsaved, setUnsaved] = useState(false);
  return (
    <RouteBlockerContext.Provider value={{ unsaved, setUnsaved }}>
      {children}
    </RouteBlockerContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useRouteBlocker = () => useContext(RouteBlockerContext);
