import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { RefundProductForm } from "../components/RefundProductForm";
import { EntityLayout } from "@/features/entity";
import { Group, Loader } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";

interface RefundProductCreateProps {
  parentEntityId?: string;
  redirectTo?: string;
  usingModal?: boolean;
  closeModal?: () => void;
}

export function RefundProductCreate({
  parentEntityId: propParentEntityId,
  redirectTo: propRedirectTo,
  usingModal: propUsingModal,
  closeModal, // Add closeModal to props
}: RefundProductCreateProps) {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["RefundProduct"],
    Schemas["RefundProductCreateDto"]
  >({ resourcePath: "/api/RefundProducts", queryKey: "refundProduct" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const refundId = propParentEntityId ?? searchParams.get("refundId");
  const redirectTo =
    propRedirectTo ?? searchParams.get("redirectTo") ?? "/app/refundProducts";
  const usingModal = propUsingModal ?? false;
  const {
    data: refund = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Refund"]>({
    resourcePath: "/api/Refunds/{id}",
    resourceId: refundId!,
    queryKey: ["refund", refundId],
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <RefundProductForm
      initialValues={{
        refundId: refundId ?? "",
        refund: refund,
      }}
      isCreate={true}
      title={t("refundProducts.createTitle")}
      actionSection={
        <Group>
          {!usingModal && <EntityLayout.SaveButton setClose={setClose} />}
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (usingModal) {
              if (close) {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
                closeModal?.();
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
              }
            } else {
              if (close) {
                navigate(redirectTo);
              } else {
                let navigateTo = `/app/refundProducts/${data.data.id}`;
                navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
                navigate(navigateTo);
              }
            }
          },
        });
      }}
    />
  );
}
