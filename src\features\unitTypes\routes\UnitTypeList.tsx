import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { EntityLinkRenderer } from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";

const PATH = "UnitTypes";

export function UnitTypeListInner({
  resourcePath,
  createPath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["UnitTypeRetrieveDto"],
        Schemas["UnitTypeRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="unitType"
        entityPath="unitTypes"
        title={t("unitTypes.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "code",
            header: t("unitTypes.code"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "unitTypes"),
          },
          {
            accessorKey: "name",
            header: t("unitTypes.name"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "unitTypes"),
          },
        ]}
        initialSorting={[
          {
            id: "code",
            desc: false,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function UnitTypeList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <UnitTypeListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
