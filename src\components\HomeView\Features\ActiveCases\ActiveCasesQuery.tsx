import { useEntityListQuery } from "@/features/entity/queries";
import UseUserBusinessUnit from "@/hooks/businessUnit/useUserBusinessUnit";
import { type Schemas } from "@/types";

export default function ActiveCasesQuery() {
  const { userBusinessUnitId } = UseUserBusinessUnit();
  const start = new Date();
  start.setUTCHours(0, 0, 0, 0);
  const end = new Date();
  end.setUTCHours(23, 59, 59, 999);

  const filterClosedToday = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "Status == 5",
    `modifiedOn <= ${end.toISOString().replace("Z", "")}`,
    `modifiedOn >= ${start.toISOString().replace("Z", "")}`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data } = useEntityListQuery<Schemas["CaseRetrieveDtoPagedList"]>({
    resourcePath: "/api/Cases",
    params: {
      filter: filterClosedToday,
    },
    queryKey: `closedTodayCasesHome`,
  });

  const filterActiveCases = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "Status != 5",
  ]
    .filter(Boolean)
    .join(" && ");

  const { data: activeCases } = useEntityListQuery<
    Schemas["CaseRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Cases",
    params: {
      filter: filterActiveCases,
    },
    queryKey: `activeCasesHome`,
  });

  const cases = activeCases?.data ?? [];
  const closedToday = data?.totalCount;
  const totalCount = activeCases?.totalCount;
  return { totalCount, closedToday, cases };
}
