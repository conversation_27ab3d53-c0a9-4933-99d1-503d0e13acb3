import {
  type Dispatch,
  type SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  ActionIcon,
  Box,
  Button,
  Checkbox,
  Fieldset,
  Flex,
  Menu,
  Select,
  Stack,
  Textarea,
  Title,
  Center,
  Loader,
} from "@mantine/core";
import {
  IconPaperclip,
  IconPlus,
  IconSquareX,
  IconTrash,
} from "@tabler/icons-react";
import {
  AppointmentStatus,
  AppointmentType,
  AppointmentTypeSpaceTour,
  AppointmentTypeSelfStorage,
  AppointmentTypeEngineRoom,
  AppointmentTypeVault,
  AppointmentTypeRoboticStorage,
  AppointmentTypeMovingHelp,
  AppointmentTypeMovingHelpAdditional,
} from "@/types/enums";
import { DateInput, TimeInput } from "@mantine/dates";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getEnumTransKey } from "@/utils/trans";
import { getDirtyFormFields } from "@/features/entity/utils";
import { GetDurationString } from "./DurationCombobox";
import { type Schemas } from "@/types";
import { CloseAppointmentButtons } from "./CloseAppointmentButtons";
import { AppointmentAttachments } from "../utils/AppointmentAttachments";
import { LeadContactLookup } from "@/components/Lookup/Features/LeadContacts/LeadContactLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { UserGroupLookup } from "@/components/Lookup/Features/UserGroups/UserGroupLookup";
import { useNavigate } from "react-router-dom";
import { useRouteBlocker } from "@/hooks/blockerContext";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { RentableItemLookup } from "@/components/Lookup/Features/RentableItems/RentableItemLookup";
import { lowerCaseNthLetter } from "@/utils/filters";
import { EntityLayout } from "@/features/entity";
import NoShowWizardButton from "@/components/Wizards/NoShow/NoShowWizardButton";
import { useEntityQuery } from "@/features/entity/queries";
import { CustomStartTimeControl } from "./timePickers/CustomStartTimeControl";
import { CustomEndTimeControl } from "./timePickers/CustomEndTimeControl";
import {
  addMinutesToTimeString,
  calculateDurationString,
  calculateNewEndDate,
  calculateNewStartDate,
  getAdjustedEndDateFromTimeString,
  isMultiDayEvent,
  recalculateEndTime,
} from "./timePickers/timeUtils";

const appointmentTypeEnum = z.enum(AppointmentType as [string]);
const appointmentStatusEnum = z.enum(AppointmentStatus as [string]);
//const rentableItemTypeEnum = z.enum(RentableItemType as [string]);

const formSchema = z.object({
  id: z.string(),
  description: z.string(),
  noShowNotes: z.string(),
  startDate: z.date().nullable(),
  endDate: z.date().nullable(),
  businessUnitId: z.string().nullable(),
  businessUnit: z.object({}).nullable(),
  leadId: z.string().nullable(),
  lead: z.object({}).nullable(),
  contactId: z.string().nullable(),
  contact: z.object({}).nullable(),
  rentableItemId: z.string().nullable(),
  rentableItem: z.object({}).nullable(),
  assignedToId: z.string().nullable(),
  assignedTo: z.object({}).nullable(),
  appointmentType: appointmentTypeEnum.refine((value) => !!value).nullable(),
  appointmentStatus: appointmentStatusEnum.refine((value) => !!value),
  createPhoneCallFlag: z.boolean().default(false),
});

export type FormSchema = z.infer<typeof formSchema>;

interface AppointmentFormProps {
  onSubmit: (
    values: Partial<FormSchema>,
    attachment?: File[],
    id?: string,
  ) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  isCreate: boolean;
  fromCalendar?: boolean;
  showBackButton?: boolean;
  closeModal?: () => void;
  isModal?: boolean;
  isMovingHelp?: boolean;
  creatingFromRentableItem?: boolean;
  isAdmin?: boolean;
  setPhantomEvent?: Dispatch<
    SetStateAction<Schemas["AppointmentRetrieveDto"] | null>
  >;
}

export function AppointmentForm({
  onSubmit,
  actionSection,
  initialValues,
  isCreate,
  fromCalendar,
  closeModal,
  isMovingHelp = false,
  isModal,
  creatingFromRentableItem,
  setPhantomEvent,
  isAdmin = false,
}: AppointmentFormProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const [editableForm, setEditableForm] = useState(isCreate ? true : false);
  const { lookupEntity: businessUnitLookup } = useLookup<
    Schemas["BusinessUnitRetrieveDto"]
  >("businessUnitIdAppointment");

  const [submitDisabled, setSubmitDisabled] = useState(false);
  const navigate = useNavigate();
  const [attachment, setAttachment] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      setAttachment((prevFiles) => [...prevFiles, ...newFiles]);
    }
  };

  const refStart = useRef<HTMLInputElement>(null);
  const refEnd = useRef<HTMLInputElement>(null);

  const { setUnsaved } = useRouteBlocker();

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      description: initialValues?.description ?? "",
      noShowNotes: initialValues?.noShowNotes ?? "",
      startDate: initialValues?.startDate ?? new Date(),
      endDate: initialValues?.endDate ?? new Date(),
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      assignedToId: initialValues?.assignedToId ?? "",
      assignedTo: initialValues?.assignedTo ?? null,
      leadId: initialValues?.leadId ?? "",
      lead: initialValues?.lead ?? null,
      contactId: initialValues?.contactId ?? "",
      contact: initialValues?.contact ?? null,
      rentableItemId: initialValues?.rentableItemId ?? "",
      rentableItem: initialValues?.rentableItem ?? null,
      appointmentType: initialValues?.appointmentType ?? null,
      appointmentStatus: initialValues?.appointmentStatus ?? "",
      createPhoneCallFlag: initialValues?.createPhoneCallFlag ?? false,
    },
    validate: zodResolver(formSchema),
  });

  const hasUnsavedChanges = form.isDirty();
  useEffect(() => {
    if (hasUnsavedChanges !== undefined) {
      if (setUnsaved) {
        setUnsaved(hasUnsavedChanges);
      }
    }

    return () => {
      if (setUnsaved) {
        setUnsaved(false);
      }
    };
  }, [hasUnsavedChanges, setUnsaved]);

  const currentTime = new Date();
  const isSameDay = (date1: Date, date2: Date) => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  };

  const occursToday =
    isSameDay(initialValues?.startDate ?? new Date(), currentTime) ||
    isSameDay(initialValues?.endDate ?? new Date(), currentTime);

  const displayEditButton =
    !editableForm &&
    (initialValues?.appointmentStatus == "Open" ||
      initialValues?.appointmentStatus == "Rescheduled" ||
      (initialValues?.appointmentStatus == "NoShow" && occursToday)) &&
    (initialValues?.appointmentType != "BlockCalendar" ||
      !(
        initialValues?.rentableItemId &&
        initialValues.rentableItemId.trim() !== ""
      ) ||
      isAdmin);

  const displayCloseAppointmentButtons =
    !editableForm &&
    displayEditButton &&
    (((initialValues?.appointmentType != "BlockCalendar" || isAdmin) &&
      initialValues?.appointmentType != "MovingHelpBlockCalendar") ||
      (initialValues?.appointmentType == "BlockCalendar" &&
        initialValues?.rentableItemId &&
        initialValues.rentableItemId.trim() !== ""));

  const isTransportAppointment =
    initialValues?.appointmentType == "Trailer" ||
    initialValues?.appointmentType == "MovingVan" ||
    initialValues?.appointmentType == "MovingHelp";

  const isRentableAdmin =
    initialValues?.appointmentType == "BlockCalendar" && isAdmin;

  const isContact =
    form.getValues().contactId &&
    form.getValues().contactId !== "" &&
    (!form.getValues().leadId || form.getValues().leadId === "");

  const [startTime, setStartTime] = useState(
    form.getValues().startDate?.getHours() +
      ":" +
      form.getValues().startDate?.getMinutes(),
  );

  const [endTime, setEndTime] = useState(
    form.getValues().endDate?.getHours() +
      ":" +
      form.getValues().endDate?.getMinutes(),
  );

  const [duration, setDuration] = useState(
    calculateDurationString(startTime, endTime),
  );

  const setDefaultDuration = (AppointmentType: string) => {
    let newEndTime = endTime;

    if (AppointmentType === "BankSafe") {
      newEndTime = addMinutesToTimeString(startTime, 45);
    } else if (AppointmentType === "SpaceTour") {
      newEndTime = addMinutesToTimeString(startTime, 30);
    } else if (AppointmentType === "EngineRoomTour") {
      newEndTime = addMinutesToTimeString(startTime, 30);
    } else if (AppointmentType === "RoboticStorageTour") {
      newEndTime = addMinutesToTimeString(startTime, 30);
    } else return;

    submitEndDate(newEndTime);
    if (refEnd.current) {
      refEnd.current.value = newEndTime;
    }
    setEndTime(newEndTime);

    const eventDuration = calculateDurationString(startTime, newEndTime);
    setDuration(eventDuration);

    if (setPhantomEvent) {
      setPhantomEvent({
        startDate: form.getValues().startDate?.toString(),
        endDate: form.getValues().endDate?.toString(),
      });
    }
  };

  const submitEndDate = (val: string) => {
    const newEndDate = getAdjustedEndDateFromTimeString(
      val,
      form.getValues().startDate,
      form.getValues().endDate,
      isMultiDayEvent,
    );
    form.setValues({ endDate: newEndDate });
    form.setDirty({ endTime: true });
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeFile = (index: number) => {
    setAttachment((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const removeAllFiles = () => {
    setAttachment([]);
  };

  const hasVault = !!businessUnitLookup?.hasVault;
  const isSelfStorage = !!businessUnitLookup?.isSelfStorage;
  const hasEngineRoom = !!businessUnitLookup?.hasEngineRoom;
  const hasRoboticStorage = !!businessUnitLookup?.hasRoboticStorage;

  const isBusinessUnitSelected =
    businessUnitLookup && Object.keys(businessUnitLookup).length > 0;

  //set isBlocking to true if the appointmentType is BlockCalendar or MovingHelpBlockCalendar
  const isBlocking =
    form.getValues().appointmentType === "BlockCalendar" ||
    form.getValues().appointmentType === "MovingHelpBlockCalendar";

  const isBusinessUnitIdFilled = !!form.getValues().businessUnitId;
  const isRentableItemFilled = !!form.getValues().rentableItemId;
  const clearRentableItem = () => {
    form.setFieldValue("rentableItemId", "");
    form.setFieldValue("rentableItem", null);
  };
  const isRentableItemAllowed =
    (form.getValues().appointmentType === "MovingVan" ||
      form.getValues().appointmentType === "Trailer" ||
      (form.getValues().appointmentType === "BlockCalendar" &&
        creatingFromRentableItem)) &&
    isBusinessUnitIdFilled;

  if (isCreate && !isRentableItemAllowed && isRentableItemFilled) {
    clearRentableItem();
  }

  if (isBlocking && form.getValues().leadId != "") {
    form.setFieldValue("leadId", "");
    form.setFieldValue("lead", null);
  }

  if (isBlocking && form.getValues().contactId != "") {
    form.setFieldValue("contactId", "");
    form.setFieldValue("contact", null);
  }

  if (
    form.getValues().leadId == "" &&
    form.getValues().createPhoneCallFlag == true
  ) {
    form.setFieldValue("createPhoneCallFlag", false);
  }

  if (
    form.values.appointmentType == "MovingHelpBlockCalendar" &&
    form.getValues().businessUnitId != ""
  ) {
    form.setFieldValue("businessUnitId", "");
    form.setFieldValue("businessUnit", null);
  }

  const appointmentTypeDataRaw = [
    ...(isSelfStorage ? AppointmentTypeSpaceTour : []),
    ...(hasEngineRoom ? AppointmentTypeEngineRoom : []),
    ...(hasVault ? AppointmentTypeVault : []),
    ...(hasRoboticStorage ? AppointmentTypeRoboticStorage : []),
    ...(isSelfStorage ? AppointmentTypeSelfStorage : []),
    ...(isSelfStorage || isMovingHelp ? AppointmentTypeMovingHelp : []),
    ...(isMovingHelp ? AppointmentTypeMovingHelpAdditional : []),
    ...(!isBusinessUnitSelected ? AppointmentType : []),
  ].map((value) => ({
    value,
    label: t(getEnumTransKey("appointments", value)),
  }));

  const appointmentTypeData = appointmentTypeDataRaw.filter(
    (item, index, self) =>
      index === self.findIndex((i) => i.value === item.value),
  );

  const defaultTime = form.values.startDate?.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });

  isMovingHelp =
    isMovingHelp == false
      ? form.values.appointmentType == "MovingHelp" ||
        form.values.appointmentType == "MovingHelpBlockCalendar"
      : isMovingHelp;

  const { data: lead, isLoading: loadingLead } = useEntityQuery<
    Schemas["LeadRetrieveDto"]
  >({
    resourcePath: "/api/Leads/{id}",
    resourceId: initialValues?.leadId ?? "",
    queryKey: ["lead", initialValues?.leadId],
  });

  const isLeadClosed =
    lead?.processStage == "Won" || lead?.processStage == "Lost";

  if (loadingLead) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <>
      <form
        onSubmit={form.onSubmit((fields) => {
          const filteredFields = getDirtyFormFields(
            fields,
            isCreate,
            form.isDirty,
          );
          setSubmitDisabled(true);
          setTimeout(() => {
            setSubmitDisabled(false);
          }, 5000);
          if (isCreate) {
            if (attachment.length > 0) {
              debouncedOnSubmit(filteredFields, attachment, form.values.id);
            } else {
              debouncedOnSubmit(filteredFields);
            }
          } else {
            if (attachment.length > 0) {
              debouncedOnSubmit(filteredFields, attachment, form.values.id);
            } else {
              debouncedOnSubmit(filteredFields);
            }
          }
          form.resetDirty();
        })}
        onChange={() => {
          if (setPhantomEvent) {
            setPhantomEvent({
              startDate: form.getValues().startDate?.toString(),
              endDate: form.getValues().endDate?.toString(),
            });
          }
        }}
      >
        <EntityLayout
          actionSection={!fromCalendar || isAdmin ? actionSection : null}
          stickyHeader={false}
          disabled={
            !isCreate &&
            lead?.recordState === "Inactive" &&
            initialValues?.appointmentType !== "MovingVan" &&
            initialValues?.appointmentType !== "Trailer" &&
            initialValues?.appointmentType !== "MovingHelp"
          }
        >
          <Flex justify="space-between" align="center" direction="row" m={8}>
            <Title order={3}>
              {isCreate
                ? t("appointments.createAppointment")
                : t("appointments.editAppointment") +
                  " - " +
                  t(
                    "appointments." +
                      lowerCaseNthLetter(
                        initialValues?.appointmentStatus ?? "",
                      ),
                  )}
            </Title>
            {fromCalendar || isModal ? (
              <IconSquareX
                width={44}
                className="calendar-toolbar-icon"
                onClick={() => {
                  if (closeModal) {
                    closeModal();
                    if (setPhantomEvent) {
                      setPhantomEvent({ startDate: null, endDate: null });
                    }
                  } else {
                    navigate(-1);
                  }
                }}
              />
            ) : null}
          </Flex>

          <Stack mx={8} gap={8}>
            <Stack gap={4}>
              <Select
                required
                clearable
                searchable
                disabled={!isCreate || (isCreate && creatingFromRentableItem)}
                label={t("appointments.appointmentType")}
                data={appointmentTypeData}
                value={null}
                {...form.getInputProps("appointmentType")}
                onChange={(event) => {
                  form.setFieldValue("appointmentType", event);
                  if (isRentableItemFilled) {
                    clearRentableItem();
                  }
                  setDefaultDuration(event?.toString() ?? "");
                }}
                {...{
                  labelProps: {
                    style: {
                      flex: isModal ? "0.75" : "2",
                      fontSize: "0.7rem",
                      fontWeight: "bold",
                    },
                  },
                }}
              />

              <BusinessUnitLookup
                flex={1}
                required={
                  form.values.appointmentType != "MovingHelpBlockCalendar"
                }
                mt={0}
                label={t("appointments.businessUnit")}
                disabled={
                  !editableForm ||
                  initialValues?.appointmentStatus === "NoShow" ||
                  form.values.appointmentType == "MovingHelpBlockCalendar" ||
                  (isCreate && creatingFromRentableItem) ||
                  (!isCreate && isRentableItemFilled)
                }
                initial={form.getValues().businessUnit}
                initialId={form.getValues().businessUnitId}
                identifier="businessUnitIdAppointment"
                filterBUbyAppointmentType={form.getValues().appointmentType}
                {...form.getInputProps("businessUnitId")}
                onChange={(event: string) => {
                  form.setFieldValue("businessUnitId", event);
                  clearRentableItem();
                }}
                {...{
                  labelProps: {
                    style: {
                      flex: isModal ? "0.75" : "2",
                      fontSize: "0.7rem",
                      fontWeight: "bold",
                    },
                  },
                }}
              />
              <RentableItemLookup
                flex={1}
                required={
                  form.values.appointmentType == "MovingVan" ||
                  form.values.appointmentType == "Trailer"
                }
                mt={0}
                label={t("appointments.rentableItem")}
                disabled={
                  !editableForm ||
                  initialValues?.appointmentStatus === "NoShow" ||
                  !isRentableItemAllowed ||
                  !isBusinessUnitIdFilled ||
                  (isCreate && creatingFromRentableItem)
                }
                initial={form.getValues().rentableItem}
                initialId={form.getValues().rentableItemId}
                identifier="rentableItemIdAppointment"
                {...form.getInputProps("rentableItemId")}
                {...{
                  labelProps: {
                    style: {
                      flex: isModal ? "0.75" : "2",
                      fontSize: "0.7rem",
                      fontWeight: "bold",
                    },
                  },
                }}
                startDate={form.getValues().startDate}
                endDate={form.getValues().endDate}
                businessUnitId={form.getValues().businessUnitId ?? ""}
                rentableItemType={form.values.appointmentType}
                appointmentId={initialValues?.id}
              />
            </Stack>
            <Fieldset
              disabled={
                !editableForm || initialValues?.appointmentStatus === "NoShow"
              }
            >
              {isMovingHelp &&
                (form.getValues().appointmentType == "MovingHelp" ||
                  form.getValues().appointmentType ==
                    "MovingHelpBlockCalendar") && (
                  <>
                    <Flex dir="row" gap={"xs"} align={"center"}>
                      <UserGroupLookup
                        required
                        mt={0}
                        flex={1}
                        label={t("appointments.assignedTo")}
                        initial={form.getValues().assignedTo}
                        initialId={form.getValues().assignedToId}
                        identifier="assignedToIdAppointmentForm"
                        {...form.getInputProps("assignedToId")}
                        {...{
                          labelProps: {
                            style: {
                              flex: isModal ? "0.75" : "2.3",
                              fontSize: "0.7rem",
                              fontWeight: "bold",
                            },
                          },
                        }}
                      />
                      <Menu trigger="click-hover">
                        <Menu.Target>
                          <ActionIcon
                            variant="outline"
                            size="sm"
                            color="gray"
                            w={32}
                            h={32}
                          >
                            <IconPaperclip
                              style={{ width: "70%", height: "70%" }}
                              stroke={1.5}
                            />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item
                            rightSection={<IconPlus size={14} />}
                            onClick={triggerFileInput}
                          >
                            {t("features:downloads.addFiles")}
                          </Menu.Item>
                          {attachment.length > 0 &&
                            attachment.map((file, index) => (
                              <Menu.Item
                                key={index}
                                rightSection={<IconTrash size={14} />}
                                onClick={() => removeFile(index)}
                              >
                                {file.name}
                              </Menu.Item>
                            ))}
                          <Menu.Item
                            rightSection={<IconTrash size={14} />}
                            onClick={removeAllFiles}
                            disabled={attachment.length === 0}
                          >
                            {t("features:downloads.removeAllFiles")}
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Flex>

                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      style={{ display: "none" }}
                      multiple
                      accept="all"
                    />
                  </>
                )}
              <Stack gap={4}>
                <LeadContactLookup
                  flex={1}
                  initial={
                    form.getValues().leadId
                      ? form.getValues().lead
                      : form.getValues().contact
                  }
                  entity={form.getValues().leadId ? "leads" : "contacts"}
                  identifier="leadcontacts"
                  label={
                    form.getValues().leadId
                      ? t("appointments.lead")
                      : form.getValues().contactId
                        ? t("appointments.contact")
                        : t("appointments.leadAndContact")
                  }
                  required={isMovingHelp ? false : isBlocking ? false : true}
                  {...{
                    labelProps: {
                      style: {
                        flex: isModal ? "0.75" : "2",
                        fontSize: "0.7rem",
                        fontWeight: "bold",
                      },
                    },
                    styles: {
                      root: {
                        flexDirection: "column",
                      },
                    },
                  }}
                  disabled={isBlocking}
                  multiLookupOnChange={(
                    value: string,
                    id: string,
                    entity: string,
                  ) => {
                    switch (entity) {
                      case "contacts":
                        form.setFieldValue("contact", {
                          fullName: value,
                          id: id,
                        });
                        form.setFieldValue("contactId", id ?? "");
                        form.setFieldValue("leadId", "");
                        form.setDirty({
                          contact: false,
                          leadId: true,
                          contactId: true,
                        });
                        break;
                      case "leads":
                        form.setFieldValue("lead", {
                          fullName: value,
                          id: id,
                        });
                        form.setFieldValue("leadId", id ?? "");
                        form.setFieldValue("contactId", "");
                        form.setDirty({
                          lead: false,
                          leadId: true,
                          contactId: true,
                        });
                        break;
                    }
                  }}
                  onChange={(value: string) => {
                    // is only used for reset
                    if (value == null) {
                      form.setFieldValue("leadId", "");
                      form.setFieldValue("contactId", "");
                    }
                  }}
                />
                {isCreate && (
                  <Checkbox
                    disabled={
                      form.getValues().leadId == "" ||
                      form.getValues().leadId == null ||
                      form.getValues().leadId == undefined
                    }
                    checked={form.getValues().createPhoneCallFlag}
                    label={t("appointments.createPhoneCallFlag")}
                    onChange={(event) =>
                      form.setFieldValue(
                        "createPhoneCallFlag",
                        event.currentTarget.checked,
                      )
                    }
                  />
                )}
              </Stack>
              <Stack my={16} gap={4}>
                <Flex gap={8} align="flex-end">
                  <DateInput
                    flex={2}
                    valueFormat={dateFormat.toUpperCase()}
                    size="xs"
                    label={t("appointments.startDate")}
                    {...form.getInputProps("startDate")}
                    onChange={(event) => {
                      if (!event) return;

                      const startDate = form.getValues().startDate;
                      const endDate = form.getValues().endDate;

                      if (startDate && endDate) {
                        const dayShiftAmount = Math.floor(
                          (event.getTime() - startDate.getTime()) /
                            (1000 * 60 * 60 * 24),
                        );

                        const newEndDate = new Date(endDate);
                        newEndDate.setDate(
                          newEndDate.getDate() + dayShiftAmount,
                        );

                        form.setValues({
                          startDate: event,
                          endDate: newEndDate,
                        });

                        if (!isCreate) {
                          form.setValues({
                            appointmentStatus: "Rescheduled",
                          });
                        }
                      }

                      if (setPhantomEvent) {
                        setPhantomEvent({
                          startDate: event?.toString() ?? "",
                          endDate: form.getValues().endDate?.toString() ?? "",
                        });
                      }
                    }}
                    {...{
                      labelProps: {
                        style: {
                          flex: isModal ? "1.75" : "3.2",
                          fontSize: "0.7rem",
                          fontWeight: "bold",
                        },
                      },
                      styles: {
                        root: {
                          flexDirection: "column",
                        },
                      },
                    }}
                  />
                  <TimeInput
                    size="xs"
                    flex={2}
                    defaultValue={defaultTime}
                    onChange={(event) => {
                      if (event.target.value === "") {
                        event.target.value = defaultTime ?? "";
                        return;
                      }

                      if (event.target.value !== undefined) {
                        if (!isCreate) {
                          form.setValues({
                            appointmentStatus: "Rescheduled",
                          });
                        }

                        // recalculate start date
                        const newStartDate = calculateNewStartDate(
                          form.getValues().startDate ?? new Date(),
                          event.target.value,
                        );

                        // recalculate end time
                        const newEndTime = recalculateEndTime(
                          event.target.value,
                          duration,
                        );

                        if (refEnd.current) {
                          refEnd.current.value = newEndTime;
                        }

                        // recalculate end date
                        const newEndDate = calculateNewEndDate(
                          form.getValues().startDate ?? new Date(),
                          form.getValues().endDate ?? new Date(),
                          newEndTime,
                          newStartDate,
                        );

                        // set new values
                        form.setValues({ startDate: newStartDate });
                        setStartTime(event.target.value);
                        setEndTime(newEndTime);
                        form.setValues({ endDate: newEndDate });
                        form.setDirty({ startDate: true, endDate: true });
                      }
                    }}
                    ref={refStart}
                    rightSection={
                      <CustomStartTimeControl
                        startDate={form.getValues().startDate}
                        endDate={form.getValues().endDate}
                        setEndDate={(endDate) => form.setValues({ endDate })}
                        setStartDate={(startDate) =>
                          form.setValues({ startDate })
                        }
                        duration={duration}
                        setPhantomEvent={setPhantomEvent}
                        refStart={refStart}
                        refEnd={refEnd}
                        startTime={startTime}
                        setStartTime={setStartTime}
                        setEndTime={setEndTime}
                      />
                    }
                    {...{
                      labelProps: {
                        style: {
                          flex: isModal ? "1" : undefined,
                          fontSize: "0.7rem",
                          fontWeight: "bold",
                        },
                      },
                      styles: {
                        root: {
                          flexDirection: "column",
                        },
                      },
                    }}
                  />
                </Flex>

                <Flex gap={8} align="flex-end">
                  <DateInput
                    flex={2}
                    size="xs"
                    minDate={form.values.startDate ?? new Date()}
                    valueFormat={dateFormat.toUpperCase()}
                    label={t("appointments.endDate")}
                    {...form.getInputProps("endDate")}
                    onChange={(event) => {
                      // Update the form value first
                      form.setFieldValue("endDate", event);
                      if (!isCreate) {
                        form.setValues({
                          appointmentStatus: "Rescheduled",
                        });
                      }
                      if (setPhantomEvent) {
                        setPhantomEvent({
                          endDate: event?.toString() ?? "",
                          startDate: form.values.startDate?.toString() ?? "",
                        });
                      }
                    }}
                    {...{
                      labelProps: {
                        style: {
                          flex: isModal ? "1.75" : "3.2",
                          fontSize: "0.7rem",
                          fontWeight: "bold",
                        },
                      },
                      styles: {
                        root: {
                          flexDirection: "column",
                        },
                      },
                    }}
                  />
                  <TimeInput
                    flex={2}
                    size="xs"
                    defaultValue={form.values.endDate?.toLocaleTimeString(
                      "en-US",
                      {
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: false,
                      },
                    )}
                    label={`Duration: ${GetDurationString(form.values.startDate!, form.values.endDate!)}`}
                    onChange={(event) => {
                      if (event.target.value === "") {
                        event.target.value = defaultTime ?? "";
                        return;
                      }
                      if (event.target.value !== undefined) {
                        submitEndDate(event.target.value);
                      }
                      const eventDuration = calculateDurationString(
                        startTime,
                        event.target.value,
                      );
                      setDuration(eventDuration);
                      if (!isCreate) {
                        form.setValues({
                          appointmentStatus: "Rescheduled",
                        });
                      }
                    }}
                    ref={refEnd}
                    rightSection={
                      <CustomEndTimeControl
                        startDate={form.getValues().startDate}
                        endDate={form.getValues().endDate}
                        setEndDate={(endDate) => form.setValues({ endDate })}
                        setDuration={setDuration}
                        setPhantomEvent={setPhantomEvent}
                        refEnd={refEnd}
                        startTime={startTime}
                        endTime={endTime}
                        setEndTime={setEndTime}
                      />
                    }
                    {...{
                      labelProps: {
                        style: {
                          flex: isModal ? "1" : undefined,
                          fontSize: "0.7rem",
                          fontWeight: "bold",
                        },
                      },
                      styles: {
                        root: {
                          flexDirection: "column",
                        },
                      },
                    }}
                  />
                </Flex>
              </Stack>
            </Fieldset>
            <Fieldset disabled={!editableForm}>
              <Textarea
                mt={0}
                label={t("appointments.description")}
                {...form.getInputProps("description")}
                minRows={4}
                autosize
                {...{
                  labelProps: {
                    style: {
                      flex: isModal ? "0.75" : "1.25",
                      fontSize: "0.7rem",
                      fontWeight: "bold",
                    },
                  },
                  styles: {
                    root: {
                      flexDirection: "column",
                    },
                  },
                }}
              />
            </Fieldset>
            {!isCreate && form.getValues().appointmentStatus == "NoShow" && (
              <Textarea
                mt={0}
                disabled
                label={t("appointments.noShowNotes")}
                {...form.getInputProps("noShowNotes")}
                minRows={4}
                autosize
                {...{
                  labelProps: {
                    style: {
                      flex: isModal ? "0.75" : "1.25",
                      fontSize: "0.7rem",
                      fontWeight: "bold",
                    },
                  },
                  styles: {
                    root: {
                      flexDirection: "column",
                    },
                  },
                }}
              />
            )}
          </Stack>

          <Flex
            m={8}
            gap="xs"
            justify="center"
            align="center"
            direction="row"
            wrap="nowrap"
          >
            <Button
              disabled={!displayEditButton}
              display={isCreate || editableForm ? "none" : undefined}
              variant="light"
              w={"100%"}
              onClick={() => {
                setEditableForm(true);
              }}
            >
              {t("appointments.edit")}
            </Button>

            {displayEditButton &&
            displayCloseAppointmentButtons &&
            (isContact ||
              isLeadClosed ||
              isTransportAppointment ||
              isRentableAdmin) ? ( //Contact is filled and lead is empty. Additional check to make sure appointment is for Contact or lead is won / lost
              <CloseAppointmentButtons
                currentState={initialValues?.appointmentStatus}
                appointmentId={initialValues?.id ?? ""}
                appointmentType={initialValues?.appointmentType ?? ""}
                rentableItemId={initialValues?.rentableItemId ?? ""}
                disabled={!displayEditButton || !displayCloseAppointmentButtons}
                isAdmin={isAdmin}
              />
            ) : form.getValues().leadId &&
              form.getValues().leadId != "" &&
              !editableForm ? (
              <NoShowWizardButton
                appointmentId={form.getValues().id}
                leadId={form.getValues().leadId!}
                disabled={!displayEditButton || !displayCloseAppointmentButtons}
              />
            ) : null}

            <Button
              variant="light"
              disabled={submitDisabled}
              display={isCreate || !editableForm ? "none" : undefined}
              w={"100%"}
              onClick={() => {
                form.reset();
                setEditableForm(false);
              }}
            >
              {t("appointments.cancel")}
            </Button>

            {editableForm && (
              <Button
                variant="filled"
                disabled={submitDisabled || !form.isDirty()}
                w={"100%"}
                style={
                  !editableForm ? { pointerEvents: "none", opacity: "0.6" } : {}
                }
                type="submit"
              >
                {isCreate
                  ? t("appointments.saveAndSend")
                  : form.isDirty("startDate") || form.isDirty("endDate")
                    ? t("appointments.rescheduleAndSend")
                    : t("appointments.save")}
              </Button>
            )}
          </Flex>
          {isMovingHelp &&
            form.values.appointmentType == "MovingHelp" &&
            form.values.id && (
              <Box mr={8} mt={8}>
                <AppointmentAttachments appointmentId={form.values.id} />
              </Box>
            )}
        </EntityLayout>
      </form>
    </>
  );
}
