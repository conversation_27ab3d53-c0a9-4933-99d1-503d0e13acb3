import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { useState } from "react";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { PageLoader } from "@/components/PageLoader";
import { ContractForm } from "../components/ContractForm";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";
import { type ContractFormSchema } from "../providers/form";

export function ContractShow() {
  const queryCache = useQueryClient();
  const [close, setClose] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Contract"],
    Schemas["ContractCreateDto"]
  >({
    resourcePath: "/api/Contracts/{id}",
    resourceId: id!,
    queryKey: "contract",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Contract"]>({
    resourcePath: "/api/Contracts/{id}",
    resourceId: id!,
    queryKey: "contract",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("contract_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <ContractForm
      isCreate={false}
      title={t("contracts.showTitle", { id })}
      contractId={data.id}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/contracts");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/contracts");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      initialValues={
        filterFalsyValues({
          ...data,
          from: data.from ? new Date(data.from) : null,
          to: data.to ? new Date(data.to) : null,
          signedOn: data.signedOn ? new Date(data.signedOn) : null,
          firstCancelDate: data.firstCancelDate
            ? new Date(data.firstCancelDate)
            : null,
          cancelledOn: data.cancelledOn ? new Date(data.cancelledOn) : null,
          moveOutDate: data.moveOutDate ? new Date(data.moveOutDate) : null,
          invoicePeriodFrom: data.invoicePeriodFrom
            ? new Date(data.invoicePeriodFrom)
            : null,
          invoicedUntil: data.invoicedUntil
            ? new Date(data.invoicedUntil)
            : null,
        }) as ContractFormSchema
      }
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
      headerSection={
        <Group>
          <EntityLayout.OwnerHeader
            setOwner={(userId: string) => {
              update({ ownerId: userId });
            }}
            ownerId={data.ownerId}
          />
        </Group>
      }
    />
  );
}
