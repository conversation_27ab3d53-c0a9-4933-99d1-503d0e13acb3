.titleText {
  color: var(--mantine-color-neutral-9);
  font-size: 0.875rem;
  font-weight: 400;
}

.displayText {
  color: var(--mantine-color-neutral-9);
  font-size: 1.25rem;
  font-weight: 700;
}

.footerText {
  color: var(--mantine-color-neutral-6);
  font-size: 0.75rem;
  font-weight: 400;
}

.footerBoldText {
  color: var(--mantine-color-neutral-6);
  font-size: 0.75rem;
  font-weight: 600;
}

.boldText {
  color: var(--mantine-color-neutral-9);
  font-size: 0.875rem;
  font-weight: 600;
}

.listContainer {
  margin-left: 16px;
  margin-right: 16px;
  margin-top: 32px;
  margin-bottom: 32px;
}

.listItem {
  padding: 16px;
  margin: 8px;
  background-color: var(--mantine-color-neutral-0);
  border-radius: 8px;
}

.noListItem {
  padding: 64px;
  margin: 8px;
  background-color: var(--mantine-color-neutral-0);
  border-radius: 8px;
}

.listItem:hover {
  cursor: pointer;
  background-color: var(--mantine-color-primary-1);
}

.listItemTitle {
  color: var(--mantine-color-neutral-9);
  font-size: 1rem;
  font-weight: 700;
}

.listItemLabel {
  color: var(--mantine-color-neutral-6);
  font-size: 0.75rem;
  font-weight: 400;
}

.listItemText {
  color: var(--mantine-color-neutral-9);
  font-size: 0.875rem;
  font-weight: 400;
}

.listNoItemsText {
  color: var(--mantine-color-neutral-6);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  font-style: italic;
}

.scrollArea {
  padding: 32px;
  border-radius: var(--mantine-radius-md);
  /* background-color: var(--mantine-color-neutral-0); */
  border: 1px solid var(--mantine-color-neutral-2);
}
