import type React from "react";
import { Flex, Grid } from "@mantine/core";
import HorizontalBarChart from "../common/HorizontalBarChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import CardChart from "../common/CardChart";

const metrics: MetricProps[] = [{ metric: "AllAppointments" }];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={12}>
      <Grid>
        <Grid.Col span={2}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <Card<PERSON>hart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={2}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
      </Grid>
    </Grid.Col>
    <Grid.Col span={12}>
      <Grid>
        <Grid.Col span={10}>
          <Grid>
            <Grid.Col span={2.4}>
              <HorizontalBarChart
                marginTop={10}
                data={data[0]}
                orientation="vertical"
                height="620"
                title="Total Leads Overview"
              />
            </Grid.Col>
            <Grid.Col span={2.4}>
              <HorizontalBarChart
                marginTop={10}
                data={data[0]}
                orientation="vertical"
                height="620"
                title="Total Leads Overview"
              />
            </Grid.Col>
            <Grid.Col span={2.4}>
              <HorizontalBarChart
                marginTop={10}
                data={data[0]}
                orientation="vertical"
                height="620"
                title="Total Leads Overview"
              />
            </Grid.Col>
            <Grid.Col span={2.4}>
              <HorizontalBarChart
                marginTop={10}
                data={data[0]}
                orientation="vertical"
                height="620"
                title="Total Leads Overview"
              />
            </Grid.Col>
            <Grid.Col span={2.4}>
              <HorizontalBarChart
                marginTop={10}
                data={data[0]}
                orientation="vertical"
                height="620"
                title="Total Leads Overview"
              />
            </Grid.Col>
          </Grid>
        </Grid.Col>
        <Grid.Col span={2}>
          <Flex
            justify="center"
            align="center"
            direction="column"
            wrap="nowrap"
          >
            <HorizontalBarChart
              marginTop={10}
              data={data[0]}
              orientation="vertical"
              height="470"
              title="Total Leads Overview"
            />
            <CardChart marginTop={30} height="120" value="0" title={"title"} />
          </Flex>
        </Grid.Col>
      </Grid>
    </Grid.Col>
  </Grid>
);

const ConversieDashboard: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default ConversieDashboard;
