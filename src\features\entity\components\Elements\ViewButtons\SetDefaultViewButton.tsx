import { IconEyeStar } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import ButtonMain from "../ButtonMain/ButtonMain";
import { type MRT_RowData, type MRT_TableInstance } from "mantine-react-table";
import { notifications } from "@mantine/notifications";
import { useTableStore } from "@/features/entity/stores/useTableStore";
import { type ViewOption } from "@/components/Table/types";

interface CustomTableInstance extends MRT_TableInstance<MRT_RowData> {
  activeView?: ViewOption;
}
interface CreateCustomViewButtonProps {
  tableRef: React.MutableRefObject<CustomTableInstance | null>;
}
type ActiveViewData = Record<string, string | null>;

export function SetDefaultViewButton({
  tableRef,
}: CreateCustomViewButtonProps) {
  const { t } = useTranslation("features");
  const viewValue = useTableStore((state) => state.viewValue);
  const activeViewData = JSON.parse(
    localStorage.getItem("activeView") || "{}",
  ) as ActiveViewData;

  // is view is default dont show the button
  if (activeViewData[document.location.pathname] == viewValue) {
    return <></>;
  }

  return (
    <ButtonMain
      label={t("entity.setDefaultView")}
      icon={<IconEyeStar size={18} />}
      onClick={() => {
        if (tableRef) {
          if (tableRef.current) {
            const newPathKey = document.location.pathname;
            const newPathValue = tableRef.current.activeView?.value ?? null;
            const activeViewData = JSON.parse(
              localStorage.getItem("activeView") || "{}",
            ) as ActiveViewData;
            activeViewData[newPathKey] = newPathValue;
            localStorage.setItem("activeView", JSON.stringify(activeViewData));
            notifications.show({
              color: "green",
              title: t("entity.setDefaultViewTitle"),
              message: t("entity.setDefaultViewSuccessMessage"),
            });
          }
        }
      }}
      color="#a5acf8"
      type="submit"
    />
  );
}
