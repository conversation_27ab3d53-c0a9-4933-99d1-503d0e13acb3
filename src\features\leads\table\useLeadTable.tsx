import {
  Date<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PhoneRenderer,
} from "@/components/Table/CellRenderers";
import { lowerCaseNthLetter } from "@/utils/filters";
import { type ComboboxData } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import {
  LeadSource,
  Priority5LeadSources,
  ProcessStage,
  StartWithin,
} from "@/types/enums";
import { recordState } from "@/features/entity/utils";
import { type Schemas } from "@/types";
import { PossiblePriorities } from "@/features/entity";
import { useLayoutVisibility } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import { ContractLookup } from "@/components/Lookup/Features/Contracts/ContractLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

interface LeadTable {
  visibleColumns?: string[];
  resourcePath: string;
}
type ActiveViewData = Record<string, string | null>;

const defaultVisibleColumns = [
  "firstName",
  "lastName",
  "owner",
  "email",
  "mobile",
  "existingContact",
  "priority",
  "businessUnit",
  "webformTitle",
  "webformDetails",
  "step",
  "processStage",
  "recordState",
  "startWithin",
  "createdOn",
  "price",
];

const callListFilter = [
  {
    id: "recordState",
    value: "Active",
  },
  {
    id: "nextCallback",
    value: [
      new Date(new Date("2020-01-01").setHours(0, 0, 0, 0)), // Start of today (00:00:00)
      new Date(new Date().setHours(24, 0, 0, 0)), // Start of the next day (00:00:00)
    ],
  },
  {
    id: "priority",
    value: ["5", "6", "7", "8", "9", "10"],
  },
  {
    id: "processStage",
    value: ["Appointment", "SpaceTour", "FollowUp"],
  },
];

const storeManagerHighPriority = [
  {
    id: "priority",
    value: ["5", "6", "7", "8", "9", "10"],
  },
  {
    id: "nextCallback",
    value: [
      new Date(new Date("2024-01-01").setHours(0, 0, 0, 0)), // Start of today (00:00:00)
      new Date(new Date().setHours(24, 0, 0, 0)), // Start of the next day (00:00:00)
    ],
  },
  {
    id: "recordState",
    value: "Active",
  },
  {
    id: "processStage",
    value: [
      "New",
      "Appointment",
      "SpaceTour",
      "FollowUp",
      "NotReached",
      "CallbackAppointment",
      "NoShow",
    ],
  },
  {
    id: "leadSource",
    value: Priority5LeadSources,
  },
];

const storeManagerFilter = [
  {
    id: "nextCallback",
    value: [
      new Date(new Date("2024-01-01").setHours(0, 0, 0, 0)), // Start of today (00:00:00)
      new Date(new Date().setHours(24, 0, 0, 0)), // Start of the next day (00:00:00)
    ],
  },
  {
    id: "recordState",
    value: "Active",
  },
  {
    id: "callCountNotReached",
    value: ["1", "2", "3", "4"],
  },
  {
    id: "processStage",
    value: ["Appointment", "SpaceTour", "FollowUp"],
  },
];

const salesServiceFilter = [
  {
    id: "priority",
    value: ["1", "2", "3", "4"],
  },
  {
    id: "recordState",
    value: "Active",
  },
  {
    id: "processStage",
    value: ["New", "NotReached", "CallbackAppointment"],
  },
  {
    id: "leadSource",
    value: [
      LeadSource.filter(
        (source) => source !== "PhoneCallAtBU" && source !== "WalkIn",
      ),
      ..."",
    ],
  },
  {
    id: "nextCallback",
    value: [
      new Date(new Date().setHours(0, 0, 0, 0)),
      new Date(new Date().setHours(24, 0, 0, 0)),
    ],
  },
];

export default function useLeadTable({
  visibleColumns,
  resourcePath,
}: LeadTable) {
  const { t } = useTranslation("features");
  const { activeNavbar } = useLayoutVisibility();
  const newPathKey = document.location.pathname;

  const activeViewData = JSON.parse(
    localStorage.getItem("activeView") || "{}",
  ) as ActiveViewData;

  const defaultView = activeViewData[newPathKey];

  const LeadColumns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "callCount",
      header: t("entity.callCount"),
      filterVariant: "range",
    },
    {
      accessorKey: "callCountNotReached",
      header: t("entity.callCountNotReached"),
      filterVariant: "multi-select",
    },
    {
      accessorKey: "nextCallback",
      header: t("entity.nextCallback"),
      sortDescFirst: true, // leave this here ~!
      filterVariant: "date-range",
      enableColumnFilter: false,
      Cell: DateRenderer,
    },
    {
      accessorKey: "firstName",
      header: t("leads.firstName"),
      sortDescFirst: true, // leave this here ~!
      filterVariant: "text",
      Cell: (props) => EntityLinkRenderer(props, "leads"),
    },
    {
      accessorKey: "lastName",
      header: t("leads.lastName"),
      filterVariant: "text",
      Cell: (props) => EntityLinkRenderer(props, "leads"),
    },
    {
      accessorKey: "email",
      header: t("leads.emailHeader"),
      filterVariant: "text",
      Cell: (props) => EntityLinkRenderer(props, "leads"),
    },
    {
      accessorKey: "mobile",
      header: t("leads.mobileHeader"),
      filterVariant: "text",
      Cell: PhoneRenderer,
    },
    {
      accessorKey: "existingContact",
      header: t("leads.existingContactId"),
      ...TableRenderer(ContactLookup, "contacts", ["lastName", "firstName"]),
    },
    {
      accessorKey: "priority",
      header: t("leads.priority"),
      filterVariant: "multi-select",
      Cell: ({ cell }) => {
        return cell.getValue() as string;
      },
      mantineFilterSelectProps: {
        data: PossiblePriorities,
      },
      enableColumnFilter: false,
      sortingFn: (a, b) => {
        const rowA: Schemas["Lead"] = a.original;
        const rowB: Schemas["Lead"] = b.original;
        const priorityA = rowA.priority ?? 0;
        const priorityB = rowB.priority ?? 0;
        return priorityA - priorityB;
      },
    },
    {
      accessorKey: "ownerId",
      header: t("Owner Id"),
      filterVariant: "text",
    },
    {
      accessorKey: "owner",
      header: t("Owner Name"),
      ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
    },
    {
      accessorKey: "contract",
      header: t("leads.contract"),
      ...TableRenderer(ContractLookup, "contracts", ["contractNumber"]),
    },
    {
      accessorKey: "businessUnit",
      header: t("leads.businessUnit"),
      ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
    },
    {
      accessorKey: "price",
      header: t("leads.price"),
      filterVariant: "text",
    },
    {
      accessorKey: "processStage",
      header: t("leads.processStageHeader"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: ProcessStage as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "startWithin",
      header: t("leads.startWithin"),
      filterVariant: "multi-select",
      Cell: ({ cell }) => {
        const cellValue = cell.getValue<string>();
        return cellValue ? t("leads." + lowerCaseNthLetter(cellValue)) : "";
      },
      mantineFilterSelectProps: {
        data: StartWithin as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "webformTitle",
      header: t("leads.webformTitle"),
      filterVariant: "text",
    },
    {
      accessorKey: "webformDetails",
      header: t("leads.webformDetails"),
      filterVariant: "text",
    },
    {
      accessorKey: "step",
      header: t("leads.step"),
      filterVariant: "text",
    },
    {
      accessorKey: "leadSource",
      header: t("leads.leadSourceHeader"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: LeadSource as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "recordState",
      header: t("leads.recordState"),
      filterVariant: "select",
      Cell: ({ cell }) =>
        t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: recordState as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "modifiedOn",
      header: t("entity.modifiedOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
  ];

  const isStoreManagerApp = activeNavbar === "Store Manager";
  const isSalesAndServiceApp = activeNavbar === "Sales & Service";
  const isSalesAndServiceSupervisorApp =
    activeNavbar === "Sales & Service Supervisor";

  const columns = visibleColumns ? visibleColumns : defaultVisibleColumns;

  const highPriorityStoreManagerView = {
    value: "HighPriority",
    label: "5+ Priority Leads",
    visibleColumns: ["callCount", "nextCallback", "leadSource", ...columns],
    filter: storeManagerHighPriority,
    default: undefined,
  };

  const CallListStoreManagerView = {
    value: "callListStoreManagerView",
    label: t("leadList.callListStoreManagerView"),
    filter: callListFilter,
    default: undefined,
  };

  const omnichannelOverviewView = {
    value: "OmnichannelOverview",
    label: "Omnichannel Overview",
    visibleColumns: ["callCount", "nextCallback", "leadSource", ...columns],
    filter: [],
    resourcePath: resourcePath + "/omnichannelOverview",
    default: undefined,
  };

  const baseViews = [
    {
      value: "Open",
      label: t("features:leads.openLeads"),
      visibleColumns: ["callCount", "nextCallback", "leadSource", ...columns],
      filter: isStoreManagerApp
        ? storeManagerFilter
        : isSalesAndServiceSupervisorApp
          ? salesServiceFilter
          : [],
      default: true,
    },
    {
      value: "All",
      label: t("features:leads.allLeads"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [],
      default: undefined,
    },
    {
      value: "Active",
      label: t("features:leads.activeLeads"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [{ id: "recordState", value: "Active" }],
      default: undefined,
    },
    {
      value: "ActivePriority5",
      label: t("features:leads.activeLeadsPriority5"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [
        { id: "recordState", value: "Active" },
        { id: "priority", value: ["5"] },
      ],
      default: undefined,
    },
    {
      value: "ActivePriority6",
      label: t("features:leads.activeLeadsPriority6"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [
        { id: "recordState", value: "Active" },
        { id: "priority", value: ["6"] },
      ],
      default: undefined,
    },
    {
      value: "ActivePriority7",
      label: t("features:leads.activeLeadsPriority7"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [
        { id: "recordState", value: "Active" },
        { id: "priority", value: ["7"] },
      ],
      default: undefined,
    },
    {
      value: "Inactive",
      label: t("features:leads.inactiveLeads"),

      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [{ id: "recordState", value: "Inactive" }],
      default: undefined,
    },
    {
      value: "snsNoShows",
      label: t("features:leads.noShows"),
      visibleColumns: [...columns],
      filter: [],
      resourcePath: resourcePath + "/salesNoShows",
      default: undefined,
    },
  ];

  const salesAndServiceViews = [
    {
      value: "ActivePriority2",
      label: t("features:leads.activeLeadsPriority2"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [
        { id: "recordState", value: "Active" },
        { id: "priority", value: ["2"] },
      ],
      default: true,
    },
    {
      value: "ActivePriority3",
      label: t("features:leads.activeLeadsPriority3"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [
        { id: "recordState", value: "Active" },
        { id: "priority", value: ["3"] },
      ],
      default: undefined,
    },
    {
      value: "ActivePriority4",
      label: t("features:leads.activeLeadsPriority4"),
      visibleColumns: ["callCount", "nextCallback", ...columns],
      filter: [
        { id: "recordState", value: "Active" },
        { id: "priority", value: ["4"] },
      ],
      default: undefined,
    },
  ];

  const views = [];
  if (isStoreManagerApp) {
    views.push(
      ...baseViews,
      highPriorityStoreManagerView,
      CallListStoreManagerView,
    );
  } else if (isSalesAndServiceApp) {
    views.push(...salesAndServiceViews);
  } else if (isSalesAndServiceSupervisorApp) {
    views.push(...baseViews, omnichannelOverviewView);
  } else {
    views.push(...baseViews);
  }

  if (views.length != 0) {
    views.forEach((view) => {
      if (defaultView == undefined) return;
      if (view.value == defaultView) {
        view.default = true;
      } else {
        view.default = false;
      }
    });
  }

  return {
    LeadColumns,
    defaultVisibleColumns,
    views,
  };
}
