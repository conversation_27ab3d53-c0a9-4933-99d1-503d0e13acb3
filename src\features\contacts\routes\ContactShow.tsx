import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { ContactForm } from "../components/ContactForm";
import { type SetNonNullable } from "type-fest";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function ContactShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") ?? "/app/contacts";
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Contact"],
    Schemas["ContactCreateDto"]
  >({
    resourcePath: "/api/Contacts/{id}",
    resourceId: id!,
    queryKey: "contact",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Contact"]>({
    resourcePath: "/api/Contacts/{id}",
    resourceId: id!,
    queryKey: "contact",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("contact_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <ContactForm
      isCreate={false}
      title={t("contacts.showTitle", { id })}
      initialValues={
        filterFalsyValues(data) as Required<SetNonNullable<Schemas["Contact"]>>
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values, {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
