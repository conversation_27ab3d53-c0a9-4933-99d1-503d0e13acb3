import { isValid, parseISO } from "date-fns";

/**
 * Checks if a value is a string that represents a valid ISO date.
 * Note: This is a basic check, adjust if your date formats vary significantly.
 */
export function isValidIsoDateString(value: unknown): value is string {
  if (typeof value !== "string") {
    return false;
  }
  // Basic check for ISO-like format before parsing
  if (!/^\d{4}-\d{2}-\d{2}/.test(value)) {
    return false;
  }
  return isValid(parseISO(value));
}

/**
 * Safely access a potentially nested property from an object.
 * Returns undefined if path is invalid or value is not found.
 */
export function getNestedProperty<T>(
  obj: T | null | undefined,
  path: string,
): unknown {
  if (obj == null || path.trim() === "") {
    return undefined;
  }

  return path.split(".").reduce<unknown>((acc, key) => {
    if (
      acc !== null &&
      acc !== undefined &&
      typeof acc === "object" &&
      key in acc
    ) {
      // acc is object-like, so index into it
      return (acc as Record<string, unknown>)[key];
    }
    return undefined;
  }, obj as unknown);
}
