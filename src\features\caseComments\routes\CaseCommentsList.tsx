import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { CaseCommentsColumns } from "../table/CaseCommentsColumns";
import { CaseCommentsColumnsCase } from "../table/CaseCommentsColumnsCase";
import { CaseCommentsCreate } from "./CaseCommentsCreate";

const PATH = "CaseComments";
export function CaseCommentsListInner({
  resourcePath,
  createPath,
  parentEntityId,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["CaseCommentRetrieveDto"],
        Schemas["CaseCommentRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="caseComment"
        entityPath="caseComments"
        title={t("caseComments.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton
              to={createPath}
              FormComponent={parentEntityId ? CaseCommentsCreate : undefined}
              formProps={
                parentEntityId
                  ? {
                      parentEntityId: parentEntityId,
                      redirectTo: "/app/caseComments",
                      usingModal: true,
                    }
                  : undefined
              }
            />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={
          window.location.pathname.includes("/cases/")
            ? CaseCommentsColumnsCase()
            : CaseCommentsColumns()
        }
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function CaseCommentsList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <CaseCommentsListInner
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
      />
    </ListCommandsProvider>
  );
}
