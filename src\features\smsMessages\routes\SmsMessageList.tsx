import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { DateRenderer } from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

const PATH = "SmsMessages";

export function SmsMessageListInner({ resourcePath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["SmsMessageRetrieveDto"],
        Schemas["SmsMessageRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="smsMessage"
        entityPath="smsMessages"
        title={t("smsMessages.title")}
        toolbar={<Group></Group>}
        pageSize={10}
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "externalId",
            header: t("smsMessages.externalId"),
            filterVariant: "text",
          },
          {
            accessorKey: "recipients",
            header: t("smsMessages.recipients"),
            filterVariant: "text",
          },
          {
            accessorKey: "body",
            header: t("smsMessages.body"),
            filterVariant: "text",
          },
          {
            accessorKey: "lead",
            header: t("smsMessages.lead"),
            ...TableRenderer(LeadLookup, "leads", ["lastName", "firstName"]),
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function SmsMessageList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <SmsMessageListInner
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
