import { IconEyePlus } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import ButtonMain from "../ButtonMain/ButtonMain";
import { type MRT_RowData, type MRT_TableInstance } from "mantine-react-table";
import { type ViewOption } from "@/components/Table/types";

interface CustomTableInstance extends MRT_TableInstance<MRT_RowData> {
  activeView?: ViewOption;
}

interface CreateCustomViewButtonProps {
  tableRef: React.MutableRefObject<CustomTableInstance | null>;
}
export function CreateCustomViewButton({
  tableRef,
}: CreateCustomViewButtonProps) {
  const { t } = useTranslation("features");

  return (
    <ButtonMain
      label={t("entity.createCustomView")}
      icon={<IconEyePlus size={18} />}
      onClick={() => {
        if (tableRef) {
          if (tableRef.current) {
            alert(tableRef.current.activeView?.value);
          }
        }
      }}
      color="#a5acf8"
      type="submit"
    />
  );
}
