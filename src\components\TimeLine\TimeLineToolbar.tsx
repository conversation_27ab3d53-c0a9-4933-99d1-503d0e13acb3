import { AppointmentCreate } from "@/features/appointments/routes/AppointmentCreate";
import { ContactMomentCreate } from "@/features/contactMoments/routes/ContactMomentCreate";
import LeadSendEmail from "@/features/leads/components/LeadSendEmail";
import LeadEventEntryCreate from "@/features/leadEventEntries/routes/LeadEventEntryCreate";
import { PhoneCallCreate } from "@/features/phoneCalls/routes/PhoneCallCreate";
import { type Schemas } from "@/types";
import {
  Flex,
  Menu,
  Button,
  rem,
  Modal,
  Box,
  MultiSelect,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import {
  IconPlus,
  IconCalendarClock,
  IconPhoneCall,
  IconPhoneOutgoing,
  IconMessageCircle,
  IconMailForward,
  IconWalk,
  IconArrowsVertical,
} from "@tabler/icons-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";

interface TimeLineToolbarProps {
  setFilter: (filter: string[] | null) => void;
  queryKey: string;
  timeLineEntityId: string;
  timeLineEntity: string;
  businessUnitId?: string;
  sendEmailTo?: string;
  toggleAllPanels: () => void;
}

export function TimeLineToolbar({
  setFilter,
  queryKey,
  timeLineEntity,
  timeLineEntityId,
  businessUnitId,
  sendEmailTo,
  toggleAllPanels,
}: TimeLineToolbarProps) {
  const { t } = useTranslation("features");
  const [selectedActivity, setSelectedActivity] = useState<
    Schemas["TimelineActivityTypeEnum"] | undefined
  >(undefined);
  const queryCache = useQueryClient();
  const refreshForm = async () => {
    await queryCache.invalidateQueries(`${queryKey}_list`);
  };
  const [opened, { open, close }] = useDisclosure(false);

  const [selectedTab, setSelectedTab] = useState<string[] | null>(null);

  return (
    <>
      <Flex
        justify="flex-start"
        align="center"
        direction="column"
        w={"100%"}
        my={16}
        gap={8}
      >
        <Flex justify="flex-start" gap="xs" direction="row" w={"100%"}>
          <Menu shadow="md">
            <Menu.Target>
              <Button variant="outline" size="xs">
                <IconPlus size={16} />
                <Box ml={8}>{t("common.createNewActivity")}</Box>
              </Button>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Label>{t("leads.activitiesDropDownLabel")}</Menu.Label>
              <Menu.Item
                title={t("leads.appointment")}
                leftSection={
                  <IconCalendarClock
                    style={{ width: rem(14), height: rem(14) }}
                  />
                }
                onClick={() => {
                  open();
                  setSelectedActivity("Appointment");
                }}
                component="a"
              >
                {t("leads.appointment")}
              </Menu.Item>
              <Menu.Item
                title={t("leads.phoneCall")}
                leftSection={
                  <IconPhoneCall style={{ width: rem(14), height: rem(14) }} />
                }
                onClick={() => {
                  open();
                  setSelectedActivity("PhoneCall");
                }}
                component="a"
              >
                {t("leads.phoneCall")}
              </Menu.Item>
              <Menu.Item
                title={t("leads.callback")}
                leftSection={
                  <IconPhoneOutgoing
                    style={{ width: rem(14), height: rem(14) }}
                  />
                }
                onClick={() => {
                  open();
                  setSelectedActivity("Callback");
                }}
                component="a"
              >
                {t("leads.callback")}
              </Menu.Item>
              <Menu.Item
                title={t("leads.contactMoment")}
                leftSection={
                  <IconMessageCircle
                    style={{ width: rem(14), height: rem(14) }}
                  />
                }
                onClick={() => {
                  open();
                  setSelectedActivity("ContactMoment");
                }}
                component="a"
              >
                {t("leads.contactMoment")}
              </Menu.Item>
              <Menu.Item
                title={t("leads.email")}
                leftSection={
                  <IconMailForward
                    style={{ width: rem(14), height: rem(14) }}
                  />
                }
                onClick={() => {
                  open();
                  setSelectedActivity("Email");
                }}
                component="a"
              >
                {t("leads.email")}
              </Menu.Item>
              <Menu.Item
                title={t("leads.walkIn")}
                leftSection={
                  <IconWalk style={{ width: rem(14), height: rem(14) }} />
                }
                onClick={() => {
                  open();
                  setSelectedActivity("LeadEvent");
                }}
                component="a"
              >
                {t("leads.walkIn")}
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          <MultiSelect
            value={selectedTab ? selectedTab : []}
            onChange={(values) => {
              setFilter(values.length > 0 ? values : null);
              setSelectedTab(values.length > 0 ? values : null);
            }}
            data={[
              { label: t("common.appointments"), value: "Appointment" },
              { label: t("common.phoneCalls"), value: "PhoneCall" },
              { label: t("common.callbacks"), value: "Callback" },
              { label: t("common.smsMessages"), value: "Sms" },
              { label: t("common.emails"), value: "Email" },
              { label: t("common.webForms"), value: "WebForm" },
              { label: t("common.leadEvents"), value: "LeadEvent" },
              { label: t("common.contactMoments"), value: "ContactMoment" },
            ]}
            checkIconPosition="right"
            size="xs"
            clearable
            searchable
            style={{ maxWidth: "100%" }}
            placeholder={t("common.filterActivities")}
          />

          <Button
            variant="outline"
            size="xs"
            rightSection={<IconArrowsVertical size={18} />}
            onClick={toggleAllPanels}
            style={{ cursor: "pointer" }}
          >
            {t("leads.dropdownAllTooltipLabel")}
          </Button>
        </Flex>
      </Flex>
      {opened && (
        <Modal
          centered
          withCloseButton={false}
          size="60%"
          opened={opened}
          onClose={close}
          overlayProps={{
            backgroundOpacity: 0.55,
            blur: 3,
          }}
        >
          {selectedActivity === "Appointment" ? (
            <AppointmentCreate
              refreshForm={refreshForm}
              closeModal={() => {
                close();
              }}
              isModal={true}
              leadId={timeLineEntity == "Leads" ? timeLineEntityId : undefined}
              businessUnitId={
                timeLineEntity == "Leads" ? businessUnitId! : undefined
              }
              startDate={new Date().toISOString()}
              endDate={new Date(
                new Date().getTime() + 15 * 60 * 1000,
              ).toISOString()}
            />
          ) : selectedActivity === "PhoneCall" ? (
            <PhoneCallCreate
              refreshForm={refreshForm}
              closeModal={() => {
                close();
              }}
              isModal={true}
              leadId={timeLineEntity == "Leads" ? timeLineEntityId : undefined}
              businessUnitId={
                timeLineEntity == "Leads" ? businessUnitId! : undefined
              }
              startDate={new Date().toISOString()}
            />
          ) : selectedActivity === "Callback" ? (
            <PhoneCallCreate
              refreshForm={refreshForm}
              closeModal={() => {
                close();
              }}
              isModal={true}
              isCallback={true}
              leadId={timeLineEntity == "Leads" ? timeLineEntityId : undefined}
              businessUnitId={
                timeLineEntity == "Leads" ? businessUnitId! : undefined
              }
              startDate={new Date().toISOString()}
            />
          ) : selectedActivity === "ContactMoment" ? (
            <ContactMomentCreate
              refreshForm={refreshForm}
              closeModal={() => {
                close();
              }}
              isModal={true}
              leadId={timeLineEntity == "Leads" ? timeLineEntityId : undefined}
            />
          ) : selectedActivity === "LeadEvent" ? (
            <LeadEventEntryCreate
              eventType="WalkIn"
              triggerSource="User"
              close={close}
              refreshForm={refreshForm}
              leadId={timeLineEntityId}
            ></LeadEventEntryCreate>
          ) : selectedActivity === "Email" ? (
            <LeadSendEmail
              leadId={timeLineEntityId}
              businessUnitId={businessUnitId!}
              preffiledTo={sendEmailTo}
            />
          ) : null}
        </Modal>
      )}
    </>
  );
}
