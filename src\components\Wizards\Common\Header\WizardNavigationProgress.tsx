import { Box, Progress } from "@mantine/core";

interface WizardNavigationProgressProps {
  currentPageIndex: number;
  totalPages: number;
}

export default function WizardNavigationProgress({
  currentPageIndex,
  totalPages,
}: WizardNavigationProgressProps) {
  const progressValue = (currentPageIndex / totalPages) * 100;
  return (
    <Box w={"70%"}>
      <Progress
        animated={progressValue > 98 ? true : false}
        value={progressValue}
        size={"md"}
        style={{ borderRadius: 8 }}
        color="primary"
      />
    </Box>
  );
}
