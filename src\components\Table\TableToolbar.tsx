import { Flex, Group, Select, TextInput } from "@mantine/core";
import {
  MRT_ShowHideColumnsButton,
  MRT_ToggleFullScreenButton,
} from "mantine-react-table";
import { IconChevronDown } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { type TableToolbarProps } from "./types";
import viewClasses from "./modules/TableMantine.module.css";
import titleClasses from "./modules/TableMantineTitle.module.css";
import searchClasses from "./modules/TableMantineSearch.module.css";

export function TableToolbar({
  viewOptions,
  activeView,
  onViewChange,
  title,
  searchTerm,
  onSearchChange,
  searchDisabled = false,
  table,
  customToolbarContent,
}: TableToolbarProps) {
  const { t } = useTranslation("features"); // Assuming 'features' namespace

  return (
    <Group w={"100%"} justify="space-between" grow m={4}>
      <Flex
        justify="flex-start"
        align="center"
        direction="row"
        wrap="nowrap"
        gap={"xs"}
      >
        {viewOptions && viewOptions.length > 0 ? (
          <Select
            rightSection={<IconChevronDown size={16} />}
            leftSection={<></>}
            leftSectionPointerEvents="none"
            classNames={viewClasses}
            data={viewOptions}
            value={activeView?.value} // Controlled component
            allowDeselect={false}
            onChange={onViewChange} // Directly pass the handler
            disabled={!viewOptions || viewOptions.length === 0} // Disable if no options
          />
        ) : (
          <Select // Display Title if no viewOptions
            rightSection={<></>}
            leftSection={<></>}
            classNames={titleClasses}
            data={[{ value: title ?? "", label: title ?? "" }]}
            defaultValue={title}
            readOnly
            allowDeselect={false}
          />
        )}
        {!searchDisabled && (
          <TextInput
            size="xs"
            placeholder={t("common.searchPlaceholder")}
            value={searchTerm}
            classNames={searchClasses}
            onChange={(e) => onSearchChange(e.currentTarget.value)}
          />
        )}
      </Flex>
      <Flex
        gap={"xs"}
        justify="right"
        align="center"
        direction="row"
        wrap="wrap"
      >
        {customToolbarContent} {/* Render custom toolbar elements */}
        <MRT_ShowHideColumnsButton table={table} />
        <MRT_ToggleFullScreenButton table={table} mr={20} />
      </Flex>
    </Group>
  );
}
