import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { RefundForm } from "../components/RefundForm";
import { EntityLayout } from "@/features/entity";
import { Group, Loader } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";
import { api } from "@/lib/api";
import { deleteFile } from "@/utils/deleteFile";

interface Attachment {
  attachmentType: Schemas["AttachmentTypeEnum"];
  file: File;
}

export function RefundCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Refund"],
    Schemas["RefundCreateDto"]
  >({ resourcePath: "/api/Refunds", queryKey: "refund" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const caseId = searchParams.get("caseId");
  const redirectTo = searchParams.get("redirectTo") ?? "/app/refunds";
  const {
    data: caseRecord = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Case"]>({
    resourcePath: "/api/Cases/{id}",
    resourceId: caseId!,
    queryKey: "case",
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <RefundForm
      initialValues={{
        caseId: caseId ?? "",
        case: caseRecord,
        contactId: caseRecord.contactId ?? "",
        contact: caseRecord.contact ?? null,
      }}
      isCreate={true}
      title={t("refunds.createTitle")}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values, attachments, deleteAttachments) => {
        const filteredValues = filterFalsyValues(values);
        mutate(filteredValues, {
          onSuccess: async (data) => {
            try {
              await AddAttachments(data.data.id!, attachments, "Refunds");
              await HandleDeleteAttachments(deleteAttachments);

              if (close) {
                navigate(redirectTo);
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
                let navigateTo = `/app/refunds/${data.data.id}`;
                navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
                navigate(navigateTo);
              }
            } catch (error) {
              console.error("Error uploading attachments:", error);
            }
          },
        });
      }}
    />
  );
}

export async function AddAttachments(
  entityId: string,
  attachments: Attachment[],
  entity: string,
) {
  const url = `/api/${entity}/${entityId}/attachments`;

  if (!attachments || attachments.length === 0) {
    return;
  }

  const uploadPromises = attachments.map((attachment) => {
    const formData = new FormData();
    formData.append("File", attachment.file);
    formData.append("AttachmentType", attachment.attachmentType.toString()); // Convert enum to string

    return api.post(url, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  });

  try {
    const responses = await Promise.all(uploadPromises);

    if (responses.some((res) => res.status !== 200)) {
      throw new Error(`Some requests failed. Check server logs.`);
    }

    return responses;
  } catch (error) {
    console.error("Error in AddAttachments:", error);
    throw error;
  }
}

export async function HandleDeleteAttachments(ids: string[]) {
  try {
    await Promise.all(ids.map((id) => deleteFile(id)));
  } catch (error) {
    console.error("Error deleting files:", error);
  }
}
