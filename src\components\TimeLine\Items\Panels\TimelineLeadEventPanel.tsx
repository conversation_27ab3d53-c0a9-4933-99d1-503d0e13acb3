import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Box, Center, Flex, Loader, Stack } from "@mantine/core";
import { DateTime } from "luxon";
import { useTranslation } from "react-i18next";
interface TimelineLeadEventPanelProps {
  leadEventId: string;
}

export function TimelineLeadEventPanel({
  leadEventId,
}: TimelineLeadEventPanelProps) {
  const { t } = useTranslation("features");
  const { dateFormat } = useSettingsContext();
  const { data, isLoading } = useEntityQuery<
    Schemas["LeadEventEntryRetrieveDto"]
  >({
    resourcePath: `/api/LeadEventEntries/{id}`,
    resourceId: leadEventId,
    queryKey: ["leadEvent", leadEventId],
  });

  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <Box ml={8}>
      <Stack gap="xs">
        <Flex>
          {t("common.createdOn")}:
          <Box ml={8}>
            {DateTime.fromJSDate(new Date(data?.createdOn ?? "")).toFormat(
              dateFormat + " HH:mm:ss",
            )}
          </Box>
        </Flex>
        <Flex>
          {t("leadEventEntries.triggeredBy")}:
          <Box ml={8}>{data?.leadEventTriggerSource}</Box>
        </Flex>
      </Stack>
    </Box>
  );
}
