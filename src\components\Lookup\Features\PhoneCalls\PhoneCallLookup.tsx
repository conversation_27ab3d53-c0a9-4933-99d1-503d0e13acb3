import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { t } from "i18next";

const ENTITY = "phoneCalls";
type ENTITY_TYPE = Schemas["PhoneCallRetrieveDto"];

type PhoneCallLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function PhoneCallLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  ...props
}: PhoneCallLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.startDate,
    initialId,
    initial,
    true,
  );

  const queryParams: QueryParams = {
    pageSize: 50,
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["PhoneCallRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/PhoneCalls",
      queryKey: "phoneCall",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.startDate")}</Box>
      <Box>{t("common:lookup.status")}</Box>
    </React.Fragment>
  );
  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.startDate, searchTerm, `startDate_${entity.id}`),
        OptionRender(
          entity.phoneCallStatus,
          searchTerm,
          `phoneCallStatus_${entity.id}`,
        ),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.startDate!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
