import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { EntityLinkRenderer } from "@/components/Table/CellRenderers";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
const PATH = "Emails";

export function EmailListInner({
  visibleColumns,
  resourcePath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["EmailRetrieveDto"],
        Schemas["EmailRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        title={t("emails.title")}
        resourcePath={resourcePath as PathKeys}
        redirectTo={window.location.pathname}
        queryKey="email"
        entityPath="emails"
        visibleColumns={visibleColumns}
        columns={[
          {
            accessorKey: "to",
            header: t("emails.to"),
            filterVariant: "text",
          },
          {
            accessorKey: "from",
            header: t("emails.from"),
            filterVariant: "text",
          },
          {
            accessorKey: "cc",
            header: t("emails.cc"),
            filterVariant: "text",
          },
          {
            accessorKey: "subject",
            header: t("emails.subject"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "emails"),
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function EmailList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <EmailListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
