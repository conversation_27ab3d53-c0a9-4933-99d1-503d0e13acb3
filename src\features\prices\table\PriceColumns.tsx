import { DateRenderer } from "@/components/Table/CellRenderers";
import { lowerCaseNthLetter } from "@/utils/filters";
import { type ComboboxData } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { recordState } from "@/features/entity/utils";

export function PriceColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "pricePerMonth",
      header: t("prices.pricePerMonth"),
      filterVariant: "range",
    },
    {
      accessorKey: "minPrice",
      header: t("prices.minPrice"),
      filterVariant: "range",
    },
    {
      accessorKey: "maxPrice",
      header: t("prices.maxPrice"),
      filterVariant: "range",
    },
    {
      accessorKey: "recordState",
      header: t("prices.recordState"),
      filterVariant: "select",
      Cell: ({ cell }) =>
        t("prices." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: recordState as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "from",
      header: t("prices.from"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "to",
      header: t("prices.to"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "modifiedOn",
      header: t("entity.modifiedOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
  ];
  return columns;
}
