import { createContext, useContext, useState, type ReactNode } from "react";

interface LookupState<T> {
  searchTerm: string;
  lookupValue: string | null;
  lookupId: string | null;
  lookupEntity: T | null;
}

interface LookupContextType<T = unknown> {
  entities: Record<string, LookupState<T> | undefined>;
  updateEntity: (entity: string, newState: Partial<LookupState<T>>) => void;
}

const LookupContext = createContext<LookupContextType<unknown> | undefined>(
  undefined,
);

export function LookupProvider({ children }: { children: ReactNode }) {
  const [entities, setEntities] = useState<
    LookupContextType<unknown>["entities"]
  >({});
  const updateEntity = (
    entity: string,
    newState: Partial<LookupState<unknown>>,
  ) => {
    setEntities((prev) => {
      const oldLookupState = prev[entity] || {
        searchTerm: "",
        lookupValue: null,
        lookupId: null,
        lookupEntity: null,
      };
      return {
        ...prev,
        [entity]: { ...oldLookupState, ...newState },
      };
    });
  };
  return (
    <LookupContext.Provider value={{ entities, updateEntity }}>
      {children}
    </LookupContext.Provider>
  );
}

// eslint-disable-next-line react-refresh/only-export-components
export function useLookupContext<T = unknown>() {
  const context = useContext(LookupContext);
  if (!context) {
    throw new Error("useLookupContext must be used within a <LookupProvider>.");
  }
  return context as LookupContextType<T>;
}
