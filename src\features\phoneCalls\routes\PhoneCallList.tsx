import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { type Schemas } from "@/types";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function PhoneCallList() {
  const { t } = useTranslation("features");
  return (
    <EntityLayout showBackButton={false}>
      {
        <EntityLayout.TableMantine<
          Schemas["PhoneCallRetrieveDto"],
          Schemas["PhoneCallRetrieveDtoPagedList"]
        >
          resourcePath="/api/PhoneCalls"
          queryKey="phoneCallList"
          entityPath="phoneCalls"
          columns={[
            {
              accessorKey: "description",
              header: t("phoneCalls.description"),
              filterVariant: "text",
              Cell: (props) => Entity<PERSON>ink<PERSON>enderer(props, "phoneCalls"),
            },
            {
              accessorKey: "lead",
              header: t("phoneCalls.lead"),
              ...TableRenderer(LeadLookup, "leads", ["lastName", "firstName"]),
            },
            {
              accessorKey: "businessUnit",
              header: t("phoneCalls.businessUnit"),
              ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
            },
            {
              accessorKey: "createdOn",
              header: t("entity.createdOn"),
              sortingFn: "datetime",
              filterVariant: "date-range",
              Cell: DateRenderer,
            },
          ]}
          initialSorting={[
            {
              id: "createdOn",
              desc: true,
            },
          ]}
        />
      }
    </EntityLayout>
  );
}
