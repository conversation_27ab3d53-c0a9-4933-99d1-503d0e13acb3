import {
  createBrowserRouter,
  createRoutesFromElements,
  Route,
  useRoutes,
} from "react-router-dom";
import { useAuth0 } from "@auth0/auth0-react";
import { PageLoader } from "@/components/PageLoader";
import { protectedRoutes } from "./protected";
import { publicRoutes } from "./public";
import { LookupProvider } from "@/components/Lookup/Context/LookupContext";
import { ErrorFallback } from "@/components/ErrorFallback";
import { SettingsContextProvider } from "@/components/Layout/Contexts/Settings/SettingsContext";
import { LayoutVisibilityProvider } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import { useRouteBlocker } from "@/hooks/blockerContext";
import { useNavigationBlocker } from "@/hooks/useNavigationBlocker";

export function AppRoutes() {
  const { isLoading } = useAuth0();
  const { unsaved } = useRouteBlocker();

  const routes = [...publicRoutes, ...protectedRoutes];

  const element = useRoutes(routes);

  useNavigationBlocker(unsaved);

  if (isLoading) {
    return <PageLoader />;
  }
  return (
    <SettingsContextProvider>
      <LayoutVisibilityProvider>
        <LookupProvider>{element}</LookupProvider>
      </LayoutVisibilityProvider>
    </SettingsContextProvider>
  );
}

// eslint-disable-next-line react-refresh/only-export-components
export const router = createBrowserRouter(
  createRoutesFromElements(
    <Route path="*" element={<AppRoutes />} errorElement={<ErrorFallback />} />,
  ),
);
