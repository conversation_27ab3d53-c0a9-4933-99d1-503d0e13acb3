import { type Schemas } from "@/types";
import { Accordion, Anchor, Box, Flex, Modal, rem, Text } from "@mantine/core";
import { IconPhoneCall, IconPhoneOutgoing } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { TimelinePhoneCallPanel } from "./Panels/TimelinePhoneCallPanel";
import { DateTime } from "luxon";
import { PhoneCallShow } from "@/features/phoneCalls/routes/PhoneCallShow";
import { useDisclosure } from "@mantine/hooks";
import { lowerCaseNthLetter } from "@/utils/filters";

interface TimelinePhoneCallProps {
  activity: Schemas["TimelineRetrieveDto"];
  openActivities: string[];
}
export function TimelinePhoneCall({
  activity,
  openActivities,
}: TimelinePhoneCallProps) {
  const { t } = useTranslation("features");
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] =
    useDisclosure(false);

  function lowerCaseFirstLetter(value: string) {
    if (!value) return value;
    return value.charAt(0).toLowerCase() + value.slice(1);
  }

  const isAccordionOpen = openActivities.includes(activity.id ?? "");
  const createdOn = DateTime.fromJSDate(new Date(activity.createdOn ?? ""));

  return (
    <>
      <Modal
        centered
        withCloseButton={true}
        size="60%"
        opened={editModalOpened}
        onClose={closeEditModal}
        overlayProps={{
          backgroundOpacity: 0.55,
          blur: 3,
        }}
      >
        <PhoneCallShow phoneCallId={activity.id} closeModal={closeEditModal} />
      </Modal>
      <Accordion.Item
        value={activity.id ?? ""}
        style={{ backgroundColor: "#fff" }}
      >
        <Accordion.Control>
          <Flex justify="space-between" align="center" direction="row">
            <Text mr={4} fw={600} size={"xs"} style={{ whiteSpace: "nowrap" }}>
              {createdOn.toFormat("HH:mm:ss")}
            </Text>
            <Anchor
              style={{ cursor: "pointer" }}
              onClick={(event) => {
                event.stopPropagation();
                openEditModal();
              }}
            >
              <Flex align="center">
                {activity.timelineActivityType === "Callback" ? (
                  <IconPhoneOutgoing
                    style={{
                      width: rem(15),
                      height: rem(15),
                      marginRight: rem(5),
                    }}
                  />
                ) : (
                  <IconPhoneCall
                    style={{
                      width: rem(15),
                      height: rem(15),
                      marginRight: rem(5),
                    }}
                  />
                )}
                {t(
                  "leads." +
                    lowerCaseFirstLetter(
                      activity.timelineActivityType as string,
                    ),
                ) + " "}
                {activity.displayTitle}
              </Flex>
            </Anchor>
            <Box ml="auto" style={{ whiteSpace: "nowrap" }}>
              {t("phoneCalls." + lowerCaseNthLetter(activity.status ?? ""))}
            </Box>
          </Flex>
        </Accordion.Control>

        <Accordion.Panel>
          {isAccordionOpen && (
            <TimelinePhoneCallPanel phoneCallId={activity.id!} />
          )}
        </Accordion.Panel>
      </Accordion.Item>
    </>
  );
}
