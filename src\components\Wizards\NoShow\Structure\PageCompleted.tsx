import { Box, Text, Center } from "@mantine/core";
import { IconCircleCheck } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

export default function PageCompleted() {
  const { t } = useTranslation("features");
  return (
    <Box>
      <Center mt={40}>
        <IconCircleCheck width={128} height={128} color="#86D5A0" />
      </Center>
      <Center>
        <Text fz={24} fw={600} c={"#282828"}>
          {t("wizards.Completed.Title")}
        </Text>
      </Center>
      <Center>
        <Text fz={16} fw={300} c={"#ADADAD"}>
          {t("wizards.Completed.Label")}
        </Text>
      </Center>
    </Box>
  );
}
