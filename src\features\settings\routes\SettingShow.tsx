import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { SettingForm } from "../components/SettingForm";
import { type SetNonNullable } from "type-fest";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function SettingShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Setting"],
    Schemas["SettingCreateDto"]
  >({
    resourcePath: "/api/Settings/{id}",
    resourceId: id!,
    queryKey: "setting",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Setting"]>({
    resourcePath: "/api/Settings/{id}",
    resourceId: id!,
    queryKey: "setting",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("setting_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <SettingForm
      isCreate={false}
      title={t("settings.showTitle", { id })}
      initialValues={
        filterFalsyValues(data) as Required<SetNonNullable<Schemas["Setting"]>>
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/settings");
          } else {
            return;
          }
        }
        update(values, {
          onSuccess: () => {
            if (close) {
              navigate("/app/settings");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
