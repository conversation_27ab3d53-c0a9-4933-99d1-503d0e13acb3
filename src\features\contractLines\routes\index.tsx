import { Route, Routes } from "react-router-dom";
import { ContractLineList } from "./ContractLineList";
import { ContractLineShow } from "./ContractLineShow";
import { ContractLineCreate } from "./ContractLineCreate";

export default function ContractLinesRoutes() {
  return (
    <Routes>
      <Route index element={<ContractLineList />} />
      <Route path=":id" element={<ContractLineShow />} />
      <Route path="create" element={<ContractLineCreate />} />
    </Routes>
  );
}
