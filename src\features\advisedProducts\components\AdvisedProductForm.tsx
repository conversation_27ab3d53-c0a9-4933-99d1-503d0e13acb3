import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, NumberInput, Paper } from "@mantine/core";
import { useEffect, useState, type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { ProductLookup } from "@/components/Lookup/Features/Products/ProductLookupField";
import { QuoteLookup } from "@/components/Lookup/Features/Quotes/QuoteLookupField";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";

const formSchema = z.object({
  price: z.coerce.number().nullable(),
  totalPrice: z.coerce.number().nullable(),
  quantity: z.coerce.number().nullable(),
  leadId: z.string().nullable(),
  lead: z.object({}).nullable(),
  productId: z.string().nullable(),
  product: z.object({}).nullable(),
  quoteId: z.string().nullable(),
  quote: z.object({}).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface AdvisedProductFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function AdvisedProductForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: AdvisedProductFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const [isPriceEditable, setIsPriceEditable] = useState(false); // Tracks whether the price field is enabled
  const form = useForm<FormSchema>({
    initialValues: {
      price: initialValues?.price ?? null,
      totalPrice: initialValues?.totalPrice ?? null,
      quantity: initialValues?.quantity ?? 1,
      leadId: initialValues?.leadId ?? "",
      lead: initialValues?.lead ?? null,
      productId: initialValues?.productId ?? "",
      product: initialValues?.product ?? null,
      quoteId: initialValues?.quoteId ?? "",
      quote: initialValues?.quote ?? null,
    },
    validate: zodResolver(formSchema),
  });

  const { data } = useEntityQuery<Schemas["ProductRetrieveDto"]>({
    resourcePath: `/api/Products/{id}`,
    resourceId: form.values.productId ?? "",
    queryKey: ["product", form.values.productId],
  });

  useEffect(() => {
    if (data?.allowPriceChange) {
      setIsPriceEditable(true);
      form.setValues({
        price: 0,
      });
    } else {
      setIsPriceEditable(false);
      form.setValues({
        price: 0,
      });
    }
    if (form.values.quantity) {
      if (data?.price && !form.values.price) {
        form.setValues({
          price: data.price,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.allowPriceChange, data?.price, form.values.productId]);

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
        disabled={form.getValues().quote !== null}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="lg">
              <ProductLookup
                label={t("advisedProducts.product")}
                initial={form.getValues().product}
                initialId={form.getValues().productId}
                identifier="productIdAdvisedProduct"
                mt="sm"
                {...form.getInputProps("productId")}
              />
              <NumberInput
                mt="sm"
                label={t("advisedProducts.quantity")}
                defaultValue={1}
                min={1}
                {...form.getInputProps("quantity")}
              />
              <NumberInput
                mt="sm"
                disabled={!isPriceEditable}
                label={t("advisedProducts.price")}
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("price")}
              />
              <NumberInput
                mt="sm"
                disabled
                label={t("advisedProducts.totalPrice")}
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("totalPrice")}
              />
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="lg">
              <LeadLookup
                mt="sm"
                label={t("advisedProducts.lead")}
                initial={form.getValues().lead}
                initialId={form.getValues().leadId}
                identifier="leadIdAdvisedProduct"
                {...form.getInputProps("leadId")}
              />
              <QuoteLookup
                disabled
                mt="sm"
                label={t("advisedProducts.quote")}
                initial={form.getValues().quote}
                initialId={form.getValues().quoteId}
                identifier="quoteIdAdvisedProduct"
                {...form.getInputProps("quoteId")}
              />
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
