import type React from "react";
import { Grid } from "@mantine/core";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import DonutChart from "../common/DonutChart";
import CardChart from "../common/CardChart";

const metrics: MetricProps[] = [{ metric: "AllAppointments" }];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={12}>
      <Grid>
        <Grid.Col span={3}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <Card<PERSON>hart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
      </Grid>
    </Grid.Col>
    <Grid.Col span={12}>
      <Grid>
        <Grid.Col span={3}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <CardChart marginTop={10} height="120" value="0" title={"title"} />
        </Grid.Col>
      </Grid>
    </Grid.Col>
    <Grid.Col span={12}>
      <Grid>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
      </Grid>
    </Grid.Col>
    <Grid.Col span={12}>
      <Grid>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
        <Grid.Col span={3}>
          <DonutChart height={100} data={data[0]} title={"title"} />
        </Grid.Col>
      </Grid>
    </Grid.Col>
  </Grid>
);

const LandelijkOverzicht: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default LandelijkOverzicht;
