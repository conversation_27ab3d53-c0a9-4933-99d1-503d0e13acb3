.root {
  margin-left: 0px;
  margin-right: 20px;
}

.item {
  transition: transform 150ms ease;
  width: 100%;
}

.controlRoot {
  background-color: white;
}

.controlRoot:hover {
  background-color: var(--mantine-color-gray-0);
  cursor: pointer;
}

.control {
}

.control:hover {
  background-color: var(--mantine-color-gray-0);
}

.chevron {
  &[data-rotate] {
    transform: rotate(360deg);
  }
}

.label {
}

.icon {
}

.itemTitle {
}

.panel {
}

.content {
}

.scrollbar {
  &[data-orientation="vertical"] .thumb {
    background-color: var(--mantine-color-primary-6);
  }

  direction: rtl;
  &[data-orientation="horizontal"] .thumb {
    background-color: var(--mantine-color-primary-6);
  }
}
