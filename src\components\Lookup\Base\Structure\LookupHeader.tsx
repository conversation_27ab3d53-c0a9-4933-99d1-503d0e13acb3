/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
import React, {
  type ReactElement,
  type CSSProperties,
  cloneElement,
} from "react";

interface ChildProps {
  style?: CSSProperties;
  [key: string]: unknown; // or more specific props if needed
}

function LookupHeader({ header }: { header: ReactElement }) {
  return (
    <>
      {React.Children.map(header.props.children, (child: ReactElement) => {
        if (!React.isValidElement<ChildProps>(child)) {
          return child;
        }

        const existingStyle = child.props.style || {};
        const hasFlex = existingStyle.flex !== undefined;
        const newStyle: CSSProperties = hasFlex
          ? existingStyle
          : { ...existingStyle, flex: 1 };

        // Clone the element, overriding style
        return cloneElement<ChildProps>(child, {
          style: newStyle,
        });
      })}
    </>
  );
}

export default LookupHeader;
