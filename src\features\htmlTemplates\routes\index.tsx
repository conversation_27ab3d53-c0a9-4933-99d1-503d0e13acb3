import { Route, Routes } from "react-router-dom";
import { HtmlTemplateList } from "./HtmlTemplateList";
import { HtmlTemplateShow } from "./HtmlTemplateShow";
import { HtmlTemplateCreate } from "./HtmlTemplateCreate";

export default function HtmlTemplatesRoutes() {
  return (
    <Routes>
      <Route index element={<HtmlTemplateList />} />
      <Route path=":id" element={<HtmlTemplateShow />}>
        <Route path=":tabValue" element={<HtmlTemplateShow />} />
      </Route>
      <Route path="create" element={<HtmlTemplateCreate />} />
    </Routes>
  );
}
