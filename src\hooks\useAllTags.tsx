import { useEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";

export default function UseAllTags() {
  const {
    data: allTagsResponse,
    isLoading: allTagsLoading,
    refetch: refetchAllTags,
  } = useEntityListQuery<Schemas["TagRetrieveDtoPagedList"]>({
    resourcePath: "/api/Tags",
    queryKey: "allTags",
  });

  return {
    allTagsResponse,
    allTagsLoading,
    refetchAllTags,
  };
}
