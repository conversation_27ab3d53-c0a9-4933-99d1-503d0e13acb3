import { useEntityListQuery } from "@/features/entity/queries";
import UseUserBusinessUnit from "@/hooks/businessUnit/useUserBusinessUnit";
import { type Schemas } from "@/types";

export default function CallbackQuery() {
  const start = new Date();
  start.setUTCHours(0, 0, 0, 0);
  const end = new Date();
  end.setUTCHours(23, 59, 59, 999);
  const { userBusinessUnitId } = UseUserBusinessUnit();
  const now = new Date();

  const filterOpen = [
    userBusinessUnitId ? `lead.businessUnitId == ${userBusinessUnitId}` : null,
    "phoneCallType == Callback",
    "phoneCallStatus == Open",
    "lead.recordState == Active",
    "(lead.processStage == Appointment || lead.processStage == SpaceTour || lead.processStage == FollowUp || lead.processStage == NoShow)",
    `startDate <= ${end.toISOString().replace("Z", "")}`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data } = useEntityListQuery<Schemas["PhoneCallRetrieveDtoPagedList"]>(
    {
      resourcePath: "/api/PhoneCalls",
      params: {
        filter: filterOpen,
        orderBy: "startDate",
      },
      queryKey: `callbackListHome`,
    },
  );

  const filterReached = [
    userBusinessUnitId ? `lead.businessUnitId == ${userBusinessUnitId}` : null,
    "phoneCallType == Callback",
    "phoneCallStatus == Reached",
    `modifiedOn <= ${end.toISOString().replace("Z", "")}`,
    `modifiedOn >= ${start.toISOString().replace("Z", "")}`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data: reachedCallbacks } = useEntityListQuery<
    Schemas["PhoneCallRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/PhoneCalls",
    params: {
      filter: filterReached,
    },
    queryKey: `callbackListHome`,
  });

  const totalCount = data?.totalCount;
  const totalReached = reachedCallbacks?.totalCount;

  const todaysCallbacks = (data?.data ?? [])
    .filter((callback) => {
      const startDate = callback.startDate ?? "";
      return startDate > start.toISOString();
    })
    .reverse();

  const missedCallbacks = (data?.data ?? []).filter((callback) => {
    const startDate = callback.startDate ?? "";
    return startDate < now.toISOString() && startDate < start.toISOString();
  });

  return { totalCount, totalReached, todaysCallbacks, missedCallbacks };
}
