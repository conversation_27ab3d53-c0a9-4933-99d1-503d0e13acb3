import { useState, type <PERSON>ps<PERSON>ith<PERSON>hildren } from "react";
import "@/lib/i18n";
import { api } from "@/lib/api";
import { NotFound } from "@/routes/NotFound";
import { Forbidden } from "@/routes/Forbidden/Forbidden";

interface ErrorResponse {
  response: {
    status: number;
  };
}

export function ErrorProvider({ children }: PropsWithChildren) {
  const [modalOpen, setModalOpen] = useState(false);
  const [activeError, setActiveError] = useState<ErrorResponse | null>(null);

  api.interceptors.response.use(
    (response) => response,
    (error: ErrorResponse) => {
      const status = error.response ? error.response.status : null;
      if (status === 403) {
        setActiveError(error);
        setModalOpen(true);
        return Promise.reject(error);
      } else if (status === 401) {
        console.error("Unauthorized request. Redirecting to login...");
        return Promise.reject(error);
      } else {
        return Promise.reject(error);
      }
    },
  );

  return (
    <>
      {!modalOpen && children}
      {modalOpen && activeError?.response.status == 404 && <NotFound />}
      {modalOpen && activeError?.response.status == 403 && <Forbidden />}
    </>
  );
}
