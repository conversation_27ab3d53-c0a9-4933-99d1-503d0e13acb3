import { type ReactNode } from "react";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { Anchor } from "@mantine/core";
import { format, parseISO } from "date-fns";
import { type MRT_Cell, type MRT_RowData } from "mantine-react-table";
import {
  isValidIsoDateString,
  getNestedProperty,
} from "../../utils/rendererUtils";

interface LookupCellProps {
  cell: MRT_Cell<MRT_RowData, unknown>;
  entityPath: string;
  nameFields: string[];
  idField?: string; // Optional: field name for the ID, defaults to "id"
}

export function LookupCell({
  cell,
  entityPath,
  nameFields,
  idField = "id",
}: LookupCellProps): ReactNode {
  const { dateFormat } = useSettingsContext();
  const entity = cell.getValue<Record<string, unknown> | null | undefined>();

  // If no entity data for this cell, render nothing or a placeholder
  if (!entity) {
    return <></>;
  }

  // Construct the display name
  const displayName = nameFields
    .map((field) => {
      // Safely access nested properties
      let value = getNestedProperty(entity, field) as string;
      if (value === null || value === undefined) return "";

      // Format dates if detected
      if (isValidIsoDateString(value)) {
        try {
          // Ensure value is string before parsing
          const dateStr = String(value);
          value = format(parseISO(dateStr), dateFormat || "yyyy-MM-dd"); // Provide default format
        } catch (e) {
          console.error(
            `Error formatting date string "${value}" for field "${field}":`,
            e,
          );
          value = String(value); // Fallback to original string value on error
        }
      } else {
        value = String(value); // Ensure it's a string for joining
      }

      return value;
    })
    .join(" ") // Join parts with a space
    .trim(); // Remove leading/trailing whitespace

  // Get the ID for the link
  const entityId = getNestedProperty(entity, idField);

  // If no ID, we can't link, maybe just display the name?
  if (
    !entityId ||
    (typeof entityId !== "string" && typeof entityId !== "number")
  ) {
    return <>{displayName || ""}</>;
  }

  // Construct the URL (consider using URLSearchParams for redirectTo)
  const redirectParam = window.location.pathname
    ? `?redirectTo=${encodeURIComponent(window.location.pathname)}`
    : "";
  const url = `/app/${entityPath}/${entityId}${redirectParam}`;

  return (
    <Anchor fz={12} href={url} underline="hover" c="primary">
      {displayName || ""}
    </Anchor>
  );
}
