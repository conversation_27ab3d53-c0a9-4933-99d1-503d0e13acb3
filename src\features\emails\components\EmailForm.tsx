import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useDebounceCallback } from "usehooks-ts";
import { useForm } from "@mantine/form";
import {
  Grid,
  Paper,
  Tabs,
  TextInput,
  Textarea,
  Button,
  Collapse,
  Fieldset,
  ScrollArea,
} from "@mantine/core";
import { zodResolver } from "mantine-form-zod-resolver";
import { type ReactNode } from "react";
import { getDirtyFormFields } from "@/features/entity/utils";
import { EmailAttachments } from "./EmailAttachments";
import { useState, useEffect } from "react";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";

const formSchema = z.object({
  from: z.string(),
  to: z.string(),
  cc: z.string().min(1),
  subject: z.string().min(1),
  body: z.string(),
  recordState: z.string(),
  replyToEmailId: z.string().nullable(),
  replyToEmail: z.object({}).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface EmailFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  headerSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  emailId: string;
  isCreate: boolean;
}

export function EmailForm({
  onSubmit,
  initialValues,
  actionSection = null,
  headerSection = null,
  title,
  emailId,
  isCreate,
}: EmailFormProps) {
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const { t } = useTranslation("features");

  const form = useForm<FormSchema>({
    initialValues: {
      from: initialValues?.from ?? "",
      to: initialValues?.to ?? "",
      cc: initialValues?.cc ?? "",
      subject: initialValues?.subject ?? "",
      body: initialValues?.body ?? "",
      recordState: initialValues?.recordState ?? "",
      replyToEmailId: initialValues?.replyToEmailId ?? "",
      replyToEmail: initialValues?.replyToEmail ?? null,
    },
    validate: zodResolver(formSchema),
  });

  const [htmlContent, setHtmlContent] = useState(initialValues?.body);
  const [showHtml, setShowHtml] = useState(false);
  const [opened, setOpened] = useState(false);
  const [conversationVisible, setConversationVisible] = useState(false);
  const [conversation, setConversation] = useState<
    Schemas["EmailRetrieveDto"][]
  >([]);

  useEffect(() => {
    if (htmlContent !== form.values.body) {
      form.setValues({ body: htmlContent });
    }
  }, [htmlContent, form]);

  const { data } = useEntityQuery<Schemas["EmailRetrieveDto"]>({
    resourcePath: `/api/Emails/{id}/conversation`,
    resourceId: initialValues?.replyToEmailId ?? "",
    queryKey: ["email", initialValues?.replyToEmailId],
  });
  useEffect(() => {
    const buildConversation = () => {
      const conversation = [];
      let currentEmail = data;

      while (currentEmail) {
        conversation.push(currentEmail);
        currentEmail = currentEmail.replyToEmail;
      }

      return conversation;
    };

    if (initialValues?.replyToEmailId) {
      const conversation = buildConversation();
      setConversation(conversation);
    }
  }, [data, emailId, initialValues]);

  const handleChange = (event: {
    target: { value: React.SetStateAction<string | undefined> };
  }) => {
    setHtmlContent(event.target.value);
  };
  const toggleHtmlDisplay = () => {
    setShowHtml(!showHtml);
  };
  const toggleConversation = () => {
    setConversationVisible((prev) => !prev);
  };

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
        headerSection={headerSection}
        recordState={t(
          (form.getInputProps("recordState").value as string) ?? "",
        )}
      >
        <Tabs defaultValue="general">
          <Tabs.List style={{ pointerEvents: "auto" }}>
            <Tabs.Tab value="general">{t("leads.general")}</Tabs.Tab>
          </Tabs.List>
          <Tabs.Panel value="general">
            <Grid mt="lg">
              <Grid.Col span={{ base: 12, md: 12 }}>
                <Button
                  variant="light"
                  onClick={() => setOpened((o) => !o)}
                  m={8}
                >
                  {opened ? "Hide Attachments" : "Show Attachments"}
                </Button>

                <ScrollArea.Autosize
                  m={8}
                  type="auto"
                  style={{
                    opacity: opened ? 1 : 0,
                    maxHeight: opened ? "256px" : "0px",
                    overflow: "hidden",
                    transition: "opacity 0.3s ease, max-height 0.3s ease",
                  }}
                >
                  <EmailAttachments emailId={emailId} />
                </ScrollArea.Autosize>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 12 }}>
                <Fieldset
                  disabled={
                    form.getInputProps("recordState").value === "Inactive"
                  }
                >
                  <Paper shadow="xs" p="lg">
                    <TextInput
                      label={t("emails.to")}
                      {...form.getInputProps("to")}
                    />
                    <TextInput
                      mt="sm"
                      label={t("emails.from")}
                      {...form.getInputProps("from")}
                    />
                    <TextInput
                      mt="sm"
                      label={t("emails.cc")}
                      {...form.getInputProps("cc")}
                    />
                    <TextInput
                      mt="sm"
                      label={t("emails.subject")}
                      {...form.getInputProps("subject")}
                    />
                  </Paper>
                  <br />

                  <Button variant="light" m={8} onClick={toggleHtmlDisplay}>
                    {showHtml ? t("emails.showVisual") : t("emails.showHtml")}
                  </Button>

                  <Paper shadow="xs" p="lg" m={8}>
                    {showHtml ? (
                      <div>
                        <Textarea
                          minRows={20}
                          autosize
                          mt="sm"
                          value={htmlContent}
                          onChange={handleChange}
                        />
                      </div>
                    ) : (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: htmlContent as TrustedHTML,
                        }}
                      />
                    )}
                  </Paper>
                </Fieldset>
              </Grid.Col>
            </Grid>
            <Paper mt="xs">
              {conversation.length > 0 && (
                <Button variant="outline" onClick={toggleConversation}>
                  {conversationVisible
                    ? t("emails.hideHistory")
                    : t("emails.showHistory")}
                </Button>
              )}
              <Collapse in={conversationVisible}>
                <Grid mt="lg">
                  {conversation.map((email, index) => (
                    <Grid.Col key={email.replyToEmailId || index}>
                      <Paper shadow="xs" p="lg" mb="sm">
                        <strong>{t("emails.from")}:</strong> {email.from}
                        <br />
                        <strong>{t("emails.to")}:</strong> {email.to}
                        <br />
                        <strong>{t("emails.subject")}:</strong> {email.subject}
                        <br />
                        <div
                          dangerouslySetInnerHTML={{
                            __html: email.body as TrustedHTML,
                          }}
                        />
                      </Paper>
                    </Grid.Col>
                  ))}
                </Grid>
              </Collapse>
            </Paper>
          </Tabs.Panel>
        </Tabs>
      </EntityLayout>
    </form>
  );
}
