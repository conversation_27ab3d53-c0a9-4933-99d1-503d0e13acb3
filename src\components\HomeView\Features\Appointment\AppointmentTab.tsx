import { Box, Flex, Group, Progress, Text } from "@mantine/core";

import { type PossibleTabs } from "../../Home";
import { type TabProps } from "../../Structure/HomeTabs";

import classes from "../..//Home.module.css";
import AppointmentQuery from "./AppointmentQuery";
import NumberLoader from "../../Components/numberLoader";
import { useTranslation } from "react-i18next";

const TAB: PossibleTabs = "Appointment";

export default function AppointmentTab({ setActiveTab, isActive }: TabProps) {
  const { t } = useTranslation("features");

  const { totalCompleted, totalCount } = AppointmentQuery();
  let percentage = 0;
  if (totalCompleted != undefined && totalCount != undefined) {
    if (totalCount == 0) {
      percentage = 100;
    } else {
      percentage = (totalCompleted / (totalCount + totalCompleted)) * 100;
    }
  }
  return (
    <Box
      w={{ base: "100%", xs: "100%", md: "32%", lg: "16%", xl: "16%" }}
      className={isActive ? classes.activeHomeTab : classes.homeTab}
      onClick={() => setActiveTab(TAB)}
    >
      <Flex justify="center" align="center" direction="row" wrap="wrap">
        <Text w={"100%"} className={classes.tabTitleText}>
          {t(`appointments.pendingAppointments`)}
        </Text>

        <Text className={classes.tabDisplayText}>
          <NumberLoader number={totalCount} />
        </Text>

        <Progress
          w={"100%"}
          radius="xl"
          size={8}
          value={percentage}
          mt={8}
          mb={8}
          bg={"neutral.2"}
        />

        <Group justify="space-between" gap={4} w={"100%"}>
          <Text className={classes.tabFooterText}>
            {t(`appointments.doneToday`)}
          </Text>
          <Text className={classes.tabFooterBoldText}>
            <NumberLoader number={totalCompleted} />
          </Text>
        </Group>
      </Flex>
    </Box>
  );
}
