import { Box, Center, Loader } from "@mantine/core";
import { useUserContext } from "../Layout/Contexts/User/useUserContext";
import HomeTabs from "./Structure/HomeTabs";
import { HomeHeader } from "./Structure/HomeHeader";
import { useState } from "react";
import HomeLists from "./Structure/HomeLists";

export type PossibleTabs =
  | "Appointment"
  | "Callback"
  | "RecentLeads"
  | "ActiveCases"
  | "OpenLeads"
  | "Omnichannel";

export default function Home() {
  const { finalUser, isLoading: isLoadingUser } = useUserContext();
  const [activeTab, setActiveTab] = useState<PossibleTabs | undefined>(
    undefined,
  );

  if (isLoadingUser) {
    return (
      <Center mt={40}>
        <Loader />
      </Center>
    );
  }
  return (
    <Box ml={12} mt={16}>
      <HomeHeader username={finalUser?.name ?? ""} />
      <HomeTabs activeTab={activeTab!} setActiveTab={setActiveTab} />
      <HomeLists activeTab={activeTab!} />
    </Box>
  );
}
