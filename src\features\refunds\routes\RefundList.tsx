import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { RefundsColumns } from "../table/RefundsColumns";

const PATH = "Refunds";

export function RefundListInner({
  visibleColumns,
  resourcePath,
  createPath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["RefundRetrieveDto"],
        Schemas["RefundRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="refund"
        entityPath="refunds"
        title={t("refunds.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        redirectTo={window.location.pathname}
        visibleColumns={visibleColumns}
        columns={RefundsColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function RefundList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <RefundListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
