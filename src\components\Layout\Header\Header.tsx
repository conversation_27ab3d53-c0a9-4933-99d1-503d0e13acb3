import {
  <PERSON>pp<PERSON><PERSON>,
  <PERSON>,
  Center,
  Flex,
  Group,
  Loader,
  Title,
} from "@mantine/core";
import { config } from "@/config";
import classes from "./Header.module.css";
import { UserMenu } from "../UserMenu/UserMenu";
import { Link } from "react-router-dom";
import { NotificationPopover } from "./NotificationPopover";
import { BusinessUnitSelect } from "@/components/Layout/Header/BusinessUnitSelect";
import Omnichannel from "../Omnichannel/Omnichannel";
import { useLayoutVisibility } from "../Contexts/LayoutVisibility/LayoutVisibilityContext";
import { useUserContext } from "../Contexts/User/useUserContext";
import CustomerSearch from "@/components/Layout/Header/CustomerSearch/CustomerSearch";

interface HeaderProps {
  mobileOpened: boolean;
  desktopOpened: boolean;
  onClickMobile: VoidFunction;
  onClickDesktop: VoidFunction;
  userBusinessUnitId: string | null;
  setUserBusinessUnitId: (
    businessUnitId: string | null,
    closeModal: boolean,
  ) => void;
}

export function Header({
  mobileOpened,
  desktopOpened,
  onClickMobile,
  onClickDesktop,
  userBusinessUnitId,
  setUserBusinessUnitId,
}: HeaderProps) {
  const { roles } = useUserContext();

  const { isOmnichannelVisible } = useLayoutVisibility();
  const { userUpdating } = useUserContext();

  if (userUpdating) {
    return (
      <AppShell.Header>
        <Center h={"100%"}>
          <Loader />
        </Center>
      </AppShell.Header>
    );
  }

  if (isOmnichannelVisible) {
    return (
      <AppShell.Header>
        <Omnichannel />
      </AppShell.Header>
    );
  }

  const isTest = document.location.origin.includes("https://test.");
  const isAcceptance = document.location.origin.includes("https://accept.");

  const title = isTest
    ? config.PRODUCT_TITLE_TEST
    : isAcceptance
      ? config.PRODUCT_TITLE_ACC
      : config.PRODUCT_TITLE;

  const headerClass = isTest
    ? classes.headerTest
    : isAcceptance
      ? classes.headerAcceptance
      : classes.header;

  return (
    <AppShell.Header withBorder={true} className={headerClass}>
      <Group justify="space-between" align="center" px="md" h="100%" grow>
        {/* Left Section */}
        <Flex h="100%" flex={1} justify="flex-start" align="center" gap="md">
          <Burger
            opened={mobileOpened}
            onClick={onClickMobile}
            h={28}
            hiddenFrom="sm"
            size="sm"
            color="black"
          />
          <Burger
            opened={desktopOpened}
            onClick={onClickDesktop}
            h={28}
            visibleFrom="sm"
            size="sm"
            color="black"
          />
          <Link to="/app" style={{ textDecoration: "none" }}>
            <Title size={20} h={28} order={2} c={"brand"}>
              {title}
            </Title>
          </Link>
        </Flex>

        {/* Center Section*/}
        <Flex flex={1} justify="center">
          <CustomerSearch />
        </Flex>

        {/* Right Section */}
        <Flex flex={1} justify="flex-end" align="center" gap="md">
          {roles.includes("Store Manager") && (
            <BusinessUnitSelect
              businessUnitId={userBusinessUnitId}
              setBusinessUnitId={setUserBusinessUnitId}
              closeModal={false}
            />
          )}

          <Flex gap={0}>
            <NotificationPopover />
            <UserMenu />
          </Flex>
        </Flex>
      </Group>
    </AppShell.Header>
  );
}
