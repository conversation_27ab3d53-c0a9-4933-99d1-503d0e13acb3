import {
  Box,
  Fieldset,
  Grid,
  NumberInput,
  Paper,
  Select,
  TextInput,
  Textarea,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useTranslation } from "react-i18next";
import {
  Discount,
  ProductType,
  SizeOfUnit,
  RentAsBusiness,
  StorageDuration,
  StorageUnitReason,
  Transportation,
} from "@/types/enums";
import { config } from "@/config";
import { getEnumTransKey } from "@/utils/trans";
import { useLeadFormContext } from "../../providers/form";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { AdvisedUnitList } from "@/features/advisedUnits/routes/AdvisedUnitList";
import { AdvisedProductList } from "@/features/advisedProducts/routes/AdvisedProductList";
import { ReservationList } from "@/features/reservations/routes/ReservationList";
import { QuoteList } from "@/features/quotes/routes/QuoteList";
import { StorageTypeLookup } from "@/components/Lookup/Features/StorageTypes/StorageTypeLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

interface SpaceTourTabProps {
  leadId?: string;
}

export function SpaceTourTab({ leadId }: SpaceTourTabProps) {
  const { t } = useTranslation("features");
  const form = useLeadFormContext();
  const { dateFormat } = useSettingsContext();
  const isFormDisabled = form.getInputProps("recordState").value === "Inactive";
  return (
    <>
      <Fieldset disabled={isFormDisabled}>
        <Grid mt="lg">
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="xs" pt="">
              <Select
                searchable
                label={t("leads.storageUnitReason")}
                data={StorageUnitReason.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("storageUnitReason")}
              />
              <FieldValidation isDirty={form.isDirty("storageTypeId")}>
                <StorageTypeLookup
                  initial={form.getValues().storageType}
                  initialId={form.getValues().storageTypeId}
                  identifier="storageTypeIdLead"
                  label={t("leads.storageType")}
                  {...form.getInputProps("storageTypeId")}
                />
              </FieldValidation>
              <DatePickerInput
                valueFormat={dateFormat.toUpperCase()}
                clearable
                label={t("leads.moveInDate")}
                {...form.getInputProps("moveInDate")}
              />
              <Select
                searchable
                label={t("leads.storageDuration")}
                data={StorageDuration.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("storageDuration")}
              />
              <Select
                searchable
                label={t("leads.rentAsBusiness")}
                data={RentAsBusiness.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("rentAsBusiness")}
              />
              <Select
                searchable
                label={t("leads.transportation")}
                data={Transportation.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("transportation")}
              />

              <Select
                searchable
                label={t("leads.discount")}
                data={Discount.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("discount")}
              />
              <TextInput
                label={t("leads.discountText")}
                {...form.getInputProps("discountText")}
              />
              <FieldValidation isDirty={form.isDirty("productType")}>
                <Select
                  searchable
                  label={t("leads.productType")}
                  data={ProductType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  {...form.getInputProps("productType")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("sizeOfUnit")}>
                <Select
                  searchable
                  label={t("leads.sizeOfUnit")}
                  data={SizeOfUnit.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  {...form.getInputProps("sizeOfUnit")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("amount")}>
                <NumberInput
                  label={t("leads.amount")}
                  leftSection={config.CURRENCY.symbol}
                  {...form.getInputProps("amount")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("volume")}>
                <NumberInput
                  label={t("leads.volume")}
                  leftSection={config.CUBICMETERS.symbol}
                  {...form.getInputProps("volume")}
                />
              </FieldValidation>
              <Textarea
                label={t("leads.spaceTourRemarks")}
                {...form.getInputProps("spaceTourRemarks")}
                minRows={4}
                autosize
              />
              {/*<Switch
            mt="sm"
            label={t("leads.quotationEmailSent")}
            checked={form.getValues().quotationEmailSent}
            {...form.getInputProps("quotationEmailSent")}
          /> */}
            </Paper>
          </Grid.Col>

          {leadId ? (
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Box mb={6}>
                <AdvisedUnitList
                  visibleColumns={[
                    "unit",
                    "pricePerMonth",
                    "pricePerWeek",
                    "reservation",
                    "reservationStatus",
                  ]}
                  parentEntityId={leadId}
                  parentEntityName="Leads"
                  parentEntityIdParam="leadId"
                />
              </Box>
              <Box mb={6}>
                <AdvisedProductList
                  parentEntityId={leadId}
                  parentEntityName="Leads"
                  parentEntityIdParam="leadId"
                  visibleColumns={[
                    "price",
                    "product",
                    "quantity",
                    "totalPrice",
                  ]}
                />
              </Box>
              <Box mb={6}>
                <QuoteList
                  parentEntityId={leadId}
                  parentEntityName="Leads"
                  parentEntityIdParam="leadId"
                  visibleColumns={[
                    "number",
                    "totalMonthlyPrice",
                    "expirationDate",
                    "status",
                    "createdOn",
                    "owner",
                  ]}
                />
              </Box>
              <Box mt={6}>
                <ReservationList
                  parentEntityId={leadId}
                  parentEntityName="Leads"
                  parentEntityIdParam="leadId"
                />
              </Box>
            </Grid.Col>
          ) : null}
        </Grid>
      </Fieldset>
    </>
  );
}
