import { Mo<PERSON> } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import React from "react";
import { cloneElement } from "react";

interface WizardOverlay {
  WizardContent: JSX.Element;
  WizardTarget: JSX.Element;
  onClick: () => void;
}
export default function WizardOverlay({
  WizardContent,
  WizardTarget,
  onClick,
}: WizardOverlay) {
  const [opened, { open, close }] = useDisclosure(false);

  const targetWithProps = React.isValidElement(WizardTarget)
    ? cloneElement(WizardTarget as JSX.Element, {
        onClick: () => {
          open();
          onClick();
        },
      })
    : WizardTarget;

  const contentWithProps = React.isValidElement(WizardContent)
    ? cloneElement(WizardContent as JSX.Element, {
        closeModal: close,
      })
    : WizardContent;
  return (
    <>
      <Modal
        opened={opened}
        onClose={close}
        closeOnClickOutside={false}
        withCloseButton={false}
        centered
        radius={16}
        size={"100%"}
      >
        {contentWithProps}
      </Modal>
      {targetWithProps}
    </>
  );
}
