import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import TableRenderer from "@/components/Table/renderers/TableRenderer";
import { MailboxesCreate } from "./MailboxesCreate";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";

const PATH = "Mailboxes";
export function MailboxesListInner({
  resourcePath,
  createPath,
  parentEntityId,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["MailboxRetrieveDto"],
        Schemas["MailboxRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="mailbox"
        entityPath="Mailboxes"
        title={t("mailbox.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton
              to={createPath}
              FormComponent={parentEntityId ? MailboxesCreate : undefined}
              formProps={
                parentEntityId
                  ? {
                      parentEntityId: parentEntityId,
                      redirectTo: "/app/mailboxes",
                      usingModal: true,
                    }
                  : undefined
              }
            />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "email",
            header: t("mailbox.email"),
            filterVariant: "text",
          },
          {
            accessorKey: "businessUnit",
            header: t("mailbox.businessUnit"),
            ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function MailboxesList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <MailboxesListInner
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
      />
    </ListCommandsProvider>
  );
}
