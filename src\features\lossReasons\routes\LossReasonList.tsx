import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { EntityLinkRenderer } from "@/components/Table/CellRenderers";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  recordState,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { HtmlTemplateLookup } from "@/components/Lookup/Features/HtmlTemplates/HtmlTemplateLookup";
import { lowerCaseNthLetter } from "@/utils/filters";
import { type ComboboxData } from "@mantine/core";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

const PATH = "LossReasons";
export function LossReasonListInner({
  pageSize,
  resourcePath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["LossReasonRetrieveDto"],
        Schemas["LossReasonRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="lossReason"
        entityPath="lossReasons"
        title={t("lossReasons.title")}
        redirectTo={window.location.pathname}
        pageSize={pageSize}
        columns={[
          {
            accessorKey: "name",
            header: t("lossReasons.name"),
            sortDescFirst: true, // leave this here ~!
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "lossReasons"),
          },
          {
            accessorKey: "recordState",
            header: t("leads.recordState"),
            filterVariant: "select",
            Cell: ({ cell }) =>
              t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
            mantineFilterSelectProps: {
              data: recordState as ComboboxData | undefined,
            },
          },
          {
            accessorKey: "htmlTemplate",
            header: t("lossReasons.htmlTemplate"),
            ...TableRenderer(HtmlTemplateLookup, "htmlTemplates", ["name"]),
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function LossReasonList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
  pageSize,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <LossReasonListInner
        resourcePath={resourcePath}
        createPath={createPath}
        pageSize={pageSize}
      />
    </ListCommandsProvider>
  );
}
