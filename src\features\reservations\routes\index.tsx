import { Route, Routes } from "react-router-dom";
import { ReservationList } from "./ReservationList";
import { ReservationShow } from "./ReservationShow";
import { ReservationCreate } from "./ReservationCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<ReservationList />} />
      <Route path=":id" element={<ReservationShow />} />
      <Route path="create" element={<ReservationCreate />} />
    </Routes>
  );
}
