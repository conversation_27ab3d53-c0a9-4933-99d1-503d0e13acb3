import {
  CloseButton,
  Combobox,
  type ComboboxOptionProps,
  ScrollArea,
  TextInput,
  type ComboboxStore,
  Loader,
  Flex,
  Group,
  Anchor,
  Center,
  Box,
  Checkbox,
} from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { Highlight } from "@mantine/core";
import { isScrollThreasholdReached } from "../Base/utils";
import { useEffect, useRef, useState } from "react";
import { IconEye } from "@tabler/icons-react";
import classes from "../Base/Lookup.module.css";
import type React from "react";
import LookupHeader from "../Base/Structure/LookupHeader";

type LookupProps = GetInputPropsReturnType & {
  combobox: ComboboxStore;
  required?: boolean;
  disabled?: boolean;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  lookupValue: string | null;
  setLookupValue: (value: string | null) => void;
  options: (JSX.Element | null)[] | undefined;
  header: JSX.Element;
  isFetching: boolean;
  currentFocus: number;
  entity: string;
  entityId: string | undefined;
};

export function Lookup({
  combobox,
  onChange,
  required = false,
  disabled = false,
  searchTerm,
  setSearchTerm,
  lookupValue,
  setLookupValue,
  options,
  header,
  isFetching,
  currentFocus,
  entity,
  entityId,
  ...props
}: LookupProps) {
  const viewportRef = useRef<HTMLDivElement>(null);
  const [currentId, setCurrentId] = useState<string | null>(entityId ?? null);

  useEffect(() => {
    viewportRef.current
      ?.querySelectorAll(".mantine-Combobox-option")
      ?.[currentFocus]?.scrollIntoView({ block: "nearest" });
  }, [currentFocus]);

  return (
    <Combobox
      store={combobox}
      withinPortal={true}
      width={"70vw"}
      onOptionSubmit={(val, optionProps: ComboboxOptionProps) => {
        combobox.closeDropdown();
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        onChange(val);
        setCurrentId(val);
        setLookupValue(optionProps.display as string);
        setSearchTerm("");
      }}
    >
      <Combobox.Target>
        <TextInput
          pointer
          required={required}
          disabled={disabled}
          contentEditable={true}
          {...props}
          value={
            lookupValue != null && lookupValue != "" ? lookupValue : searchTerm
          }
          onChange={(event) => {
            setLookupValue(event.currentTarget.value);
            setSearchTerm(event.currentTarget.value);
          }}
          leftSection={
            entity !== null &&
            currentId !== null && (
              <Anchor
                underline="always"
                href={`${document.location.origin}/app/${entity}/${currentId}?redirectTo=${document.location.pathname}`}
                className={classes.eyeAnchor}
              >
                <Center>
                  <IconEye />
                </Center>
              </Anchor>
            )
          }
          rightSection={
            currentId !== null ? (
              <CloseButton
                size="sm"
                onMouseDown={(event) => event.preventDefault()}
                disabled={disabled}
                onClick={() => {
                  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                  onChange(null);
                  setLookupValue(null);
                  setCurrentId(null);
                  setSearchTerm("");
                }}
                aria-label="Clear value"
              />
            ) : (
              <Combobox.Chevron />
            )
          }
          onClick={() => combobox.toggleDropdown()}
        ></TextInput>
      </Combobox.Target>
      <Combobox.Dropdown style={{ left: "15vw" }}>
        <Combobox.Options>
          {options?.length !== 0 && (
            <Combobox.Header className={classes.lookupHeader}>
              <Group justify="space-between" gap="sm" grow>
                <LookupHeader header={header} />
              </Group>
            </Combobox.Header>
          )}
          {isFetching ? (
            <Flex p={20} justify="center">
              <Loader></Loader>
            </Flex>
          ) : (
            <ScrollArea.Autosize
              mah={"25vh"}
              type="always"
              offsetScrollbars
              scrollbars="y"
              viewportRef={viewportRef}
            >
              {options?.length === 0 ? (
                <Combobox.Empty>{"Nothing Found..."}</Combobox.Empty>
              ) : (
                <>{options}</>
              )}
            </ScrollArea.Autosize>
          )}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}

export const OptionGroupRender = (
  id: string,
  index: number,
  display: string,
  entityCount: number,
  infiniteScrollRef: React.RefObject<HTMLDivElement>,
  options: JSX.Element[],
  disabled?: boolean,
) => {
  return (
    <Combobox.Option
      value={id}
      key={id}
      display={display}
      disabled={disabled}
      ref={
        isScrollThreasholdReached(index, entityCount)
          ? infiniteScrollRef
          : undefined
      }
    >
      <Group gap="sm">{options}</Group>
    </Combobox.Option>
  );
};

export const BoolOptionRender = (value: boolean, id?: string, flex = 1) => {
  return (
    <Box flex={flex} key={id}>
      <Center w={"100%"}>
        <Checkbox readOnly checked={value} />
      </Center>
    </Box>
  );
};

export const OptionRender = (
  value: string | null | undefined,
  searchTerm: string,
  id?: string,
  flex = 1,
) => {
  return (
    <Box flex={flex} key={id}>
      <Highlight highlight={searchTerm} size="sm">
        {value ?? ""}
      </Highlight>
    </Box>
  );
};
