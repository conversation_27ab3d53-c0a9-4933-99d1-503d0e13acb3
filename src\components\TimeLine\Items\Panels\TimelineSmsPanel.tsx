import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Box, Center, Flex, Loader, Stack } from "@mantine/core";
import { DateTime } from "luxon";
import { useTranslation } from "react-i18next";
interface TimelineSmsPanelProps {
  smsId: string;
}

export function TimelineSmsPanel({ smsId }: TimelineSmsPanelProps) {
  const { t } = useTranslation("features");
  const { dateFormat } = useSettingsContext();
  const { data, isLoading } = useEntityQuery<Schemas["SmsMessageRetrieveDto"]>({
    resourcePath: `/api/SmsMessages/{id}`,
    resourceId: smsId,
    queryKey: ["sms", smsId],
  });

  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <Box ml={8}>
      <Stack gap="xs">
        <Flex>
          {t("smsMessages.createdOn")}:
          <Box ml={8}>
            {DateTime.fromJSDate(new Date(data?.createdOn ?? "")).toFormat(
              dateFormat + " HH:mm:ss",
            )}
          </Box>
        </Flex>
        {data?.sentOn &&
          new Date(data.sentOn).getFullYear() !== 1 && ( //Year is not 0001
            <Flex>
              {t("smsMessages.sentOn")}:
              <Box ml={8}>
                {DateTime.fromJSDate(new Date(data?.sentOn ?? "")).toFormat(
                  dateFormat + " HH:mm:ss",
                )}
              </Box>
            </Flex>
          )}
        <Flex>
          {t("smsMessages.recipients")}:
          <Box ml={8}>{data?.recipients ?? ""}</Box>
        </Flex>
        <Flex>
          {t("smsMessages.subject")}:<Box ml={8}>{data?.subject ?? ""}</Box>
        </Flex>
      </Stack>
    </Box>
  );
}
