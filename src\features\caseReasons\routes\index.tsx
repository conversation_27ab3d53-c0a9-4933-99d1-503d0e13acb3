import { Route, Routes } from "react-router-dom";
import { CaseReasonsList } from "./CaseReasonsList";
import { CaseReasonsCreate } from "./CaseReasonsCreate";
import { CaseReasonsShow } from "./CaseReasonsShow";

export default function CaseReasonsRoutes() {
  return (
    <Routes>
      <Route index element={<CaseReasonsList />} />
      <Route path=":id" element={<CaseReasonsShow />} />
      <Route path="create" element={<CaseReasonsCreate />} />
    </Routes>
  );
}
