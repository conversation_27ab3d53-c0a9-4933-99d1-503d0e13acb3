import {
  MantineReactTable,
  type MRT_ColumnDef,
  type MRT_RowData,
  MRT_ShowHideColumnsButton,
  MRT_ToggleFullScreenButton,
  useMantineReactTable,
} from "mantine-react-table";
import { useNavigate } from "react-router";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>tyLinkRenderer,
  Phone<PERSON><PERSON>er,
} from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { recordState } from "@/features/entity/utils";
import { ProcessStage, StartWithin } from "@/types/enums";
import { type Schemas } from "@/types";
import { lowerCaseNthLetter } from "@/utils/filters";
import {
  Box,
  type ComboboxItem,
  type ComboboxLikeRenderOptionInput,
  type ComboboxData,
  Group,
  Flex,
  Loader,
  Center,
} from "@mantine/core";
import { useEntityListQuery } from "@/features/entity/queries";
import ButtonOmnichannel from "@/components/Layout/Omnichannel/ButtonOmnichannel";
import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";
import { PossiblePriorities } from "@/features/entity";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { ContractLookup } from "@/components/Lookup/Features/Contracts/ContractLookup";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export default function OmnichannelTable() {
  const { t } = useTranslation("features");
  const navigate = useNavigate();
  const { finalUser } = useUserContext();

  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "firstName",
      header: t("leads.firstName"),
      filterVariant: "text",
      Cell: (props) => EntityLinkRenderer(props, "leads"),
    },
    {
      accessorKey: "lastName",
      header: t("leads.lastName"),
      filterVariant: "text",
      Cell: (props) => EntityLinkRenderer(props, "leads"),
    },
    {
      accessorKey: "email",
      header: t("leads.emailHeader"),
      filterVariant: "text",
      Cell: EmailRenderer,
    },
    {
      accessorKey: "mobile",
      header: t("leads.mobileHeader"),
      filterVariant: "text",
      Cell: PhoneRenderer,
    },
    {
      accessorKey: "existingContact",
      header: t("leads.existingContactId"),
      ...TableRenderer(ContactLookup, "contacts", ["lastName", "firstName"]),
    },
    {
      accessorKey: "priority",
      header: t("leads.priority"),
      filterVariant: "multi-select",
      Cell: ({ cell }) => {
        return cell.getValue() as string;
      },
      mantineFilterSelectProps: {
        data: PossiblePriorities,
      },
      sortingFn: (a, b) => {
        const rowA: Schemas["Lead"] = a.original;
        const rowB: Schemas["Lead"] = b.original;
        const priorityA = rowA.priority ?? 0;
        const priorityB = rowB.priority ?? 0;
        return priorityA - priorityB;
      },
    },
    {
      accessorKey: "ownerId",
      header: t("Owner Id"),
      filterVariant: "text",
    },
    {
      accessorKey: "owner",
      header: t("Owner Name"),
      filterVariant: "text",
      ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
    },
    {
      accessorKey: "contract",
      header: t("leads.contract"),
      ...TableRenderer(ContractLookup, "contracts", ["contractNumber"]),
    },
    {
      accessorKey: "businessUnit",
      header: t("leads.businessUnit"),
      ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
    },
    {
      accessorKey: "processStage",
      header: t("leads.processStageHeader"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: ProcessStage as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "startWithin",
      header: t("leads.startWithin"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: StartWithin as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "webformTitle",
      header: t("leads.webformTitle"),
      filterVariant: "text",
    },
    {
      accessorKey: "webformDetails",
      header: t("leads.webformDetails"),
      filterVariant: "text",
    },
    {
      accessorKey: "step",
      header: t("Step"),
      filterVariant: "text",
    },
    {
      accessorKey: "recordState",
      header: t("leads.recordState"),
      filterVariant: "select",
      Cell: ({ cell }) =>
        t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: recordState as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "modifiedOn",
      header: t("entity.modifiedOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
  ];

  const { data, isLoading, isRefetching } = useEntityListQuery<
    Schemas["LeadRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Leads/omnichannel",
    queryKey: "lead_omnichannel",
    params: {
      filter: `OwnerId == ${finalUser?.id}`,
    },
  });

  const showColumns: Record<string, boolean> = {};

  columns.forEach((column: MRT_ColumnDef<MRT_RowData>) => {
    showColumns[column.accessorKey ?? ""] = true;
  });

  showColumns.ownerId = false;

  const rowData = data?.data ?? [];

  const table = useMantineReactTable({
    columns: columns,
    data: rowData,
    enableColumnFilterModes: true,
    enableColumnResizing: true,
    enableDensityToggle: false,
    enableGlobalFilter: false,
    enableFacetedValues: true,
    enableColumnActions: false,
    columnFilterDisplayMode: "popover",
    enableColumnPinning: false,
    layoutMode: "grid",
    state: { isLoading: isRefetching },
    initialState: {
      density: "xs",
      columnPinning: {
        left: ["mrt-row-expand", "mrt-row-select"],
        right: ["mrt-row-actions"],
      },
      columnVisibility: showColumns,
    },
    defaultColumn: { minSize: 40, maxSize: 1000, size: 80 },
    displayColumnDefOptions: {
      "mrt-row-select": {
        size: 40,
        grow: false,
      },
    },
    renderTopToolbar: ({ table }) => (
      <Group w={"100%"} justify="space-between" grow m={3}>
        <Flex
          gap="xs"
          justify="left"
          align="center"
          direction="row"
          wrap="wrap"
        ></Flex>
        <Flex
          gap={"xs"}
          justify="right"
          align="center"
          direction="row"
          wrap="wrap"
        >
          <ButtonOmnichannel table={table} />
          <MRT_ShowHideColumnsButton table={table} />
          <MRT_ToggleFullScreenButton table={table} mr={20} />
        </Flex>
      </Group>
    ),
    paginationDisplayMode: "pages",
    positionToolbarAlertBanner: "bottom",
    enableToolbarInternalActions: false,
    mantineFilterSelectProps: {
      renderOption: (item: ComboboxLikeRenderOptionInput<ComboboxItem>) => {
        const label = item.option.label;
        const shouldTranslate = !PossiblePriorities.includes(label);
        console.log("shouldTranslate", shouldTranslate);
        console.log("label", label);
        return (
          <Box w="100%" ta={"center"}>
            {shouldTranslate ? t("leads." + lowerCaseNthLetter(label)) : label}
          </Box>
        );
      },
    },
    mantineFilterMultiSelectProps: {
      renderOption: (item: ComboboxLikeRenderOptionInput<ComboboxItem>) => {
        return (
          <Box w="100%" ta={"center"}>
            {t("leads." + lowerCaseNthLetter(item.option.label))}
          </Box>
        );
      },
    },
    manualPagination: false,
    manualSorting: false,
    manualFiltering: true,
    mantineTableBodyRowProps: ({ row }) => ({
      onDoubleClick: () => {
        if (row?.original?.id) {
          navigate(
            `/app/leads/${row?.original?.id}?redirectTo=/app/omnichannel`,
          );
        }
      },
    }),
  });

  if (isLoading) {
    return (
      <Box ml={10} w="100%" h="100%" style={{ pointerEvents: "auto" }}>
        <Center w="100%" h="100%" mt={"20%"}>
          <Loader />
        </Center>
      </Box>
    );
  }

  return (
    <Box ml={10} style={{ pointerEvents: "auto" }}>
      <MantineReactTable table={table} />
    </Box>
  );
}
