import { PageLoader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  AdvisedUnitForm,
  type FormSchema,
} from "../components/AdvisedUnitForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function AdvisedUnitShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["AdvisedUnit"],
    Schemas["AdvisedUnitCreateDto"]
  >({
    resourcePath: "/api/AdvisedUnits/{id}",
    resourceId: id!,
    queryKey: "advisedUnit",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/advisedUnits";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("advisedUnit_" + id);
  };
  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/AdvisedUnits/{id}",
    resourceId: id!,
    queryKey: "advisedUnit",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["AdvisedUnit"]>({
    resourcePath: "/api/AdvisedUnits/{id}",
    resourceId: id!,
    queryKey: "advisedUnit",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <AdvisedUnitForm
      isCreate={false}
      title={t("advisedUnits.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["AdvisedUnitCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />

          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("advisedUnits.delete", { id })}
            modalContent={t("advisedUnits.deleteConfirmation", { id })}
            confirmLabel={t("advisedUnits.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate(redirectTo ?? "/app/advisedUnits");
              }
            }}
          />
        </Group>
      }
    />
  );
}
