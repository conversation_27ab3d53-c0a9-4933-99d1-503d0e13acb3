import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { type SetNonNullable } from "type-fest";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";
import { UserGroupForm } from "../components/UserGroupForm";

export function UserGroupShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["UserGroup"],
    Schemas["UserGroupCreateDto"]
  >({
    resourcePath: "/api/UserGroups/{id}",
    resourceId: id!,
    queryKey: "userGroup",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/UserGroups/{id}",
    resourceId: id!,
    queryKey: "userGroup",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["UserGroup"]>({
    resourcePath: "/api/UserGroups/{id}",
    resourceId: id!,
    queryKey: "userGroup",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("userGroup_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <UserGroupForm
      isCreate={false}
      title={t("userGroups.showTitle", { id })}
      contextRecordId={id}
      initialValues={
        filterFalsyValues(data) as Required<
          SetNonNullable<Schemas["UserGroup"]>
        >
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/usergroups");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/usergroups");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("userGroups.delete", { id })}
            modalContent={t("userGroups.deleteConfirmation", { id })}
            confirmLabel={t("userGroups.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/usergroups");
              }
            }}
          />
        </Group>
      }
    />
  );
}
