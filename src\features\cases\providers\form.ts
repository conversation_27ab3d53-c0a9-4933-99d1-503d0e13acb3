import { z } from "zod";
import { createFormContext } from "@mantine/form";
import { CaseStatus } from "@/types/enums";

export const caseFormSchema = z.object({
  number: z.string(),
  name: z.string(),
  subject: z.string(),
  senderEmail: z.string(),
  businessUnitId: z.string().nullable(),
  businessUnit: z.object({}).nullable(),
  originalBusinessUnitId: z.string().nullable(),
  originalBusinessUnit: z.object({}).nullable(),
  originId: z.string().nullable(),
  origin: z.object({}).nullable(),
  parentCaseId: z.string().nullable(),
  parentCase: z.object({}).nullable(),
  caseReasonId: z.string().nullable(),
  caseReason: z.object({}).nullable(),
  complaintReasonId: z.string().nullable(),
  complaintReason: z.object({}).nullable(),
  description: z.string(),
  contactId: z.string().nullable(),
  contact: z.object({}).nullable(),
  status: z.enum(CaseStatus as [string]),
});

export type CaseFormSchema = z.infer<typeof caseFormSchema>;

export const [CaseFormProvider, useCaseFormContext, useCaseForm] =
  createFormContext<CaseFormSchema>();
