import { C<PERSON>rency<PERSON>enderer } from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { ProductLookup } from "@/components/Lookup/Features/Products/ProductLookupField";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function AdvisedProductsColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "lead",
      header: t("advisedProducts.lead"),
      ...TableRenderer(LeadLookup, "leads", ["fullName"]),
    },
    {
      accessorKey: "product",
      header: t("advisedProducts.product"),
      ...TableRenderer(ProductLookup, "products", ["name"]),
    },
    {
      accessorKey: "price",
      header: t("advisedProducts.price"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "quantity",
      header: t("advisedProducts.quantity"),
      filterVariant: "range",
    },
    {
      accessorKey: "totalPrice",
      header: t("advisedProducts.totalPrice"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
  ];
  return columns;
}
