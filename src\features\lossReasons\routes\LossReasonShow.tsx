import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { LossReasonForm, type FormSchema } from "../components/LossReasonForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function LossReasonShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["LossReason"],
    Schemas["LossReasonCreateDto"]
  >({
    resourcePath: "/api/LossReasons/{id}",
    resourceId: id!,
    queryKey: "lossReason",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/lossReasons";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("lossReason_" + id);
  };

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["LossReason"]>({
    resourcePath: "/api/LossReasons/{id}",
    resourceId: id!,
    queryKey: "lossReason",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <LossReasonForm
      isCreate={false}
      title={t("lossReasons.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["LossReasonCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
