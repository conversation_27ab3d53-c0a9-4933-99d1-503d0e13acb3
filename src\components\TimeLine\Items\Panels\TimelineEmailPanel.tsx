import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Box, Center, Flex, Loader, SimpleGrid, Stack } from "@mantine/core";
import { DateTime } from "luxon";
import EmailPreview from "./EmailPreview";
import { EmailAttachmentsList } from "@/features/emails/components/EmailAttachmentsList";
import { useTranslation } from "react-i18next";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
interface TimelineEmailPanelProps {
  emailId: string;
  showHtml?: boolean;
}

export function TimelineEmailPanel({
  emailId,
  showHtml = false,
}: TimelineEmailPanelProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const { data, isLoading } = useEntityQuery<Schemas["EmailRetrieveDto"]>({
    resourcePath: `/api/Emails/{id}`,
    resourceId: emailId,
    queryKey: ["email", emailId],
  });

  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <Box ml={8}>
      <Stack gap="xs">
        <SimpleGrid cols={2}>
          <Stack>
            <Flex>
              {t("emails.createdOn")}
              {":"}
              <Box ml={8}>
                {DateTime.fromJSDate(new Date(data?.createdOn ?? "")).toFormat(
                  dateFormat + " HH:mm:ss",
                )}
              </Box>
            </Flex>
            <Flex>
              {t("emails.sender")}
              {":"}
              <Box ml={8}>{data?.from ?? ""}</Box>
            </Flex>
            <Flex>
              {t("emails.to")}
              {":"}
              <Box ml={8}>{data?.to ?? ""}</Box>
            </Flex>
            <Flex>
              {t("emails.cc")}
              {":"}
              <Box ml={8}>{data?.cc ?? ""}</Box>
            </Flex>
            <Flex>
              {t("emails.subject")}
              {":"}
              <Box ml={8}>{data?.subject ?? ""}</Box>
            </Flex>
          </Stack>
          <Flex mah={"200px"}>
            <EmailAttachmentsList emailId={emailId} />
          </Flex>
        </SimpleGrid>
        {showHtml && (
          <Flex>
            <EmailPreview htmlContent={data?.body ?? ""} />
          </Flex>
        )}
      </Stack>
    </Box>
  );
}
