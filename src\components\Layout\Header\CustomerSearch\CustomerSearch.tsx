import {
  Button,
  Box,
  Center,
  Loader,
  Badge,
  Combobox,
  useCombobox,
  ScrollArea,
  Divider,
} from "@mantine/core";
import { IconSearch, IconUserSearch } from "@tabler/icons-react";
import { useRef, useState } from "react";
import {
  getHotkeyHand<PERSON>,
  useDebouncedValue,
  useHotkeys,
} from "@mantine/hooks";
import { type QueryParams } from "@/features/entity/api";
import { useEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";

import classes from "./CustomerSearch.module.css";
import { useTranslation } from "react-i18next";
import CustomerCard from "./CustomerCard";
import { useNavigate } from "react-router-dom";
import LeadsQuickAddWizardButton, {
  type LeadsQuickAddWizardButtonRef,
} from "@/components/Wizards/Leads/LeadsQuickAddWizardButton";

const CHARACTER_MIN = 3;

export default function CustomerSearch() {
  const [search, setSearch] = useState("");
  const [debouncedSearchTerm] = useDebouncedValue(search, 300);
  const { t } = useTranslation("features");
  const queryParams: QueryParams = {
    searchBy: debouncedSearchTerm,
    pageSize: 21,
  };

  const { data, isLoading, isRefetching } = useEntityListQuery<
    Schemas["CustomerSearchResultDto"][]
  >({
    resourcePath: "/api/Search/customers",
    queryKey: `customerSearch[${debouncedSearchTerm}]`,
    params: queryParams,
    enabled: debouncedSearchTerm.length >= CHARACTER_MIN,
  });

  const handleClick = () => {
    combobox.toggleDropdown();
  };

  const quickAddLeadRef = useRef<LeadsQuickAddWizardButtonRef>(null);

  const quickAddLead = () => {
    if (quickAddLeadRef.current) {
      quickAddLeadRef.current.triggerClick();
    }
    combobox.closeDropdown();
  };

  useHotkeys([["mod+K", handleClick]]);
  useHotkeys([["mod+L", quickAddLead]]);

  const navigate = useNavigate();
  const combobox = useCombobox({
    onDropdownOpen: () => {
      combobox.focusSearchInput();
    },
  });

  const options =
    data?.map((item, index) => (
      <Combobox.Option value={JSON.stringify(item)} key={item.id}>
        <Box key={index} w={"100%"}>
          <CustomerCard customer={item} />
        </Box>
      </Combobox.Option>
    )) || [];

  return (
    <>
      <Combobox
        classNames={{
          search: classes.input,
          options: classes.options,
          option: classes.option,
        }}
        store={combobox}
        width={600}
        position="bottom"
        withArrow
        withinPortal={true}
        onOptionSubmit={(val) => {
          const selectedItem = JSON.parse(
            val,
          ) as Schemas["CustomerSearchResultDto"];

          navigate(`/app/${selectedItem.entityName + "s"}/${selectedItem.id}`);
          combobox.closeDropdown();
        }}
        zIndex={10001}
      >
        <Combobox.Target withAriaAttributes={false}>
          <Button
            visibleFrom="md"
            className={classes.inputButton}
            variant="default"
            color={"gray"}
            size="xs"
            pl={16}
            leftSection={<IconUserSearch size={16} />}
            rightSection={
              <Badge color="gray" variant="light" size="xs" ml={16}>
                {"ctrl + K"}
              </Badge>
            }
            onClick={() => combobox.toggleDropdown()}
          >
            {t("customerSearch.searchTitle")}
          </Button>
        </Combobox.Target>

        <Combobox.Dropdown>
          <Combobox.Search
            leftSection={<IconSearch size={16} />}
            onKeyDown={getHotkeyHandler([
              ["mod+K", handleClick],
              ["esc", handleClick],
              ["mod+L", quickAddLead],
            ])}
            value={search}
            onChange={(event) => setSearch(event.currentTarget.value)}
            placeholder={t("customerSearch.searchTitle")}
            m={16}
          />
          {isLoading || isRefetching ? (
            <DropdownLoader />
          ) : data &&
            data.length > 0 &&
            debouncedSearchTerm.length >= CHARACTER_MIN ? (
            <>
              <Combobox.Options>
                <ScrollArea.Autosize
                  mt={"xs"}
                  type="auto"
                  scrollbars="y"
                  scrollbarSize={8}
                  offsetScrollbars={true}
                  mah={"64vh"}
                >
                  {options}
                  {data &&
                    data.length > 20 &&
                    debouncedSearchTerm.length >= CHARACTER_MIN && (
                      <DropdownDataOverflowIndicator />
                    )}
                </ScrollArea.Autosize>
              </Combobox.Options>
            </>
          ) : (
            <NoResultsShow searchTerm={debouncedSearchTerm} />
          )}
          <Combobox.Footer>
            <LeadsQuickAddWizardButton
              ref={quickAddLeadRef}
              closeCombobox={combobox.closeDropdown}
            />
          </Combobox.Footer>
        </Combobox.Dropdown>
      </Combobox>
    </>
  );
}

function NoResultsShow({ searchTerm }: { searchTerm: string }) {
  const { t } = useTranslation("features");
  return searchTerm.length >= CHARACTER_MIN ? (
    <Box p={8} mt={8} className={classes.noResults}>
      <Center>{t("customerSearch.moreRecordsAvailable")}</Center>
    </Box>
  ) : (
    <Box p={8} mt={8} className={classes.noResults}>
      <Center>
        {t("customerSearch.pleaseSearch")}{" "}
        {`${CHARACTER_MIN - searchTerm.length} `}
        {t("customerSearch.charactersLeft")}
      </Center>
    </Box>
  );
}

function DropdownLoader() {
  return (
    <Box p={8} mt={8} className={classes.noResults}>
      <Center>
        <Loader />
      </Center>
    </Box>
  );
}

function DropdownDataOverflowIndicator() {
  const { t } = useTranslation("features");
  return (
    <Box m={16} className={classes.noResults}>
      <Divider />
      <Center m={16}>{t("customerSearch.moreRecordsAvailable")}</Center>
    </Box>
  );
}
