import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { BooleanRenderer } from "@/components/Table/CellRenderers/BooleanRenderer";

const PATH = "ContactRoles";
export function ContactRoleListInner({
  resourcePath,
  createPath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["ContactRoleRetrieveDto"],
        Schemas["ContactRoleRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="contactRole"
        entityPath="contactRoles"
        title={t("contactRoles.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        columns={[
          {
            accessorKey: "name",
            header: t("contactRoles.name"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "contactRoles"),
          },
          {
            accessorKey: "isMain",
            header: t("contactRoles.isMain"),
            Cell: (props) => BooleanRenderer(props),
            filterVariant: "multi-select",
            mantineFilterSelectProps: {
              data: [
                { value: "true", label: t("yes") },
                { value: "false", label: t("no") },
              ],
            },
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        redirectTo={window.location.pathname}
        initialSorting={[
          {
            id: "name",
            desc: false,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function ContactRoleList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <ContactRoleListInner
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
