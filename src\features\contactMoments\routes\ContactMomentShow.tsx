import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import {
  ContactMomentForm,
  type FormSchema,
} from "../components/ContactMomentForm";
import { filterFalsyValues } from "@/utils/filters";
import { notifications } from "@mantine/notifications";
import { Loader } from "@mantine/core";
import { useParams } from "react-router-dom";

interface ContactMomentShowProps {
  refreshForm?: () => void;
  closeModal?: () => void;
  contactMomentId?: string | null;
}

export function ContactMomentShow({
  refreshForm,
  closeModal,
  contactMomentId,
}: ContactMomentShowProps) {
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const idToUse = contactMomentId ?? id;
  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["ContactMoment"]>({
    resourcePath: "/api/ContactMoments/{id}",
    resourceId: idToUse!,
    queryKey: "contactMoment",
  });

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["ContactMoment"],
    Schemas["ContactMomentCreateDto"]
  >({
    resourcePath: "/api/ContactMoments/{id}",
    resourceId: idToUse!,
    queryKey: "contactMoment",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/ContactMoments/{id}",
    resourceId: idToUse!,
    queryKey: "contactMoment",
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <ContactMomentForm
      isCreate={false}
      closeModal={closeModal}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        update(values as Schemas["ContactMomentCreateDto"], {
          onSuccess: () => {
            if (refreshForm) {
              refreshForm();
            }
            notifications.show({
              color: "green",
              title: t("notifications.updateSuccessTitle"),
              message: t("notifications.updateSuccessMessage"),
            });
          },
        });
      }}
      onDelete={async () => {
        await mutateAsync();
        if (!isDeleteError) {
          if (refreshForm) {
            refreshForm();
          }
        }
      }}
    />
  );
}
