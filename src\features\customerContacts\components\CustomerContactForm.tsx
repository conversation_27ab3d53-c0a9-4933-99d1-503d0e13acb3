import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";
import { ContactRoleLookup } from "@/components/Lookup/Features/ContactRoles/ContactRoleLookup";

const formSchema = z.object({
  id: z.string(),
  customerId: z.string().nullable(),
  customer: z.object({}).nullable(),
  contactId: z.string().nullable(),
  contact: z.object({}).nullable(),
  contactRoleId: z.string().nullable(),
  contactRole: z.object({}).nullable(),
});

type FormSchema = z.infer<typeof formSchema>;

interface CustomerContactFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function CustomerContactForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: CustomerContactFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      customerId: initialValues?.customerId ?? "",
      customer: initialValues?.customer ?? null,
      contactId: initialValues?.contactId ?? "",
      contact: initialValues?.contact ?? null,
      contactRoleId: initialValues?.contactRoleId ?? "",
      contactRole: initialValues?.contactRole ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="lg">
              <CustomerLookup
                mt="sm"
                label={t("customers.customer")}
                initial={form.getValues().customer}
                initialId={form.getValues().customerId}
                identifier="customerIdCustomerContact"
                {...form.getInputProps("customerId")}
              />
              <ContactLookup
                mt="sm"
                label={t("customers.contact")}
                initial={form.getValues().contact}
                initialId={form.getValues().contactId}
                identifier="contactIdCustomerContact"
                {...form.getInputProps("contactId")}
              />
              <ContactRoleLookup
                mt="sm"
                label={t("contactRoles.contactRole")}
                initial={form.getValues().contactRole}
                initialId={form.getValues().contactRoleId}
                identifier="contactRoleIdCustomerContact"
                {...form.getInputProps("contactRoleId")}
              />
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
