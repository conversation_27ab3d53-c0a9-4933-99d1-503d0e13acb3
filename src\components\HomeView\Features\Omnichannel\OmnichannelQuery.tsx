import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";
import { useEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";

export default function OmnichannelQuery() {
  const { finalUser } = useUserContext();
  const { data } = useEntityListQuery<Schemas["AppUserRetrieveDtoPagedList"]>({
    resourcePath: "/api/AppUsers",
    params: {
      filter: `omnichannelIsActive == true`,
    },
    queryKey: `omnichannelListHome`,
  });

  const activeUsers = data?.data ?? [];
  const totalCount = data?.totalCount;
  const userStatus = finalUser?.omnichannelIsActive;
  return { totalCount, userStatus, activeUsers };
}
