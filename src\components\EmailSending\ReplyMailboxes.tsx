import React from "react";
import { type QueryParams } from "@/features/entity/api";
import { useInfiniteEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Loader, Select } from "@mantine/core";
import { t } from "i18next";

interface ReplyMailboxesProps {
  businessUnitId: string;
  onChange: (value: string | null) => void;
  value: string | null;
  prelimanaryMailbox?: string | null;
}

export default function ReplyMailboxes({
  businessUnitId,
  prelimanaryMailbox,
  onChange,
  value,
}: ReplyMailboxesProps) {
  const queryParams: QueryParams = {
    pageSize: 50,
  };
  queryParams.filter = `BusinessUnit.id == ${businessUnitId}`;

  const { data, isFetching } = useInfiniteEntityListQuery<
    Schemas["MailboxRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Mailboxes",
    queryKey: "mailboxes_" + businessUnitId,
    params: queryParams,
    enabled: !!businessUnitId,
  });

  const mailboxOptions = React.useMemo(() => {
    if (!data) return [];

    return data.pages.flatMap(
      (page) =>
        page?.data
          ?.filter((mailbox) => mailbox.id && mailbox.email)
          .map((mailbox) => ({
            value: mailbox.id!,
            label: mailbox.email!,
          })) ?? [],
    );
  }, [data]);

  if (isFetching) {
    return <Loader />;
  }
  return (
    <Select
      required
      flex={1}
      label={t("features:cases.replyMailboxes")}
      placeholder={t("features:emails.pleaseSelectMailbox")}
      data={mailboxOptions}
      defaultValue={prelimanaryMailbox ?? value}
      maxDropdownHeight={280}
      onChange={(value: string | null) => onChange(value)}
      value={prelimanaryMailbox ?? value}
    />
  );
}
