export type PageName =
  | "START"
  | "SHOW_UP"
  | "SPACETOUR"
  | "UPDATE"
  | "COMPLETED"
  | "CANCEL"
  | "RESCHEDULE";

export const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  // Months are 0-indexed so add 1 and pad with 0 if needed
  const month = ("0" + (date.getMonth() + 1)).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const hours = ("0" + date.getHours()).slice(-2);
  const minutes = ("0" + date.getMinutes()).slice(-2);
  return { date: `${year}/${month}/${day}`, time: `${hours}:${minutes}` };
};

export function generateFullDayTimes(interval = 15): string[] {
  const times: string[] = [];
  for (let hour = 6; hour < 22; hour++) {
    for (let min = 0; min < 60; min += interval) {
      const hh = hour.toString().padStart(2, "0");
      const mm = min.toString().padStart(2, "0");
      times.push(`${hh}:${mm}`);
    }
  }
  return times;
}

export function generateTimeSlotsFrom(
  startTime: Date,
  interval = 15,
): string[] {
  const times: string[] = [];
  const startMinutes = startTime.getHours() * 60 + startTime.getMinutes() + 15;
  const endMinutes = startMinutes + 120;

  for (let minutes = startMinutes; minutes <= endMinutes; minutes += interval) {
    const hour = Math.floor(minutes / 60) % 24;
    const minute = minutes % 60;
    times.push(
      `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`,
    );
  }
  return times;
}

export function timeStringToMinutes(time: string): number {
  const [hh, mm] = time.split(":").map(Number);
  return hh! * 60 + mm!;
}
