import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { QuotesColumns } from "../table/QuoteColumns";

const PATH = "Quotes";

export function QuoteListInner({
  visibleColumns,
  resourcePath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["QuoteRetrieveDto"],
        Schemas["QuoteRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="quote"
        entityPath="quotes"
        title={t("quotes.title")}
        redirectTo={window.location.pathname}
        visibleColumns={visibleColumns}
        columns={QuotesColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function QuoteList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <QuoteListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
