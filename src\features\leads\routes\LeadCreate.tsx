import { useEntityCreateMutation } from "@/features/entity/mutations";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { LeadForm } from "../components/LeadForm";
import { EntityLayout } from "@/features/entity";
import { notifications } from "@mantine/notifications";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Group } from "@mantine/core";
import { modals } from "@mantine/modals";
import { GetAllSearchParams } from "@/utils/urlParamsUtils";
import UseUser from "@/hooks/useUser";

export function LeadCreate() {
  const { t } = useTranslation("features");
  const [close, setClose] = useState(false);
  const [loading, setLoading] = useState(false);
  const { finalUser } = UseUser();
  const navigate = useNavigate();
  const { mutate } = useEntityCreateMutation<
    Schemas["Lead"],
    Schem<PERSON>["LeadCreateDto"]
  >({ resourcePath: "/api/Leads", queryKey: "lead" });
  return (
    <LeadForm
      isCreate={true}
      title={t("leads.createTitle")}
      onSubmit={(values) => {
        setLoading(true);
        const filteredValues = filterFalsyValues(values);
        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/leads");
            } else {
              if (
                Date.now() - new Date(data.data.createdOn!).getTime() >
                60000
              ) {
                navigate("/app/leads/" + data.data.id);

                modals.open({
                  title: t("leads.existingLeadFound"),
                  children: (
                    <div>
                      <p>{t("leads.redirectedToExistingLead")}</p>
                    </div>
                  ),
                });
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
                navigate("/app/leads/" + data.data.id);
              }
            }
          },
          onSettled: () => {
            setLoading(false);
          },
        });
      }}
      initialValues={{
        ...GetAllSearchParams(),
        businessUnitId: finalUser?.businessUnitId ?? "",
        businessUnit: finalUser?.businessUnit ?? null,
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} loading={loading} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
