import { useCallback, useEffect, useRef } from "react";

export function useInView(callback: () => void, pageSize: number) {
  const observerTarget = useRef(null);

  const onIntersection = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      if (entries.length === 0) {
        return;
      }
      if (entries.length <= pageSize) {
        return;
      }
      if (entries[0]?.isIntersecting) {
        callback();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [callback],
  );

  useEffect(() => {
    const observer = new IntersectionObserver(onIntersection);

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [onIntersection]);

  return observerTarget;
}
