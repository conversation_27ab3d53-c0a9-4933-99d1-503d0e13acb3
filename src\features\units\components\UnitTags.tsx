import { useState, useEffect } from "react";
import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import {
  Group,
  Title,
  Button,
  Modal,
  Loader,
  Tooltip,
  Stack,
  Badge,
  Box,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useEntityListQuery } from "@/features/entity/queries";
import { useEntityPostMutation } from "@/features/entity/mutations";
import { notifications } from "@mantine/notifications";
import UseAllTags from "@/hooks/useAllTags";

interface UnitTagsProps {
  unitId: string | undefined;
}

export function UnitTags({ unitId }: UnitTagsProps) {
  const { t } = useTranslation("features");
  const [showModal, setShowModal] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [tags, setTags] = useState<Schemas["Tag"][]>([]);
  const [unitTags, setUnitTags] = useState<string[]>([]);
  const [tagsChanged, setTagsChanged] = useState(false);
  const [tableKey, setTableKey] = useState<string>(Date.now().toString());
  const toggleModal = () => {
    if (showModal) {
      resetTags();
    }
    setShowModal(!showModal);
  };

  const { allTagsResponse, allTagsLoading, refetchAllTags } = UseAllTags();

  const {
    data: unitTagsResponse,
    isLoading: unitTagsLoading,
    refetch: refetchUnitTags,
  } = useEntityListQuery<Schemas["UnitTagRetrieveDtoPagedList"]>({
    resourcePath: `/api/units/${unitId}/unitTags` as PathKeys,
    queryKey: "unitTags",
  });

  useEffect(() => {
    if (showModal && tagsChanged) {
      void refetchUnitTags();
    }
  }, [
    showModal,
    unitTagsResponse,
    unitTagsLoading,
    refetchUnitTags,
    tagsChanged,
  ]);

  useEffect(() => {
    if (showModal && !allTagsResponse && !allTagsLoading) {
      void refetchAllTags();
    }
  }, [showModal, allTagsResponse, allTagsLoading, refetchAllTags]);

  useEffect(() => {
    if (allTagsResponse) {
      setTags(
        allTagsResponse.data?.sort((a, b) =>
          a.name && b.name ? a.name.localeCompare(b.name) : 0,
        ) ?? [],
      );
    }
  }, [allTagsResponse]);

  useEffect(() => {
    if (unitTagsResponse) {
      const unitTagsIds = unitTagsResponse.data?.map((ut) => ut.tag?.id) ?? [];
      setUnitTags(unitTagsIds.filter(Boolean));
      setSelectedTags(unitTagsIds.filter(Boolean));
      setOriginalTags(unitTagsIds.filter(Boolean));
    }
  }, [unitTagsResponse]);

  const handleTagSelect = (tagId: string) => {
    setSelectedTags((prevSelectedTags) =>
      prevSelectedTags.includes(tagId)
        ? prevSelectedTags.filter((id) => id !== tagId)
        : [...prevSelectedTags, tagId],
    );
  };
  const resetTags = () => {
    setSelectedTags(originalTags);
    setToggleState("neutral");
  };

  const { mutate: assignTags } = useEntityPostMutation<
    Schemas["Tag"],
    string[]
  >({
    resourcePath: `/api/Tags/${unitId}/addTagsToUnit`,
    queryKey: "assignTags",
  });

  const { mutate: removeTags } = useEntityPostMutation<
    Schemas["Tag"],
    string[]
  >({
    resourcePath: `/api/Tags/${unitId}/removeTagsFromUnit`,
    queryKey: "removeTags",
  });

  function handleAssignTags() {
    const tagsToAdd = selectedTags.filter((tag) => !unitTags.includes(tag));
    const tagsToRemove = unitTags.filter((tag) => !selectedTags.includes(tag));

    if (tagsToAdd.length === 0 && tagsToRemove.length === 0) {
      notifications.show({
        color: "red",
        title: t("appUsers.nothingChangedTitle"),
        message: t("appUsers.nothingChangedMessage"),
      });
    } else {
      setTagsChanged(true);

      if (tagsToAdd.length > 0) {
        assignTags(tagsToAdd, {
          onSuccess: () => {
            notifications.show({
              color: "green",
              title: t("tags.successTitle"),
              message: t("tags.successMessage"),
            });
            setTableKey(Date.now().toString());
            void refetchUnitTags();
          },
        });
      }

      if (tagsToRemove.length > 0) {
        removeTags(tagsToRemove, {
          onSuccess: () => {
            notifications.show({
              color: "green",
              title: t("tags.successTitle"),
              message: t("tags.successMessage"),
            });
            setTableKey(Date.now().toString());
            void refetchUnitTags();
          },
        });
      }

      toggleModal();
    }
  }
  const [originalTags, setOriginalTags] = useState<string[]>([]);
  const [toggleState, setToggleState] = useState<
    "neutral" | "selectAll" | "deselectAll"
  >("neutral");
  const toggleSelectAll = () => {
    if (toggleState === "neutral") {
      setSelectedTags(tags.map((tag) => tag.id ?? ""));
      setToggleState("selectAll");
    } else if (toggleState === "selectAll") {
      setSelectedTags([]);
      setToggleState("deselectAll");
    } else {
      setSelectedTags(originalTags);
      setToggleState("neutral");
    }
  };

  return (
    <Box>
      <Modal
        opened={showModal}
        onClose={toggleModal}
        centered
        title={t("tags.unitTags")}
        size="sm"
      >
        {allTagsLoading || unitTagsLoading ? (
          <Loader />
        ) : (
          <Stack>
            <Group>
              {tags.map((tag) => (
                <Badge
                  key={tag.id}
                  color={
                    selectedTags.includes(tag.id ?? "") ? "primary" : "gray"
                  }
                  onClick={() => handleTagSelect(tag.id ?? "")}
                  style={{ cursor: "pointer" }}
                  size="lg"
                >
                  {tag.name}
                </Badge>
              ))}
            </Group>
            <Tooltip
              label={
                toggleState === "neutral"
                  ? t("tags.selectAll")
                  : toggleState === "selectAll"
                    ? t("tags.deselectAll")
                    : t("tags.originalState")
              }
              position="bottom"
            >
              <Badge
                color={"primary"}
                style={{ cursor: "pointer" }}
                onClick={toggleSelectAll}
                variant="outline"
                radius="sm"
              >
                {toggleState === "neutral" && t("tags.selectAll")}
                {toggleState === "selectAll" && t("tags.deselectAll")}
                {toggleState === "deselectAll" && t("tags.reset")}
              </Badge>
            </Tooltip>
            <Button onClick={handleAssignTags} mt="md">
              {t("tags.confirm")}
            </Button>
          </Stack>
        )}
      </Modal>

      <Group mb="lg" flex={1} justify="space-between">
        <Title order={4}>{t("units.tags")}</Title>
        <Button onClick={toggleModal} variant="outline">
          {t("tags.manageTags")}
        </Button>
      </Group>
      <EntityLayout.TableMantine<
        Schemas["UnitTagRetrieveDto"],
        Schemas["UnitTagRetrieveDtoPagedList"]
      >
        resourcePath={`/api/Units/${unitId}/unitTags` as PathKeys}
        queryKey="unitTag"
        toolbarEnabled={false}
        selectionEnabled={false}
        key={tableKey}
        entityPath="unitTags"
        columns={[
          {
            accessorKey: "tag.name",
            header: t("tags.name"),
            filterVariant: "text",
          },
        ]}
      />
    </Box>
  );
}
