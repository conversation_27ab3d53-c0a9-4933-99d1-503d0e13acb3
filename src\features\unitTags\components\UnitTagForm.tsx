import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { UnitLookup } from "@/components/Lookup/Features/Units/UnitLookupField";
import { TagLookup } from "@/components/Lookup/Features/Tags/TagLookupField";

const formSchema = z.object({
  unitId: z.string().nullable(),
  unit: z.object({}).nullable(),
  tagId: z.string().nullable(),
  tag: z.object({}).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface UnitTagFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function UnitTagForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: UnitTagFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      unitId: initialValues?.unitId ?? "",
      unit: initialValues?.unit ?? null,
      tagId: initialValues?.tagId ?? "",
      tag: initialValues?.tag ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="lg">
              <FieldValidation isDirty={form.isDirty("unitId")}>
                <UnitLookup
                  mt="sm"
                  required
                  label={t("units.unit")}
                  initial={form.getValues().unit}
                  initialId={form.getValues().unitId}
                  identifier="unitIdUnitTag"
                  {...form.getInputProps("unitId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("tagId")}>
                <TagLookup
                  mt="sm"
                  required
                  label={t("tags.tag")}
                  initial={form.getValues().tag}
                  initialId={form.getValues().tagId}
                  identifier="tagIdUnitTag"
                  {...form.getInputProps("tagId")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
