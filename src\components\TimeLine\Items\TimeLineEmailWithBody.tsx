import { type Schemas } from "@/types";
import { Accordion, Anchor, Box, Flex, Modal, rem, Text } from "@mantine/core";
import { IconMail } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { TimelineEmailPanel } from "./Panels/TimelineEmailPanel";
import { DateTime } from "luxon";
import { EmailShow } from "@/features/emails/routes/EmailShow";
import { useDisclosure } from "@mantine/hooks";
import { lowerCaseNthLetter } from "@/utils/filters";

interface TimelineEmailProps {
  activity: Schemas["TimelineRetrieveDto"];
  openActivities: string[];
}
export function TimelineEmailWithBody({
  activity,
  openActivities,
}: TimelineEmailProps) {
  const { t } = useTranslation("features");
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] =
    useDisclosure(false);

  function lowerCaseFirstLetter(value: string) {
    if (!value) return value;
    return value.charAt(0).toLowerCase() + value.slice(1);
  }
  const createdOn = DateTime.fromJSDate(new Date(activity.createdOn ?? ""));

  return (
    <>
      <Modal
        centered
        withCloseButton={true}
        size="60%"
        opened={editModalOpened}
        onClose={closeEditModal}
        overlayProps={{
          backgroundOpacity: 0.55,
          blur: 3,
        }}
      >
        <EmailShow emailId={activity.id} />
      </Modal>
      <Accordion.Item
        value={activity.id ?? ""}
        style={{ backgroundColor: "#fff" }}
      >
        <Accordion.Control>
          <Flex justify="space-between" align="center" direction="row">
            <Text mr={4} fw={600} size={"xs"} style={{ whiteSpace: "nowrap" }}>
              {createdOn.toFormat("HH:mm:ss")}
            </Text>
            <Anchor
              style={{ cursor: "pointer" }}
              onClick={(event) => {
                event.stopPropagation();
                openEditModal();
              }}
            >
              <Flex align="center">
                <IconMail
                  style={{
                    width: rem(15),
                    height: rem(15),
                    marginRight: rem(5),
                  }}
                />
                {t(
                  "leads." +
                    lowerCaseFirstLetter(
                      activity.timelineActivityType as string,
                    ),
                ) + " "}
                {activity.displayTitle}
              </Flex>
            </Anchor>
            <Box ml="auto" style={{ whiteSpace: "nowrap" }}>
              {t("emails." + lowerCaseNthLetter(activity.status ?? ""))}
            </Box>
          </Flex>
        </Accordion.Control>

        <Accordion.Panel>
          {openActivities.includes(activity.id!) && (
            <TimelineEmailPanel emailId={activity.id!} showHtml />
          )}
        </Accordion.Panel>
      </Accordion.Item>
    </>
  );
}
