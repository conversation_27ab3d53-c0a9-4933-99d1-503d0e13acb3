import { Route, Routes } from "react-router-dom";
import { RefundShow } from "@/features/refunds/routes/RefundShow";
import { RefundList } from "@/features/refunds/routes/RefundList";
import { RefundCreate } from "@/features/refunds/routes/RefundCreate";

export default function RefundsRoutes() {
  return (
    <Routes>
      <Route index element={<RefundList />} />
      <Route path=":id" element={<RefundShow />} />
      <Route path="create" element={<RefundCreate />} />
    </Routes>
  );
}
