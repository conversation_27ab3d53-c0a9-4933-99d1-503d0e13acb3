export function GetBusinessUnitTypeFilter(
  filterBUbyAppointmentType: string | null | undefined,
) {
  let businessUnitTypeFilter = undefined;
  if (filterBUbyAppointmentType === "EngineRoomTour") {
    businessUnitTypeFilter = "hasEngineRoom == true";
  } else if (filterBUbyAppointmentType === "RoboticStorageTour") {
    businessUnitTypeFilter = "hasRoboticStorage == true";
  } else if (filterBUbyAppointmentType === "BankSafe") {
    businessUnitTypeFilter = "hasVault == true";
  } else if (
    filterBUbyAppointmentType === "BlockCalendar" ||
    filterBUbyAppointmentType === "MovingHelpBlockCalendar"
  ) {
    businessUnitTypeFilter = "";
  } else if (
    filterBUbyAppointmentType === "SpaceTour" ||
    filterBUbyAppointmentType === "MovingVan" ||
    filterBUbyAppointmentType === "Trailer" ||
    filterBUbyAppointmentType === "Desk" ||
    filterBUbyAppointmentType === "FollowUp"
  ) {
    businessUnitTypeFilter = "isSelfStorage == true";
  } else {
    businessUnitTypeFilter = "";
  }

  if (businessUnitTypeFilter == "") {
    businessUnitTypeFilter = businessUnitTypeFilter + "IsHidden != True";
  } else {
    businessUnitTypeFilter = businessUnitTypeFilter + " && IsHidden != True";
  }
  return businessUnitTypeFilter;
}
