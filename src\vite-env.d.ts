/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_PUBLIC_API_URL: string;
  readonly VITE_LOCIZE_PROJECT_ID: string;
  readonly VITE_LOCIZE_API_KEY: string;
  readonly VITE_AUTH0_DOMAIN: string;
  readonly VITE_AUTH0_CLIENT_ID: string;
  readonly VITE_AUTH0_REDIRECT_URI: string;
  readonly VITE_AUTH0_AUDIENCE: string;
  readonly VITE_LOCIZE_PROJECT_ID: string;
  readonly VITE_LOCIZE_API_KEY: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
