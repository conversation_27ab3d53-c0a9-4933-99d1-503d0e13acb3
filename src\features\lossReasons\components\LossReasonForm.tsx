import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Select, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields, recordState } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { HtmlTemplateLookup } from "@/components/Lookup/Features/HtmlTemplates/HtmlTemplateLookup";
import { EntityState } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";

const recordStateEnum = z.enum(recordState as [string]);

const formSchema = z.object({
  name: z.string().nullable(),
  htmlTemplateId: z.string().nullable(),
  htmlTemplate: z.object({}).nullable(),
  recordState: recordStateEnum.refine((value) => !!value),
});

export type FormSchema = z.infer<typeof formSchema>;

interface LossReasonFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function LossReasonForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: LossReasonFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      name: initialValues?.name ?? "",
      htmlTemplateId: initialValues?.htmlTemplateId ?? "",
      htmlTemplate: initialValues?.htmlTemplate ?? null,
      recordState: initialValues?.recordState ?? "",
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        disabledActionSection={true}
        actionSection={actionSection}
      >
        <Grid m={16}>
          <Grid.Col span={{ base: 6, md: 4 }}>
            <Paper shadow="xs" p="lg" h="100%">
              <FieldValidation isDirty={form.isDirty("name")}>
                <TextInput
                  disabled
                  label={t("lossReasons.name")}
                  {...form.getInputProps("name")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("htmlTemplateId")}>
                <HtmlTemplateLookup
                  flex={1}
                  mt="sm"
                  label={t("lossReasons.htmlTemplate")}
                  initial={form.getValues().htmlTemplate}
                  initialId={form.getValues().htmlTemplateId}
                  identifier="htmlTemplateIdLossReason"
                  {...form.getInputProps("htmlTemplateId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("recordState")}>
                <Select
                  label={t("common.status")}
                  data={EntityState.map((value) => ({
                    value,
                    label: t(getEnumTransKey("common", value)),
                  }))}
                  value={form.values.recordState}
                  {...form.getInputProps("recordState")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
