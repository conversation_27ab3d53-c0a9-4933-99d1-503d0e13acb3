import {
  Combobox,
  Group,
  Box,
  Center,
  Checkbox,
  Highlight,
} from "@mantine/core";
import { isScrollThreasholdReached } from "../utils";

export const OptionGroupRender = (
  id: string,
  index: number,
  display: string,
  entityCount: number,
  infiniteScrollRef: React.RefObject<HTMLDivElement>,
  options: JSX.Element[],
) => {
  return (
    <Combobox.Option
      value={id}
      key={id}
      display={display}
      ref={
        isScrollThreasholdReached(index, entityCount)
          ? infiniteScrollRef
          : undefined
      }
    >
      <Group gap="sm">{options}</Group>
    </Combobox.Option>
  );
};

export const ComboboxOptionGroupRender = (
  label: string,
  options: JSX.Element[],
) => {
  return (
    <Combobox.Group
      styles={{
        groupLabel: {
          fontWeight: 600,
          fontSize: "0.85rem",
          padding: "6px 16px",
          background: "#f8f8f8",
          borderBottom: "1px solid #ddd",
          borderTop: "1px solid #ddd",
          color: "#000",
        },
      }}
      key={label}
      label={label}
    >
      {options}
    </Combobox.Group>
  );
};

export const BoolOptionRender = (value: boolean, id?: string, flex = 1) => {
  return (
    <Box flex={flex} key={id}>
      <Center w={"100%"}>
        <Checkbox readOnly checked={value} />
      </Center>
    </Box>
  );
};

export const OptionRender = (
  value: string | null | undefined,
  searchTerm: string,
  id?: string,
  flex = 1,
) => {
  return (
    <Box flex={flex} key={id}>
      <Highlight highlight={searchTerm} size="sm">
        {value ?? ""}
      </Highlight>
    </Box>
  );
};
