import { type Dispatch, type SetStateAction, useState } from "react";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Box, Button, Flex, ScrollArea, Textarea, Title } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields, recordState } from "@/features/entity/utils";
import { type Schemas } from "@/types";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";

const recordStateEnum = z.enum(recordState as [string]);

const formSchema = z.object({
  id: z.string(),
  comment: z.string().min(1).max(1000),
  leadId: z.string().nullable(),
  lead: z.object({}).nullable(),
  recordState: recordStateEnum.refine((value) => !!value),
});

export type FormSchema = z.infer<typeof formSchema>;

interface ContactMomentFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  onDelete?: VoidFunction;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  isCreate: boolean;
  showBackButton?: boolean;
  closeModal?: () => void;
  isModal?: boolean;
  setPhantomEvent?: Dispatch<SetStateAction<Schemas["ContactMoment"] | null>>;
}

export function ContactMomentForm({
  onSubmit,
  initialValues,
  isCreate,
  isModal,
}: ContactMomentFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const [formKey] = useState(0);
  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      comment: initialValues?.comment ?? "",
      leadId: initialValues?.leadId ?? "",
      lead: initialValues?.lead ?? null,
      recordState: initialValues?.recordState ?? "",
    },
    initialDirty: { startDate: true },
    validate: zodResolver(formSchema),
  });

  const isFormDisabled =
    !isCreate && form.getInputProps("recordState").value === "Inactive";

  return (
    <form
      key={formKey}
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        filteredFields.recordState = form.values.recordState;
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <Flex justify="space-between" align="center" direction="row">
        <Title order={3}>
          {isCreate
            ? t("contactMoments.createContactMoment")
            : t("contactMoments.contactMoment")}
        </Title>
      </Flex>
      <ScrollArea
        scrollbarSize={4}
        type="hover"
        mah={
          document.location.pathname.includes("app/contactMoments")
            ? "74vh"
            : isModal
              ? "74vh"
              : "56vh"
        }
      >
        <Box
          mr={10}
          style={
            isFormDisabled ? { pointerEvents: "none", opacity: "0.6" } : {}
          }
        >
          <Flex gap={"sm"}>
            <LeadLookup
              flex={1}
              mt="sm"
              disabled={!isCreate}
              label={t("contactMoments.lead")}
              initial={form.getValues().lead}
              initialId={form.getValues().leadId}
              identifier="leadIdContactMoment"
              {...form.getInputProps("leadId")}
              {...{ labelProps: { style: { flex: "1.5" } } }}
            />
          </Flex>
          <Textarea
            mt="sm"
            disabled={!isCreate}
            label={t("contactMoments.comment")}
            {...form.getInputProps("comment")}
            minRows={4}
            autosize
            {...{ labelProps: { style: { flex: "0.675" } } }}
          />
        </Box>
        <Button
          variant="filled"
          disabled={!isCreate}
          w={"100%"}
          style={!isCreate ? { pointerEvents: "none", opacity: "0.6" } : {}}
          type="submit"
        >
          {t("common.save")}
        </Button>
      </ScrollArea>
      <Flex
        mt={10}
        mr={10}
        gap="xs"
        justify="center"
        align="center"
        direction="row"
        wrap="nowrap"
      ></Flex>
    </form>
  );
}
