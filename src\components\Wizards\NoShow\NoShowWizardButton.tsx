import NoShowWizard from "./NoShowWizard";
import { IconMenuDeep } from "@tabler/icons-react";
import { type Schemas } from "@/types";
import { useEntityQuery } from "@/features/entity/queries";
import WizardOverlay from "../Common/WizardOverlay";
import { ActionIcon } from "@mantine/core";

interface NoShowWizardMainProps {
  appointmentId: string;
  leadId: string;
  disabled?: boolean;
}

export default function NoShowWizardButton({
  appointmentId,
  leadId,
  disabled = false,
}: NoShowWizardMainProps) {
  const { data: lead, isLoading: loadingLead } = useEntityQuery<
    Schemas["LeadRetrieveDto"]
  >({
    resourcePath: "/api/Leads/{id}",
    resourceId: leadId,
    queryKey: ["lead", leadId],
  });

  const { data: appointment, isLoading: loadingAppointment } = useEntityQuery<
    Schemas["AppointmentRetrieveDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: appointmentId,
    queryKey: ["appointment", appointmentId],
  });

  return (
    <WizardOverlay
      WizardContent={<NoShowWizard lead={lead!} appointment={appointment!} />}
      WizardTarget={
        <ActionIcon
          variant="filled"
          disabled={disabled}
          color="primary"
          size="lg"
          loading={loadingLead || loadingAppointment}
        >
          <IconMenuDeep style={{ width: "60%", height: "60%" }} />
        </ActionIcon>
      }
      onClick={() => {
        // leave this as an example
        // pass this if you want to do something on child level
      }}
    />
  );
}
