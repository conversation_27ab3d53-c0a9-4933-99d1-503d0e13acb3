/* Light - Weight 300 */
@font-face {
  font-family: "DIN";
  src:
    url("../assets/fonts/DIN Light.ttf") format("truetype"),
    url("../assets/fonts/DIN LightAlternate.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
}

/* Regular - Weight 400 */
/* Including DIN-Regular, DIN RegularAlternate, and the ambiguous DIN.ttf */
@font-face {
  font-family: "DIN";
  src:
    url("../assets/fonts/DIN-Regular.ttf") format("truetype"),
    url("../assets/fonts/DIN RegularAlternate.ttf") format("truetype"),
    url("../assets/fonts/DIN.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

/* Medium - Weight 500 */
@font-face {
  font-family: "DIN";
  src:
    url("../assets/fonts/DIN Medium.ttf") format("truetype"),
    url("../assets/fonts/DIN MediumAlternate.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

/* Bold - Weight 700 */
/* Combining DIN Bold, DIN BoldAlternate, and DINPro-Bold */
@font-face {
  font-family: "DIN";
  src:
    url("../assets/fonts/DIN Bold.ttf") format("truetype"),
    url("../assets/fonts/DIN BoldAlternate.ttf") format("truetype"),
    url("../assets/fonts/DINPro-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}

/* Black - Weight 900 */
@font-face {
  font-family: "DIN";
  src:
    url("../assets/fonts/DIN-Black.ttf") format("truetype"),
    url("../assets/fonts/DIN BlackAlternate.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}

:root {
  font-family: "DIN", Courier, monospace;
}

html,
body,
#root {
  height: 100%;
  width: 100%;
}

#root {
  background-color: rgb(255, 255, 255);
}

#sentry-feedback {
  --page-margin: 16px 16px 16px 240px;
  --inset: auto auto 0 0;
}
