import {
  type MRT_Column,
  type MRT_Row,
  type MRT_TableInstance,
  type MRT_Cell,
  type MRT_RowData,
} from "mantine-react-table";
import { type ReactNode, type RefObject } from "react";
import { format, parseISO, isValid } from "date-fns";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

function formatDate(dateString: string, dateFormat: string): string {
  const date = parseISO(dateString ?? "");
  return isValid(date) ? format(date, dateFormat) : "";
}

function formatDateTime(dateString: string, dateFormat: string): string {
  const date = parseISO(dateString ?? "");
  return isValid(date) ? format(date, dateFormat + " HH:mm:ss") : "";
}

export function DateRenderer(props: {
  cell: MRT_Cell<MRT_RowData, unknown>;
  column: MRT_Column<MRT_RowData, unknown>;
  renderedCellValue: ReactNode | number | string;
  renderedColumnIndex?: number;
  renderedRowIndex?: number;
  row: MRT_Row<MRT_RowData>;
  rowRef?: RefObject<HTMLTableRowElement>;
  table: MRT_TableInstance<MRT_RowData>;
}) {
  const { dateFormat } = useSettingsContext();
  const value = props.cell.getValue<string>();

  if (!value) {
    return "";
  }
  const date = parseISO(value ?? "");
  if (!isValid(date)) {
    return value;
  }

  const hasTimeComponent = !(
    date.getHours() === 0 &&
    date.getMinutes() === 0 &&
    date.getSeconds() === 0
  );

  return hasTimeComponent
    ? formatDateTime(value, dateFormat ?? "")
    : formatDate(value, dateFormat ?? "");
}
