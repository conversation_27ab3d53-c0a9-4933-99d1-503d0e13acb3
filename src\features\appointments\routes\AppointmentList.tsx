import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { type PathKeys, type Schemas } from "@/types";
import { AppointmentCalendarView } from "../components/AppointmentCalendarView";
import { useState } from "react";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import { AppointmentsColumns } from "../table/AppointmentsColumns";
import { AppointmentCreate } from "./AppointmentCreate";
import { Group } from "@mantine/core";
import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";

const PATH = "Appointments";
interface AppointmentListPropsInner extends InnerListProps {
  businessUnitId?: string;
}
export function AppointmentListInner({
  visibleColumns,
  resourcePath,
  createPath,
  parentEntityName,
  parentEntityId,
  businessUnitId,
}: AppointmentListPropsInner) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  const isRentableItemsList = resourcePath.includes("RentableItems");
  const [view] = useState<"calendar" | "list">(
    isRentableItemsList ? "list" : "calendar",
  );
  const { roles } = useUserContext();
  const isAdmin = roles.includes("Admin");
  const fromRentableItemContext = parentEntityName == "RentableItems";

  return (
    <EntityLayout stickyHeader={false} showBackButton={false}>
      {view === "calendar" ? (
        <AppointmentCalendarView />
      ) : (
        <EntityLayout.TableMantine<
          Schemas["AppointmentRetrieveDto"],
          Schemas["AppointmentRetrieveDtoPagedList"]
        >
          tableRef={tableRef}
          resourcePath={resourcePath as PathKeys}
          queryKey="appointment"
          entityPath="appointments"
          title={t("appointments.title")}
          searchDisabled={true}
          toolbar={
            isAdmin &&
            fromRentableItemContext && (
              <Group>
                <EntityLayout.CreateButton
                  to={createPath}
                  FormComponent={parentEntityId ? AppointmentCreate : undefined}
                  formProps={
                    parentEntityId
                      ? {
                          rentableItemId: parentEntityId,
                          isModal: true,
                          appointmentTypeInitial: isRentableItemsList
                            ? "BlockCalendar"
                            : null,
                          businessUnitId: businessUnitId ?? "",
                        }
                      : undefined
                  }
                  label={
                    fromRentableItemContext
                      ? "appointments.blockCalendar"
                      : undefined
                  }
                />
              </Group>
            )
          }
          redirectTo={window.location.pathname}
          visibleColumns={visibleColumns}
          columns={AppointmentsColumns()}
          initialSorting={[
            {
              id: "createdOn",
              desc: true,
            },
          ]}
        />
      )}
    </EntityLayout>
  );
}

interface AppointmentListPropsOutter extends OutterListProps {
  businessUnitId?: string;
}
export function AppointmentList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
  businessUnitId,
}: AppointmentListPropsOutter) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <AppointmentListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
        businessUnitId={businessUnitId}
        parentEntityName={parentEntityName}
      />
    </ListCommandsProvider>
  );
}
