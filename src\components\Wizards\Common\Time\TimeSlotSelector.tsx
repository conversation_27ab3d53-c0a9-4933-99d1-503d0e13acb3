import { Box, Tooltip } from "@mantine/core";
import { useState, useRef, useEffect } from "react";

interface TimeSlotSelectorProps {
  times: string[];
  disabledTimes?: string[];
  defaultSelectedStartTime?: string;
  defaultSelectedEndTime?: string;
  enableMultipleTimeSlots?: boolean;
  // Callback fired once selection is finalized.
  // In multi mode, start is the earlier and end is the later.
  // In single mode, both will be the same.
  onSelectTime?: (start: Date, end: Date) => void;
  // New prop: Use the passed date as the base for time conversion.
  selectedDate?: Date;
}

// Helper: convert a time string ("HH:MM") to a Date object using the provided base date (or today’s date if not provided)
const parseTimeToDate = (time: string, baseDate?: Date): Date => {
  const date = baseDate ? new Date(baseDate) : new Date();
  const [hourStr, minuteStr] = time.split(":");
  const hour = parseInt(hourStr!, 10);
  const minute = parseInt(minuteStr!, 10);
  date.setHours(hour, minute, 0, 0);
  return date;
};

// Helper: get duration label as "XH YM" (e.g. "2H 30M") between two times.
// It always computes the absolute difference.
const getDurationLabel = (
  time1: string,
  time2: string,
  baseDate?: Date,
): string => {
  const date1 = parseTimeToDate(time1, baseDate);
  const date2 = parseTimeToDate(time2, baseDate);
  const diffMs = Math.abs(date2.getTime() - date1.getTime());
  if (diffMs === 0) return "";
  const diffMinutes = Math.floor(diffMs / 60000);
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;
  let label = "";
  if (hours > 0) {
    label += `${hours}H `;
  }
  if (minutes > 0) {
    label += `${minutes.toString().padStart(2, "0")}M`;
  }
  return label.trim();
};

const TimeSlotSelector: React.FC<TimeSlotSelectorProps> = ({
  times,
  disabledTimes = [],
  defaultSelectedStartTime,
  defaultSelectedEndTime,
  enableMultipleTimeSlots = true,
  onSelectTime,
  selectedDate, // new prop to allow a custom date
}) => {
  const [startTime, setStartTime] = useState<string | null>(
    defaultSelectedStartTime || null,
  );
  const [endTime, setEndTime] = useState<string | null>(
    defaultSelectedEndTime || null,
  );
  // Only used in multiple-selection mode
  const [hoveredTime, setHoveredTime] = useState<string | null>(null);

  const itemRefs = useRef<Record<string, HTMLDivElement | null>>({});

  useEffect(() => {
    if (endTime) {
      const el = itemRefs.current[endTime];
      if (el) el.focus();
    } else if (startTime) {
      const el = itemRefs.current[startTime];
      if (el) el.focus();
    }
  }, [startTime, endTime]);

  const handleTimeClick = (time: string) => {
    // SINGLE SELECTION MODE: Replace any existing selection.
    if (!enableMultipleTimeSlots) {
      setStartTime(time);
      setEndTime(null);
      if (onSelectTime) {
        // Return the same date for both start and end using the selectedDate (or today)
        const parsed = parseTimeToDate(time, selectedDate);
        onSelectTime(parsed, parsed);
      }
      return;
    }

    // MULTIPLE SELECTION MODE: If no selection or both already selected, start over.
    if (!startTime || (startTime && endTime)) {
      setStartTime(time);
      setEndTime(null);
      return;
    }

    // With a first slot selected, allow any second click (different from the first)
    // then reorder them so the earlier time becomes start and the later becomes end.
    const firstIndex = times.indexOf(startTime);
    const secondIndex = times.indexOf(time);
    if (firstIndex === secondIndex) return; // same slot clicked

    const finalStartIndex = Math.min(firstIndex, secondIndex);
    const finalEndIndex = Math.max(firstIndex, secondIndex);
    const finalStartTime = times[finalStartIndex];
    const finalEndTime = times[finalEndIndex];

    setStartTime(finalStartTime!);
    setEndTime(finalEndTime!);
    setHoveredTime(null);

    if (onSelectTime) {
      onSelectTime(
        parseTimeToDate(finalStartTime!, selectedDate),
        parseTimeToDate(finalEndTime!, selectedDate),
      );
    }
  };

  // Compute the range to be colored (only in multi-selection mode)
  let rangeStartIndex: number | null = null;
  let rangeEndIndex: number | null = null;
  if (enableMultipleTimeSlots) {
    if (startTime && endTime) {
      const idx1 = times.indexOf(startTime);
      const idx2 = times.indexOf(endTime);
      rangeStartIndex = Math.min(idx1, idx2);
      rangeEndIndex = Math.max(idx1, idx2);
    } else if (startTime && hoveredTime) {
      const idx1 = times.indexOf(startTime);
      const idx2 = times.indexOf(hoveredTime);
      if (idx1 !== idx2) {
        rangeStartIndex = Math.min(idx1, idx2);
        rangeEndIndex = Math.max(idx1, idx2);
      }
    }
  }

  // Get tooltip label for the hovered slot, showing duration (only in multi mode).
  const getTooltipLabelForSlot = (time: string): string => {
    if (!enableMultipleTimeSlots) return "";
    if (disabledTimes.includes(time)) return "";
    if (time !== hoveredTime) return "";
    if (!startTime) return "";
    const firstIndex = times.indexOf(startTime);
    const hoveredIndex = times.indexOf(time);
    if (firstIndex === hoveredIndex) return "";
    const tooltipStart = firstIndex < hoveredIndex ? startTime : time;
    const tooltipEnd = firstIndex < hoveredIndex ? time : startTime;
    return getDurationLabel(tooltipStart, tooltipEnd, selectedDate);
  };

  return (
    <Box w="100%" style={{ display: "flex", flexDirection: "column" }}>
      {times.map((time, index) => {
        const isDisabled = disabledTimes.includes(time);
        let backgroundColor: string;
        let color: string;

        if (enableMultipleTimeSlots) {
          // MULTIPLE SELECTION MODE: Color the range if set.
          if (rangeStartIndex !== null && rangeEndIndex !== null) {
            if (index === rangeStartIndex || index === rangeEndIndex) {
              backgroundColor = "#006699";
              color = "#fff";
            } else if (index > rangeStartIndex && index < rangeEndIndex) {
              backgroundColor = "#006699";
              color = "#fff";
            } else if (time === startTime) {
              backgroundColor = "#006699";
              color = "#fff";
            } else if (isDisabled) {
              backgroundColor = "#f88885";
              color = "#333";
            } else {
              backgroundColor = "#f5f5f5";
              color = "#333";
            }
          } else {
            if (time === startTime) {
              backgroundColor = "#006699";
              color = "#fff";
            } else if (isDisabled) {
              backgroundColor = "#f88885";
              color = "#333";
            } else {
              backgroundColor = "#f5f5f5";
              color = "#333";
            }
          }
        } else {
          // SINGLE SELECTION MODE: Only highlight the selected slot.
          if (time === startTime) {
            backgroundColor = "#006699";
            color = "#fff";
          } else if (isDisabled) {
            backgroundColor = "#f88885";
            color = "#333";
          } else {
            backgroundColor = "#f5f5f5";
            color = "#333";
          }
        }

        const tooltipLabel = getTooltipLabelForSlot(time);

        const content = (
          <Box
            w="100%"
            ref={(el) => (itemRefs.current[time] = el)}
            onClick={() => handleTimeClick(time)}
            onMouseEnter={() => {
              if (
                enableMultipleTimeSlots &&
                startTime &&
                !endTime &&
                time !== startTime
              ) {
                setHoveredTime(time);
              }
            }}
            onMouseLeave={() => {
              if (enableMultipleTimeSlots && startTime && !endTime) {
                setHoveredTime(null);
              }
            }}
            tabIndex={0}
            style={{
              backgroundColor,
              cursor: "pointer",
              color,
              padding: "8px",
              textAlign: "center",
              marginBottom: "6px",
              opacity: isDisabled ? 0.5 : 1,
            }}
          >
            {time}
          </Box>
        );

        return tooltipLabel ? (
          <Tooltip key={time} label={tooltipLabel} withArrow>
            {content}
          </Tooltip>
        ) : (
          <div key={time}>{content}</div>
        );
      })}
    </Box>
  );
};

export default TimeSlotSelector;
