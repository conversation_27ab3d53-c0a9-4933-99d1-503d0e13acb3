import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { DateRenderer } from "@/components/Table/CellRenderers";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { ListCommandsProvider } from "@/hooks/useListCommands";
import { TagLookup } from "@/components/Lookup/Features/Tags/TagLookupField";
import { UnitLookup } from "@/components/Lookup/Features/Units/UnitLookupField";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

const PATH = "UnitTags";
export function UnitTagListInner({ resourcePath, createPath }: InnerListProps) {
  const { t } = useTranslation("features");

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["UnitTagRetrieveDto"],
        Schemas["UnitTagRetrieveDtoPagedList"]
      >
        resourcePath={resourcePath as PathKeys}
        queryKey="unitUnitTag"
        entityPath="unitUnitTags"
        title={t("unitTags.title")}
        redirectTo={window.location.pathname}
        toolbar={
          <>
            <EntityLayout.CreateButton to={createPath} />
          </>
        }
        columns={[
          {
            accessorKey: "tag",
            header: t("unitTags.tag"),
            ...TableRenderer(TagLookup, "tags", ["name"]),
          },
          {
            accessorKey: "unit",
            header: t("unitTags.unit"),
            ...TableRenderer(UnitLookup, "units", ["unitCode"]),
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function UnitTagList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <UnitTagListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
