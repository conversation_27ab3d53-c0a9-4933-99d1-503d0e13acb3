import { QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";
import { MantineProvider } from "@mantine/core";
import { Notifications } from "@mantine/notifications";
import { Suspense, type PropsWithChildren } from "react";
import { queryClient } from "@/lib/query";
import { theme } from "@/lib/theme";
import "@/lib/i18n";
import { ModalsProvider } from "@mantine/modals";
import { storage } from "@/utils/storage";
import { useTranslation } from "react-i18next";
import { CustomDatesProvider } from "./date";
import { ErrorProvider } from "./error";
import { UserContextProvider } from "@/components/Layout/Contexts/User/UserContext";
import useWebSockets from "@/websockets/useWebSockets";

export function AppProvider({ children }: PropsWithChildren) {
  const { i18n } = useTranslation();
  if (storage.getLanguage() !== i18n.language) {
    void i18n.changeLanguage(storage.getLanguage() as string);
  }
  useWebSockets(queryClient);
  return (
    <Suspense>
      <MantineProvider theme={theme}>
        <Notifications position="top-right" autoClose={1750} />
        <QueryClientProvider client={queryClient}>
          <UserContextProvider>
            {import.meta.env.DEV ? <ReactQueryDevtools /> : null}
            <ErrorProvider>
              <CustomDatesProvider language={i18n.language}>
                <ModalsProvider>{children}</ModalsProvider>
              </CustomDatesProvider>
            </ErrorProvider>
          </UserContextProvider>
        </QueryClientProvider>
      </MantineProvider>
    </Suspense>
  );
}
