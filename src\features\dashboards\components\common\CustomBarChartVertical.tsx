import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  ResponsiveContainer,
  Cell,
  type TooltipProps,
} from "recharts";
import { type Statistic } from "../../utils/types";
import {
  type NameType,
  type ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { Box, Flex, Paper, ScrollArea, Text } from "@mantine/core";
import { IconArrowsMaximize, IconArrowsMinimize } from "@tabler/icons-react";
import classes from "./report.module.css";
import { useCallback, useState } from "react";

interface CustomBarChartVerticalProps {
  data: Statistic[] | undefined;
  title: string;
  marginTop?: number;
  height?: number;
  width?: string;
  percentage?: boolean;
}

interface CustomChartTooltipProps extends TooltipProps<ValueType, NameType> {
  percentage?: boolean;
}

export default function CustomBarChartVertical({
  data,
  title,
  marginTop = 10,
  height = 270,
  width = "100%",
  percentage = false,
}: CustomBarChartVerticalProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const CustomTooltip = ({
    active,
    payload,
    label,
    percentage,
  }: CustomChartTooltipProps) => {
    if (active) {
      return (
        <Paper px="md" py="sm" withBorder shadow="md" radius="md">
          {payload!.map((item) => (
            <Text key={label as string} fz="h6" fw={700}>
              {label}: {item.value} {percentage && "%"}
            </Text>
          ))}
        </Paper>
      );
    }

    return null;
  };

  const xKey = "display";
  const yKey = percentage ? "percentage" : "value";
  const dataHeight = (data ? data.length : 1) * 40;
  const expandedStyle = {
    position: "fixed" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: "white",
    padding: "20px",
    overflow: "auto" as const,
  };

  const normalStyle = {
    border: "2px solid #e0e0e0",
    borderRadius: 4,
    height,
    width,
  };

  return (
    <Box
      style={isExpanded == true ? expandedStyle : normalStyle}
      mt={isExpanded ? 0 : marginTop}
    >
      <Flex dir="row">
        <Text fw={600} ml={4} flex={3} ta={"left"} size="sm">
          {title}
        </Text>
        <Box
          ta="right"
          mr={4}
          style={{ cursor: "pointer" }}
          onClick={toggleExpand}
        >
          {isExpanded ? (
            <IconArrowsMinimize size={14} />
          ) : (
            <IconArrowsMaximize size={14} />
          )}
        </Box>
      </Flex>

      <ScrollArea
        classNames={classes}
        scrollbarSize={6}
        type="always"
        scrollbars={"y"}
        h={isExpanded ? "calc(100% - 40px)" : "90%"}
      >
        <ResponsiveContainer
          width="98%"
          height={
            isExpanded
              ? "100%"
              : dataHeight < height
                ? height * 0.85
                : dataHeight
          }
        >
          <BarChart
            margin={{ left: -5, right: 10, bottom: -5 }}
            layout="vertical"
            data={data}
            barGap={"1%"}
            barSize={10}
          >
            <CartesianGrid horizontal={false} strokeDasharray="1 1" />
            <XAxis
              orientation="top"
              minTickGap={1}
              type="number"
              axisLine={false}
              tickLine={false}
              fontSize={12}
              fontWeight={600}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              dataKey={xKey}
              tickFormatter={(val: string) => {
                val = val.replace(/@/g, "\n@");
                val = val.replace(/ /g, "\n");
                return val;
              }}
              fontSize={10}
              interval={0}
              width={90}
              fontWeight={600}
              tickSize={0}
              type="category"
            ></YAxis>
            <Tooltip
              cursor={{ fill: "transparent" }}
              content={<CustomTooltip percentage={percentage} />}
            />
            <Bar dataKey={yKey}>
              {data?.map((_entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={"#228be6"}
                  strokeWidth={2}
                ></Cell>
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </ScrollArea>
    </Box>
  );
}
