import { useEffect } from "react";
import { useLookupContext } from "./LookupContext";

export function useLookup<T>(
  entityName: string,
  initialValue?: string | null,
  initialId?: string | null,
  entity?: T | null,
  initialization = false,
) {
  const { entities, updateEntity } = useLookupContext<T>();
  const entityState = entities[entityName] ?? {
    searchTerm: "",
    lookupValue: null,
    lookupId: null,
    lookupEntity: null,
  };

  useEffect(() => {
    return () => {
      updateEntity(entityName, {
        searchTerm: "",
        lookupValue: null,
        lookupId: null,
        lookupEntity: null,
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (initialization) {
      if (entityState.lookupId !== initialId) {
        updateEntity(entityName, {
          lookupValue: initialValue ?? null,
          lookupId: initialId ?? null,
          lookupEntity: entity ?? null,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialId]);

  const setSearchTerm = (searchTerm: string) =>
    updateEntity(entityName, { searchTerm });
  const setLookupValue = (lookupValue: string | null) =>
    updateEntity(entityName, { lookupValue });
  const setLookupId = (lookupId: string | null) =>
    updateEntity(entityName, { lookupId });
  const setLookupEntity = (lookupEntity: T | null) =>
    updateEntity(entityName, { lookupEntity });

  return {
    searchTerm: entityState.searchTerm,
    setSearchTerm,
    lookupValue: entityState.lookupValue,
    setLookupValue,
    lookupId: entityState.lookupId,
    setLookupId,
    lookupEntity: entityState.lookupEntity,
    setLookupEntity,
    entities,
  };
}
