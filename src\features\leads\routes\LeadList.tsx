import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import useLeadTable from "../table/useLeadTable";
import { SetDefaultViewButton } from "@/features/entity/components/Elements/ViewButtons/SetDefaultViewButton";
const PATH = "Leads";

export function LeadListInner({
  visibleColumns,
  resourcePath,
  createPath,
}: InnerListProps) {
  const { LeadColumns, views } = useLeadTable({
    visibleColumns,
    resourcePath,
  });
  const { tableRef } = useListCommands();
  const { t } = useTranslation("features");
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["LeadRetrieveDto"],
        Schemas["LeadRetrieveDtoPagedList"]
      >
        resourcePath={resourcePath as PathKeys}
        tableRef={tableRef}
        queryKey="lead"
        entityPath="leads"
        viewOptions={views}
        title={t("leads.title")}
        toolbar={
          <>
            <SetDefaultViewButton tableRef={tableRef} />
            <EntityLayout.CreateButton to={createPath} />
          </>
        }
        redirectTo={window.location.pathname}
        columns={LeadColumns}
      />
    </EntityLayout>
  );
}

export function LeadList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <LeadListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
