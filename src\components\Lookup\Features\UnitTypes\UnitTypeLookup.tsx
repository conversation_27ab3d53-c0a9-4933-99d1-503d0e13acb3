import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { t } from "i18next";

const ENTITY = "unitTypes";
type ENTITY_TYPE = Schemas["UnitTypeRetrieveDto"];

type UnitTypeLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function UnitTypeLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  ...props
}: UnitTypeLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.name,
    initialId,
    initial,
    true,
  );

  const queryParams: QueryParams = {
    pageSize: 50,
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["UnitTypeRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/UnitTypes",
      queryKey: "unitType",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.name")}</Box>
      <Box>{t("common:lookup.code")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.name, searchTerm, `name_${entity.id}`),
        OptionRender(entity.code, searchTerm, `code_${entity.id}`),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.name!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
