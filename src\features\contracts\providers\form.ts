import { z } from "zod";
import {
  CancelReason,
  ContractStatus,
  InvoicingType,
  PaymentMethod,
} from "@/types/enums";
import { createFormContext } from "@mantine/form";

export const contractFormSchema = z.object({
  contractNumber: z.string(),
  invoiceReference: z.string(),
  remarks: z.string(),
  invoicingInterval: z.number(),
  from: z.date().nullable(),
  to: z.date().nullable(),
  signedOn: z.date().nullable(),
  firstCancelDate: z.date().nullable(),
  cancelledOn: z.date().nullable(),
  moveOutDate: z.date().nullable(),
  invoicePeriodFrom: z.date().nullable(),
  invoicedUntil: z.date().nullable(),
  contactId: z.string().nullable(),
  contact: z.object({}).nullable(),
  customerId: z.string().nullable(),
  customer: z.object({}).nullable(),
  contractStatus: z.enum(ContractStatus as [string]),
  cancelReason: z.enum(CancelReason as [string]).nullable(),
  invoicingType: z.enum(InvoicingType as [string]).nullable(),
  paymentMethod: z.enum(PaymentMethod as [string]).nullable(),
});

export type ContractFormSchema = z.infer<typeof contractFormSchema>;

export const [ContractFormProvider, useContractFormContext, useContractForm] =
  createFormContext<ContractFormSchema>();
