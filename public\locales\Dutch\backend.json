{"ActiveReservationExists": {"message": "<PERSON><PERSON> best<PERSON><PERSON> al een actieve reservering voor deze lead."}, "ActivityStateNull": {"message": "Activiteit moet een activiteitsstatus bevatten"}, "AfoServerError": {"message": "Afo-serverfout"}, "AttachmentFileMissing": {"message": "<PERSON>r is geen bestand bijgevoegd."}, "Auth0ServerError": {"message": "Auth0-serverfout"}, "Auth0TokenError": {"message": "Auth0-tokenfout"}, "BlobDoesNotExists": {"message": "<PERSON>r <PERSON><PERSON><PERSON> geen blob met de naam {0} ({1})."}, "BlobExists": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> al een blob met de naam {0}."}, "BusinessMustContainCompanyName": {"message": "<PERSON><PERSON><PERSON><PERSON> moet een bedri<PERSON><PERSON><PERSON><PERSON> hebben"}, "BusinessUnitManagerEmpty": {"message": "Vestigingsmanager is leeg."}, "BusinessUnitRequired": {"message": "Vestiging is vereist."}, "CalendarBlockingError": {"message": "LeadId en ContactId kunnen niet worden ingesteld voor afspraaktype 'BlockingCalendar'."}, "CallbackDateEarlierThanStart": {"message": "<PERSON><PERSON><PERSON> datum kan niet eerder zijn dan de startdatum."}, "CancelAndReserveAgain": {"message": "Er is een actieve reservering. Annuleer de reservering en reserveer opnieuw."}, "CaseHasNoEmail": {"message": "<PERSON>n de <PERSON> niet beantwoorden: er is geen e-mail adres bekend"}, "ChildCannotBeParent": {"message": "Onderliggend record kan geen hoofdrecord zijn"}, "ClaimNull": {"message": "<PERSON>laim moet een waarde bevatten"}, "CompanyNameNotProvided": {"message": "Bedrijfsnaam is niet opgegeven. Kan geen nieuwe klant aanmaken."}, "ContactDoesNotHaveEmail": {"message": "Contact heeft geen e-mail."}, "ContactDuplicate": {"message": "Contact met opgegeven e-mail of telefoon bestaat al."}, "ContactNotSpecifiedOnAppointment": {"message": "Er moet een Contact opgegeven worden bij de afspraak"}, "ContactNotSpecifiedOnLead": {"message": "Bestaand contact is niet ges<PERSON>eerd bij de lead."}, "CustomerContactDuplicate": {"message": "Klantcontact met deze rol bestaat al."}, "EmailAddressNotFound": {"message": "E-mailadres niet gevonden. Zorg ervoor dat het contact of de lead een e-mailadres heeft."}, "EmailMustContainEta": {"message": "E-mail moet '@' bevatten"}, "EmailSendingError": {"message": "Fout bij het verzenden van e-mail: {0}"}, "EmptyId": {"message": "Id mag niet leeg zijn."}, "HtmlRenderingFailed": {"message": "Rendering van sja<PERSON> is mislukt: {0}"}, "HtmlTemplateNotFound": {"message": "Geselecteerde HTML-sjabloon is niet gevonden."}, "HtmlTemplateNotValid": {"message": "Geselecteerde HTML-sjabloon bevat validatiefouten: {0}"}, "IncorrectDates": {"message": "<PERSON><PERSON><PERSON>ak kan niet eindigen voordat deze is begonnen."}, "IncorrectMailbox": {"message": "Kan geen e-mail verzenden vanaf een andere mailbox dan de oorspronkelijke ontvanger van de case."}, "IncorrectPatchDoc": {"message": "Het doorgegeven patch-documentobject is onjuist."}, "InvalidArgument": {"message": "Ongeldige argumentwaarde voor {0}"}, "InvalidEnumValueInt": {"message": "Ongeldige int-waarde {0} voor enum-eigenschap {1}"}, "InvalidEnumValueString": {"message": "Ongeldige tekenreekswaarde {0} voor enum-eigenschap {1}"}, "InvalidParams": {"message": "Ongeldige parameters opgegeven."}, "InvalidRef": {"message": "Ref Id bevat geen geldige GUID."}, "ItemNotFound": {"message": "Item niet gevonden: {0}, {1}"}, "LeadIsInactive": {"message": "Lead is inactief."}, "LeadLostNull": {"message": "Bij het afleggen van een lead moet een aflegreden opgegeven worden."}, "LeadLostOpenAppointmentsError": {"message": "<PERSON><PERSON> lead niet sluiten terwijl er nog open afspraken zijn. Sluit deze en probeer het opnieuw."}, "MainContactAlreadyExists": {"message": "<PERSON><PERSON> kan slechts <PERSON><PERSON> klant<PERSON>ct he<PERSON><PERSON> met de hoofdrol."}, "MatchingLeadFoundInOmnichannel": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> al een lead met de opgegeven details, maar deze wordt momenteel verwerkt in Omnichannel."}, "MessageBirdError": {"message": "Fout bij het verz<PERSON><PERSON> van SMS: {0}"}, "NoMailbox": {"message": "Vestigingsmailbox is leeg"}, "ProcessStageNull": {"message": "Lead moet een proces<PERSON><PERSON> bevatten"}, "QuoteItemsCannotBeEdited": {"message": "Offerte-items kunnen niet worden bewerkt."}, "QuoteNotFound": {"message": "Offerte niet gevonden."}, "QuoteOrLeadMustBeSelected": {"message": "Offerte of lead moet worden geselecteerd."}, "SameUnitAlreadySelected": {"message": "Kan niet dezelfde eenheid twee keer toevoegen."}, "SelfReferenceNotAllowed": {"message": "Zelfverwijzing is niet toeges<PERSON>an."}, "UnexpectedReservationStatusValue": {"message": "Onverwachte reserveringsstatus ontvangen van AFO: {0}"}, "UnitUnavailable": {"message": "<PERSON><PERSON><PERSON><PERSON> met Id = {0} (AfoMigrationId = {1}) is niet <PERSON><PERSON>."}, "UserCreated": {"message": "Gebruiker succesvol aangemaakt"}, "UserExists": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> met Id = {0} best<PERSON><PERSON> al"}, "WalkInLeadError": {"message": "<PERSON><PERSON> is niet mogelijk om een Walk-In Lead te maken. <PERSON><PERSON> zijn {0} bestaan<PERSON> leads voor deze klant. Gebruik een bestaande lead."}}