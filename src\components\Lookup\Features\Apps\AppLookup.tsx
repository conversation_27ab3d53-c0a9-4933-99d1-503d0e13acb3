import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionGroupRender,
  OptionRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { useLookup } from "../../Context/useLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { SingleLookup } from "../../SingleLookup/SingleLookup";
import { t } from "i18next";

const ENTITY = "apps";
type ENTITY_TYPE = Schemas["AppRetrieveDto"];

type AppLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    disabled?: boolean;
    required?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function AppLookup({
  required = false,
  initial,
  initialId,
  identifier,
  disabled,
  ...props
}: AppLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.name,
    initialId,
    initial,
    true,
  );
  const queryParams: QueryParams = {
    pageSize: 50,
  };
  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["AppRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/Apps",
      queryKey: "apps",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.name")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.name, searchTerm, `name_${entity.id}`),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.name!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
