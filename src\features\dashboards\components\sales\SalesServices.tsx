import type React from "react";
import { Grid } from "@mantine/core";
import CardChart from "../common/CardChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import CustomBarChartVertical from "../common/CustomBarChartVertical";

const metrics: MetricProps[] = [
  { metric: "OperationsSns", sales: true },
  { metric: "EditedLeadsSnS", sales: true },
  { metric: "ReachedLeadsSnS", sales: true },
  { metric: "BookedAppointmentsSnS", sales: true },
  { metric: "SiteVisitsTotalSnS", sales: true },
  { metric: "NoShowsSnS", sales: true },
  { metric: "NoShowsPercentageSnS", sales: true },
  { metric: "FutureAppointmentsSnS", sales: true },
  { metric: "TotalReservationsSns", sales: true },
];
const ChartGrid = ({
  data,
  cardData,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical data={data[0]} title={"Operations Count"} />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical data={data[1]} title={"Edited Leads"} />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical data={data[2]} title={"Reached Leads"} />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CustomBarChartVertical data={data[3]} title={"Booked Appointments"} />
      </Grid.Col>
    </Grid>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[0]![0]?.total.toString() ?? "0"}
          title={"Operations Count"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[1]![0]?.total.toString() ?? "0"}
          title={"Edited Leads"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[2]![0]?.total.toString() ?? "0"}
          title={"Reached Leads"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
        <CardChart
          value={data[3]![0]?.total.toString() ?? "0"}
          title={"Booked Appointments"}
        />
      </Grid.Col>
    </Grid>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CustomBarChartVertical
          marginTop={0}
          data={data[4]}
          title={"Total Site Visits"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CustomBarChartVertical
          marginTop={0}
          data={data[5]}
          title={"No Shows"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CustomBarChartVertical
          percentage
          marginTop={0}
          data={data[6]}
          title={"Percentage No Show"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CustomBarChartVertical
          marginTop={0}
          data={data[7]}
          title={"Future Appointments"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CustomBarChartVertical
          marginTop={0}
          data={data[8]}
          title={"Total Reservations"}
        />
      </Grid.Col>
    </Grid>
    <Grid gutter={"xs"}>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CardChart
          value={data[4]![0]?.total.toString() ?? "0"}
          title={"Total Site Visits"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CardChart
          value={data[5]![0]?.total.toString() ?? "0"}
          title={"No Shows"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CardChart
          value={
            cardData[6]![
              (cardData[6]?.length ?? 0) - 1
            ]?.percentage.toString() ?? "0"
          }
          percentage
          title={"No Shows Percentage"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CardChart
          value={data[7]![0]?.total.toString() ?? "0"}
          title={"Future Appointments"}
        />
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6, lg: 2.4 }}>
        <CardChart
          value={data[8]![0]?.total.toString() ?? "0"}
          title={"Total Reservations"}
        />
      </Grid.Col>
    </Grid>
  </>
);

const SalesServices: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default SalesServices;
