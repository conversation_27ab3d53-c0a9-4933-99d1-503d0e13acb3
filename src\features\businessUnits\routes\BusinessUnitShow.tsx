import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import {
  type FormSchema as BusinessUnitFormSchema,
  BusinessUnitForm,
} from "../components/BusinessUnitForm";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function BusinessUnitShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["BusinessUnit"],
    Schemas["BusinessUnitCreateDto"]
  >({
    resourcePath: "/api/BusinessUnits/{id}",
    resourceId: id!,
    queryKey: "businessUnit",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["BusinessUnit"]>({
    resourcePath: "/api/BusinessUnits/{id}",
    resourceId: id!,
    queryKey: "businessUnit",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("businessUnit_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <BusinessUnitForm
      isCreate={false}
      title={t("businessUnits.showTitle", { id })}
      initialValues={
        filterFalsyValues({
          ...data,
          dateOfOpening: data.dateOfOpening
            ? new Date(data.dateOfOpening)
            : null,
        }) as BusinessUnitFormSchema
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/businessUnits");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/businessUnits");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
