import { EntityLayout } from "@/features/entity";
import { DownloadRenderer } from "@/components/Table/CellRenderers";
import { type PathKeys, type Schemas } from "@/types";
import { type PropsWithChildren } from "react";
import { useTranslation } from "react-i18next";

interface EmailAttachmentsProps {
  emailId: string;
}

export function EmailAttachments({
  emailId,
}: PropsWithChildren<EmailAttachmentsProps>) {
  const { t } = useTranslation("features");

  return (
    <>
      <EntityLayout.TableMantine<
        Schemas["AttachmentRetrieveDto"],
        Schemas["AttachmentRetrieveDtoPagedList"]
      >
        resourcePath={`/api/Emails/${emailId}/attachments` as PathKeys}
        queryKey="attachment"
        entityPath="attachmens"
        title={t("emails.attachments")}
        toolbarEnabled={false}
        disableNavigation={true}
        columns={[
          {
            accessorKey: "id",
            header: "",
            maxSize: 6,
            enableColumnFilter: false,
            enableSorting: false,
            size: 6,
            Cell: DownloadRenderer,
          },
          {
            accessorKey: "name",
            header: t("emails.name"),
            filterVariant: "text",
          },
        ]}
      />
    </>
  );
}
