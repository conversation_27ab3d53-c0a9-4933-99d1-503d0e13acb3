import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { config } from "@/config";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
import { type ComboboxData } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { lowerCaseNthLetter } from "@/utils/filters";
import { UnitStatus } from "@/types/enums";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { UnitTypeLookup } from "@/components/Lookup/Features/UnitTypes/UnitTypeLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

const PATH = "Units";

export function UnitListInner({ resourcePath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["UnitRetrieveDto"],
        Schemas["UnitRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="unit"
        entityPath="units"
        title={t("units.title")}
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "unitId",
            header: t("units.unitId"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "units"),
          },
          {
            accessorKey: "unitCode",
            header: t("units.unitCode"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "units"),
          },
          {
            accessorKey: "businessUnit",
            header: t("units.businessUnit"),
            ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
          },
          {
            accessorKey: "unitType",
            header: t("units.unitType"),
            ...TableRenderer(UnitTypeLookup, "unitTypes", ["name"]),
          },
          {
            accessorKey: "pricePerMonth",
            header: t("prices.pricePerMonth"),
            filterVariant: "range",
            Cell: ({ cell }) => {
              const value = cell.getValue<number>();
              return value ? `${value} ${config.CURRENCY.symbol}` : "";
            },
          },
          {
            accessorKey: "minPrice",
            header: t("prices.minPrice"),
            filterVariant: "range",
            Cell: ({ cell }) => {
              const value = cell.getValue<number>();
              return value ? `${value} ${config.CURRENCY.symbol}` : "";
            },
          },
          {
            accessorKey: "maxPrice",
            header: t("prices.maxPrice"),
            filterVariant: "range",
            Cell: ({ cell }) => {
              const value = cell.getValue<number>();
              return value ? `${value} ${config.CURRENCY.symbol}` : "";
            },
          },
          {
            accessorKey: "length",
            header: t("units.length"),
            filterVariant: "range",
            Cell: ({ cell }) => {
              const value = cell.getValue<number>();
              return value !== null ? `${value} ${config.METERS.symbol}` : "";
            },
          },
          {
            accessorKey: "width",
            header: t("units.width"),
            filterVariant: "range",
            Cell: ({ cell }) => {
              const value = cell.getValue<number>();
              return value !== null ? `${value} ${config.METERS.symbol}` : "";
            },
          },
          {
            accessorKey: "volume",
            header: t("units.volume"),
            filterVariant: "range",
            Cell: ({ cell }) => {
              const value = cell.getValue<number>();
              return value !== null
                ? `${value} ${config.CUBICMETERS.symbol}`
                : "";
            },
          },
          {
            accessorKey: "status",
            header: t("units.status"),
            filterVariant: "multi-select",
            Cell: ({ cell }) =>
              t("units." + lowerCaseNthLetter(cell.getValue<string>())),
            mantineFilterSelectProps: {
              data: UnitStatus as ComboboxData | undefined,
            },
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "number",
            desc: false,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function UnitList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <UnitListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
