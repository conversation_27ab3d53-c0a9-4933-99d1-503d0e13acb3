import { useEffect } from "react";
import * as signalR from "@microsoft/signalr";
import { type QueryClient } from "react-query";
import { useAuth0 } from "@auth0/auth0-react";
import receiveMessageListener from "./listeners/receiveMessageListener";
import queryInvalidateListener from "./listeners/queryInvalidateListener";

const useWebSockets = (queryClient: QueryClient) => {
  const { getAccessTokenSilently } = useAuth0();
  useEffect(() => {
    // Create SignalR connection
    const connection = new signalR.HubConnectionBuilder()
      .withUrl(`${import.meta.env.VITE_PUBLIC_API_URL}/api/mainHub`, {
        withCredentials: true,
        transport: signalR.HttpTransportType.LongPolling,
        accessTokenFactory: async () => {
          const token = await getAccessTokenSilently();
          return token;
        },
      })
      .withAutomaticReconnect()
      .build();

    connection
      .start()
      .then(() => console.log("WS Connection Established."))
      .catch((err) => console.error("SignalR Connection Error:", err));

    // Listen for invalidation messages
    connection.on("InvalidateQuery", (resourceName: string) => {
      queryInvalidateListener(resourceName);
    });

    connection.on("ReceiveMessage", (message: string) => {
      receiveMessageListener(message);
    });

    return () => {
      void connection.stop();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryClient]);
};

export default useWebSockets;
