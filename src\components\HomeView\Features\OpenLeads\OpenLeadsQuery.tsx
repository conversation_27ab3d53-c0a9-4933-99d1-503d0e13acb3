import { useEntityListQuery } from "@/features/entity/queries";
import UseUserBusinessUnit from "@/hooks/businessUnit/useUserBusinessUnit";
import { type Schemas } from "@/types";
import { Priority5LeadSources } from "@/types/enums";
import React from "react";

export default function OpenLeadsQuery() {
  const { userBusinessUnitId } = UseUserBusinessUnit();

  const callbackStart = new Date(new Date("2024-01-01").setHours(0, 0, 0, 0));
  const callbackEnd = new Date(new Date().setHours(24, 0, 0, 0));

  const TOP_WEBFORM_TITLES_ORDER = [
    "Aanhanger huren",
    "Transport regelen",
    "Verhuishulp aanvragen",
    "Heb je een vraag?",
  ];

  const filter = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "RecordState == Active",
    "(ProcessStage ^^ [New, Appointment, SpaceTour, FollowUp, NotReached, CallbackAppointment, NoShow])",
    `(NextCallback >= ${callbackStart.toISOString().replace("Z", "")} && NextCallback <= ${callbackEnd.toISOString().replace("Z", "")} || NextCallback == null)`,
    "Priority >= 5",
    `(LeadSource ^^ [${Priority5LeadSources.map((source) => `${source}`).join(", ")}])`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data } = useEntityListQuery<Schemas["LeadRetrieveDtoPagedList"]>({
    resourcePath: "/api/Leads",
    params: {
      filter: filter,
      orderBy: "Priority",
      desc: false,
    },
    queryKey: `leadListHomeStoreManagerOpenLeads`,
  });
  const rawLeads = data?.data ?? [];
  const totalCount = data?.totalCount;

  // Use useMemo to sort the leads only when rawLeads changes
  const sortedLeads = React.useMemo(() => {
    if (!rawLeads.length) {
      return [];
    }

    return [...rawLeads].sort((a, b) => {
      const aTitle = a.webformTitle as string | undefined;
      const bTitle = b.webformTitle as string | undefined;
      const aPriority = a.priority;
      const bPriority = b.priority;

      const aIsTop = aTitle ? TOP_WEBFORM_TITLES_ORDER.includes(aTitle) : false;
      const bIsTop = bTitle ? TOP_WEBFORM_TITLES_ORDER.includes(bTitle) : false;

      if (aIsTop && !bIsTop) {
        return -1;
      }
      if (!aIsTop && bIsTop) {
        return 1;
      }

      if (aIsTop && bIsTop && aTitle && bTitle) {
        const aIndex = TOP_WEBFORM_TITLES_ORDER.indexOf(aTitle);
        const bIndex = TOP_WEBFORM_TITLES_ORDER.indexOf(bTitle);
        return aIndex - bIndex;
      }

      if (aPriority !== undefined && bPriority !== undefined) {
        if (aPriority < bPriority) return -1;
        if (aPriority > bPriority) return 1;
      } else if (aPriority !== undefined) {
        return -1;
      } else if (bPriority !== undefined) {
        return 1;
      }
      return 0;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rawLeads]);

  return { totalCount: totalCount, leads: sortedLeads };
}
