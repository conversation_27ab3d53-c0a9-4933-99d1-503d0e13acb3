import { ActionIcon, Badge, Box, Group } from "@mantine/core";
import { IconArrowLeft, IconX } from "@tabler/icons-react";
import WizardNavigationProgress from "./WizardNavigationProgress";
import { type Dispatch, type SetStateAction } from "react";

export interface PageProps<T> {
  setPages: Dispatch<SetStateAction<T[]>>;
  setTotalPages?: Dispatch<SetStateAction<number>>;
  pages: T[];
  closeModal?: () => void;
}

interface WizardHeaderProps<T> extends PageProps<T> {
  totalPages: number;
  defaultTotalPages: number;
  currentWizardStatus: string;
  verificationStatus: boolean;
}

export default function WizardHeader<T>({
  setPages,
  pages,
  totalPages,
  closeModal,
  setTotalPages,
  defaultTotalPages,
  currentWizardStatus,
  verificationStatus = false,
}: WizardHeaderProps<T>) {
  return (
    <Box p={32}>
      <Group
        justify={
          pages[pages.length - 1] !== "COMPLETED" ? "space-between" : "right"
        }
      >
        {pages[pages.length - 1] !== "COMPLETED" && (
          <>
            <ActionIcon
              disabled={pages.length === 1}
              variant="transparent"
              color="gray"
              onClick={() => {
                setPages(pages.slice(0, pages.length - 1));
                setTotalPages!(defaultTotalPages);
              }}
            >
              <IconArrowLeft width={24} height={24} />
            </ActionIcon>
            <Badge color="gray" variant="light" size="lg">
              {currentWizardStatus}
            </Badge>
            <Badge
              color={verificationStatus ? "green" : "red"}
              variant="light"
              size="lg"
              onClick={() => {
                setPages(["VERIFICATION" as T]);
              }}
              style={{ cursor: "pointer" }}
            >
              {verificationStatus ? "VERIFIED" : "UNVERIFIED"}
            </Badge>
            <WizardNavigationProgress
              currentPageIndex={pages.length}
              totalPages={totalPages}
            />
          </>
        )}

        <ActionIcon
          variant="transparent"
          color="gray"
          onClick={() => {
            if (closeModal) {
              closeModal();
            }
          }}
        >
          <IconX width={24} height={24} />
        </ActionIcon>
      </Group>
    </Box>
  );
}
