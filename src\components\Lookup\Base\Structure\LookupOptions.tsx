import { Combobox, Flex, Group, Loader, ScrollArea } from "@mantine/core";
import { type ForwardedRef } from "react";
import LookupHeader from "./LookupHeader";
import classes from "@/components/Lookup/Base/Lookup.module.css";

interface LookupOptionsProps {
  options: (JSX.Element | null)[] | undefined;
  header: JSX.Element;
  isFetching: boolean;
  viewportRef: ForwardedRef<HTMLDivElement> | undefined;
}

export function LookupOptions({
  options,
  isFetching,
  header,
  viewportRef,
}: LookupOptionsProps) {
  if (isFetching) {
    return (
      <Flex p={20} justify="center">
        <Loader></Loader>
      </Flex>
    );
  }

  return (
    <>
      <Combobox.Header className={classes.lookupHeader}>
        <Group justify="space-between" gap="sm" grow>
          <LookupHeader header={header} />
        </Group>
      </Combobox.Header>
      <ScrollArea.Autosize
        mah={"25vh"}
        type="always"
        offsetScrollbars
        scrollbars="y"
        viewportRef={viewportRef}
      >
        {options?.length === 0 ? (
          <Combobox.Empty>{"Nothing Found..."}</Combobox.Empty>
        ) : (
          <>{options}</>
        )}
      </ScrollArea.Autosize>
    </>
  );
}
