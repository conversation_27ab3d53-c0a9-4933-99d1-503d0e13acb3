import { Box, Flex, Group, Text } from "@mantine/core";
import { type PossibleTabs } from "../../Home";
import { type TabProps } from "../../Structure/HomeTabs";

import classes from "../..//Home.module.css";
import OmnichannelQuery from "./OmnichannelQuery";
import NumberLoader from "../../Components/numberLoader";
import { useTranslation } from "react-i18next";

const TAB: PossibleTabs = "Omnichannel";

export default function OmnichannelTab({ setActiveTab, isActive }: TabProps) {
  const { t } = useTranslation("features");

  const { totalCount, userStatus } = OmnichannelQuery();
  return (
    <Box
      w={{ base: "100%", xs: "100%", md: "32%", lg: "16%", xl: "16%" }}
      className={isActive ? classes.activeHomeTab : classes.homeTab}
      onClick={() => setActiveTab(TAB)}
    >
      <Flex justify="center" align="center" direction="row" wrap="wrap">
        <Text w={"100%"} className={classes.tabTitleText}>
          {t(`omnichannel.activeUsers`)}
        </Text>

        <Text className={classes.tabDisplayText}>
          <NumberLoader number={totalCount} />
        </Text>

        <Group justify="space-between" gap={4} w={"100%"}>
          <Text className={classes.tabFooterText}>
            {t(`omnichannel.personalStatus`)}
          </Text>
          <Text className={classes.tabFooterBoldText}>
            {userStatus ? "Active" : "Paused"}
          </Text>
        </Group>
      </Flex>
    </Box>
  );
}
