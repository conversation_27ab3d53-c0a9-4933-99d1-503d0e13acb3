import { Route, Routes } from "react-router-dom";
import { LossReasonList } from "./LossReasonList";
import { LossReasonShow } from "./LossReasonShow";
import { LossReasonCreate } from "./LossReasonCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<LossReasonList />} />
      <Route path=":id" element={<LossReasonShow />} />
      <Route path="create" element={<LossReasonCreate />} />
    </Routes>
  );
}
