import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Anchor, Box, Center, Flex, Loader, Stack } from "@mantine/core";
import { DateTime } from "luxon";
import { useTranslation } from "react-i18next";
interface TimelineWebFormPanelProps {
  webFormId: string;
}

export function TimelineWebFormPanel({ webFormId }: TimelineWebFormPanelProps) {
  const { t } = useTranslation("features");
  const { dateFormat } = useSettingsContext();
  const { data, isLoading } = useEntityQuery<
    Schemas["WebformEntryRetrieveDto"]
  >({
    resourcePath: `/api/WebformEntries/{id}`,
    resourceId: webFormId,
    queryKey: ["webForm", webFormId],
  });

  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <Box ml={8}>
      <Stack gap="xs">
        <Flex>
          {t("webformEntries.webformTitle")}:
          <Box ml={8}>{data?.webformTitle ?? ""}</Box>
        </Flex>
        <Flex>
          {t("common.createdOn")}:
          <Box ml={8}>
            {DateTime.fromJSDate(new Date(data?.createdOn ?? "")).toFormat(
              dateFormat + " HH:mm:ss",
            )}
          </Box>
        </Flex>
        <Flex>
          {t("common.businessUnit")}:
          <Anchor
            href={`/app/businessUnits/${data?.businessUnitId}`}
            style={{ cursor: "pointer" }}
            ml={8}
          >
            {data?.businessUnit?.name ?? data?.businessUnit?.code ?? ""}
          </Anchor>
        </Flex>
        <Flex>
          {t("webformEntries.productType")}:
          <Box ml={8}>{data?.productType ?? ""}</Box>
        </Flex>
        <Flex>
          {t("webformEntries.startWithin")}:
          <Box ml={8}>{data?.startWithin ?? ""}</Box>
        </Flex>
        <Flex>
          {t("webformEntries.webformDetails")}:
          <Box ml={8}>{data?.webformDetails ?? ""}</Box>
        </Flex>
        <Flex>
          {t("webformEntries.step")}:<Box ml={8}>{data?.step ?? ""}</Box>
        </Flex>
        <Flex>
          {t("webformEntries.unitSize")}:
          <Box ml={8}>{data?.unitSize ?? ""}</Box>
        </Flex>
        <Flex>
          {t("webformEntries.price")}:<Box ml={8}>{data?.price ?? ""}</Box>
        </Flex>
      </Stack>
    </Box>
  );
}
