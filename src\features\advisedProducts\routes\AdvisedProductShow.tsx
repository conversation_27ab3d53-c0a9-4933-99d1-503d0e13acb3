import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  AdvisedProductForm,
  type FormSchema,
} from "../components/AdvisedProductForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function AdvisedProductShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["AdvisedProduct"],
    Schemas["AdvisedProductCreateDto"]
  >({
    resourcePath: "/api/AdvisedProducts/{id}",
    resourceId: id!,
    queryKey: "advisedProduct",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/advisedProducts";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("advisedProduct_" + id);
  };
  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/AdvisedProducts/{id}",
    resourceId: id!,
    queryKey: "advisedProduct",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["AdvisedProduct"]>({
    resourcePath: "/api/AdvisedProducts/{id}",
    resourceId: id!,
    queryKey: "advisedProduct",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <AdvisedProductForm
      isCreate={false}
      title={t("advisedProducts.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["AdvisedProductCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("advisedProducts.delete", { id })}
            modalContent={t("advisedProducts.deleteConfirmation", { id })}
            confirmLabel={t("advisedProducts.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/advisedProducts");
              }
            }}
          />
        </Group>
      }
    />
  );
}
