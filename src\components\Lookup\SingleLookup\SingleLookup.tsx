import { type ComboboxOptionProps, type ComboboxStore } from "@mantine/core";
import { type UseFormReturnType } from "node_modules/@mantine/form/lib/types";
import { BaseLookup } from "../Base/BaseLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";

interface BasicLookupProps {
  id?: string;
}

type SingleLookupProps<T extends BasicLookupProps> = ReturnType<
  UseFormReturnType<T>["getInputProps"]
> & {
  combobox: ComboboxStore;
  required?: boolean;
  disabled?: boolean;
  options: (JSX.Element | null)[] | undefined;
  header: JSX.Element;
  isFetching: boolean;
  entity: string;
  entities: T[];
  identifier: string;
  currentFocus: number;
  width?: string | number;
};

export function SingleLookup<T extends BasicLookupProps>({
  combobox,
  required = false,
  disabled = false,
  options,
  header,
  isFetching,
  entity,
  entities,
  identifier,
  currentFocus,
  width,
  ...props
}: SingleLookupProps<T>) {
  const { setLookupId, setLookupValue, setLookupEntity, setSearchTerm } =
    useLookup(identifier);
  return (
    <BaseLookup
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={entity}
      onOptionSubmit={(value: string, optionProps: ComboboxOptionProps) => {
        const selectedEntity = entities.find((x) => x.id == value);
        combobox.closeDropdown();
        // disable eslint here because mantine form does not provide a specific type for form onchange
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        props.onChange(value);
        setLookupId(value);
        setLookupEntity(selectedEntity);
        setLookupValue(optionProps.display as string);
        setSearchTerm("");
      }}
      onRightSectionClick={() => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        props.onChange(null);
        setLookupId(null);
        setLookupValue(null);
        setLookupEntity(null);
        setSearchTerm("");
      }}
      width={width}
      {...props}
    />
  );
}
