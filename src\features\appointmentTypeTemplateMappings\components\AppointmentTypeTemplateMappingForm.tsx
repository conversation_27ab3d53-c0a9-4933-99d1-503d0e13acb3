import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Select } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { AppointmentType } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { HtmlTemplateLookup } from "@/components/Lookup/Features/HtmlTemplates/HtmlTemplateLookup";

const formSchema = z.object({
  appointmentType: z.enum(AppointmentType as [string]).nullable(),
  htmlTemplate: z.object({}).nullable(),
  htmlTemplateId: z.string().nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface AppointmentTypeTemplateMappingFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function AppointmentTypeTemplateMappingForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: AppointmentTypeTemplateMappingFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      appointmentType: initialValues?.appointmentType ?? null,
      htmlTemplateId: initialValues?.htmlTemplateId ?? "",
      htmlTemplate: initialValues?.htmlTemplate ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="lg">
              <FieldValidation isDirty={form.isDirty("appointmentType")}>
                <Select
                  searchable
                  disabled
                  label={t("appointmentTypeTemplateMappings.appointmentType")}
                  data={AppointmentType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("appointments", value)),
                  }))}
                  clearable
                  {...form.getInputProps("appointmentType")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("htmlTemplateId")}>
                <HtmlTemplateLookup
                  label={t("appointmentTypeTemplateMappings.htmlTemplate")}
                  initial={form.getValues().htmlTemplate}
                  initialId={form.getValues().htmlTemplateId}
                  identifier="htmlTemplateIdAppointmentTypeTemplate"
                  {...form.getInputProps("htmlTemplateId")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
