import { useMemo, useRef } from "react";
import {
  type <PERSON>ut<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON> as Mantine<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@mantine/charts";
import { type Statistic } from "../../utils/types";
import { Box, Center, Flex, Text, useMantineTheme } from "@mantine/core";
import LegendTable from "./DonutChartLegend";

interface DonutChartProps {
  data: Statistic[] | undefined;
  title: string;
  marginTop?: number;
  height: number;
  width?: string;
  percentage?: boolean;
}

export default function DonutChart({
  marginTop,
  height = 200,
  width,
  data,
  title,
}: DonutChartProps) {
  const theme = useMantineTheme();
  const colorSet = theme.colors.chartColors ?? [];
  let colorIndex = 0; // Index to track which color should be assigned next

  function getNextColorFromSet() {
    const color = colorSet[colorIndex] ?? "#6495ED";
    colorIndex++;
    return color;
  }

  const getRandomColor = () => {
    return `#${Math.floor(Math.random() * 16777215).toString(16)}`;
  };

  const generateUniqueColors = (count: number): string[] => {
    const colors: string[] = [];

    for (let i = 0; i < count; i++) {
      if (colorSet.length === 0 || colorIndex >= colorSet.length) {
        colors.push(getRandomColor());
      } else {
        colors.push(getNextColorFromSet());
      }
    }
    return colors;
  };

  const colorsRef = useRef<string[]>([]);
  if (colorsRef.current.length === 0 && data) {
    // Initialize colors only once
    colorsRef.current = generateUniqueColors(data.length);
  }

  const formattedData: DonutChartCell[] = useMemo(() => {
    if (!data) return [];

    return data
      .map((item, index) => ({
        name: item.display,
        value: item.value,
        color: colorsRef.current[index],
      }))
      .sort() as DonutChartCell[];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.length]);

  return (
    <Box
      mt={marginTop}
      style={{ border: "2px solid #e0e0e0", borderRadius: 10 }}
      h={height}
      w={width}
    >
      <Flex m={16}>
        <Text fw={600} ml={4} flex={3} ta={"left"} size="sm">
          {title}
        </Text>
      </Flex>
      <Flex m={16} h={"80%"}>
        <Box flex={2} h="100%">
          <Center h="100%">
            <MantineDonutChart
              thickness={30}
              tooltipDataSource="segment"
              style={{ zIndex: 1 }}
              strokeWidth={0}
              chartLabel={data![0]?.total.toString() ?? "0"}
              data={formattedData}
            />
          </Center>
        </Box>
        <Box flex={2}>
          <LegendTable data={formattedData} height={height} />
        </Box>
      </Flex>
    </Box>
  );
}
