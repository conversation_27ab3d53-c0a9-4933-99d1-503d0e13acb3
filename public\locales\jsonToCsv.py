import json
import os
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill
from openpyxl.utils import get_column_letter

def flatten_dict(d, parent_key='', sep='.'):
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

def process_json_files(base_path, languages):
    all_data = {}
    
    for root, dirs, files in os.walk(base_path):
        for file in files:
            if file.endswith('.json'):
                file_name = os.path.splitext(file)[0]
                if file_name not in all_data:
                    all_data[file_name] = {lang: {} for lang in languages}
                
                lang = os.path.basename(root)
                if lang in languages:
                    with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    all_data[file_name][lang] = flatten_dict(data)
    
    return all_data

def create_excel(data, output_file, languages):
    wb = Workbook()
    wb.remove(wb.active)  # Remove the default sheet
    
    header_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    header_font = Font(bold=True)
    wrap_alignment = Alignment(wrap_text=True, vertical="top")
    
    for file_name, file_data in data.items():
        ws = wb.create_sheet(file_name)
        
        # Write headers
        headers = ["Key"] + languages
        for col, header in enumerate(headers, start=1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        # Write data
        all_keys = set()
        for lang_data in file_data.values():
            all_keys.update(lang_data.keys())
        
        for row, key in enumerate(sorted(all_keys), start=2):
            ws.cell(row=row, column=1, value=key)
            for col, lang in enumerate(languages, start=2):
                value = file_data[lang].get(key, "")
                ws.cell(row=row, column=col, value=value)
        
        # Apply styles and adjust column widths
        for col in range(1, len(headers) + 1):
            max_length = 0
            column = get_column_letter(col)
            for cell in ws[column]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(cell.value)
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = min(adjusted_width, 50)
        
        for row in ws.iter_rows():
            for cell in row:
                cell.alignment = wrap_alignment
    
    wb.save(output_file)

def main():
    base_path = '.'  # Current directory, adjust if needed
    languages = ['en', 'nl']
    output_file = 'translations_comparison.xlsx'
    
    all_data = process_json_files(base_path, languages)
    create_excel(all_data, output_file, languages)
    print(f"Excel file '{output_file}' has been created successfully.")

if __name__ == "__main__":
    main()