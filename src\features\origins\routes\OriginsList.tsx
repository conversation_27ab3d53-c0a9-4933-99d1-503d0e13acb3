import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import TableRenderer from "@/components/Table/renderers/TableRenderer";
import { OriginCategoryLookup } from "@/components/Lookup/Features/OriginCategories/OriginCategoryLookup";

const PATH = "Origins";
export function OriginsListInner({ resourcePath, createPath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["OriginRetrieveDto"],
        Schemas["OriginRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="origin"
        entityPath="origins"
        title={t("origins.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "name",
            header: t("origins.name"),
            filterVariant: "text",
          },
          {
            accessorKey: "category",
            header: t("origins.category"),
            ...TableRenderer(OriginCategoryLookup, "category", ["name"]),
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function OriginsList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <OriginsListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
