import { queryClient } from "@/lib/query";

export default function queryInvalidateListener(resourceName: string) {
  console.log(`Invalidating query: ${resourceName}`);
  const formattedResourceName =
    resourceName.charAt(0).toLowerCase() + resourceName.slice(1);

  const keysToInvalidate = new Set<string>();
  keysToInvalidate.add(formattedResourceName + "_list");

  if (resourceName === "Lead") {
    keysToInvalidate.add("leadActivity_list");
    keysToInvalidate.add("lead_omnichannel_list");
  }

  if (resourceName === "Email" || resourceName === "LeadEventEntry") {
    keysToInvalidate.add("leadActivity_list");
  }

  if (resourceName === "Appointment") {
    keysToInvalidate.add("leadActivity_list");
    keysToInvalidate.add("appointmentListHome_list");
    keysToInvalidate.add("appointmentCompletedMetricHome_list");
  }

  void queryClient.invalidateQueries({
    predicate: (query) => {
      const key = query.queryKey[0];
      return typeof key === "string" && keysToInvalidate.has(key);
    },
  });
}
