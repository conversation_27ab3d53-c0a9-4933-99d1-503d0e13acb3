import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Phone<PERSON><PERSON><PERSON>,
} from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";

export function CustomerColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "name",
      header: t("customers.name"),
      filterVariant: "text",
      Cell: (props) => EntityLinkRenderer(props, "customers"),
    },
    {
      accessorKey: "email",
      header: t("customers.email"),
      filterVariant: "text",
      Cell: EmailRenderer,
    },
    {
      accessorKey: "mobile",
      header: t("customers.mobile"),
      filterVariant: "text",
      Cell: PhoneRenderer,
    },
    {
      accessorKey: "phone",
      header: t("customers.phone"),
      filterVariant: "text",
      Cell: PhoneRenderer,
    },
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "modifiedOn",
      header: t("entity.modifiedOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
  ];
  return columns;
}
