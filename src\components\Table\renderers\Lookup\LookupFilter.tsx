import { useEntityQuery } from "@/features/entity/queries";
import { type Paths } from "@/types";
import { Box, Center, Loader } from "@mantine/core";
import {
  type MRT_Column,
  type MRT_RowData,
  type MRT_TableInstance,
  type MRT_Header,
} from "mantine-react-table";
import { type LookupComponentProps } from "../TableRenderer";

interface LookupFilterProps {
  column: MRT_Column<MRT_RowData, unknown>;
  table: MRT_TableInstance<MRT_RowData>;
  header: MRT_Header<MRT_RowData>; // Often needed for context/accessibility
  // The actual Lookup component provided by the user
  LookupComponent: React.ComponentType<LookupComponentProps>;
  nameFields: string[];
  entityPath: string;
}

export default function LookupFilter({
  column,
  LookupComponent,
  entityPath,
}: LookupFilterProps): JSX.Element {
  const currentFilterValue = column.getFilterValue();

  const initialId =
    typeof currentFilterValue === "string" ? currentFilterValue : null;

  const { data, isLoading } = useEntityQuery<MRT_RowData>({
    resourcePath: `/api/${entityPath}/{id}` as keyof Paths,
    resourceId: initialId!,
    queryKey: [entityPath, initialId],
  });

  if (isLoading) {
    return (
      <Box w={"100%"}>
        <Center>
          <Loader size={"sm"} />
        </Center>
      </Box>
    );
  }

  const handleChange = (value: string | null) => {
    // Setting to undefined or null usually clears the filter in MRT
    column.setFilterValue(value ?? undefined);
  };

  const propsForLookup: LookupComponentProps = {
    w: "100%",
    initial: data!,
    initialId: initialId,
    identifier: `${column.id}LookupFilter`, // Unique identifier for the filter instance
    onChange: handleChange,
  };
  return <LookupComponent {...propsForLookup} />;
}
