import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useBlocker } from "react-router-dom";

export function useNavigationBlocker(
  shouldBlock: boolean,
  message = "common.confirmNavigation",
) {
  const blocker = useBlocker(shouldBlock);
  const { t } = useTranslation("features");

  useEffect(() => {
    if (blocker.state === "blocked") {
      const confirm = window.confirm(t(message));
      if (confirm) {
        blocker.proceed();
      } else {
        blocker.reset();
      }
    }
  }, [blocker, message, t, shouldBlock]);
}
