import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  ResponsiveContainer,
  type TooltipProps,
  Legend,
} from "recharts";
import { type Statistic } from "../../utils/types";
import {
  type NameType,
  type ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { Box, Flex, Paper, ScrollArea, Text } from "@mantine/core";
import { IconArrowsMaximize, IconArrowsMinimize } from "@tabler/icons-react";
import classes from "./report.module.css";
import { useCallback, useState } from "react";

interface CustomBarChartStackedHorizontalProps {
  data: Statistic[];
  title: string;
  marginTop?: number;
  height?: number;
  width?: string;
  percentage?: boolean;
}

interface CustomChartTooltipProps extends TooltipProps<ValueType, NameType> {
  percentage?: boolean;
}

export default function CustomBarChartStackedHorizontal({
  title,
  marginTop = 0,
  height = 120,
  width = "100%",
  data,
  percentage = false,
}: CustomBarChartStackedHorizontalProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const transformedData =
    data?.map((item) => {
      const statusCounts = item.stackedTotal?.reduce(
        (acc, { status, count }) => ({ ...acc, [status]: count }),
        {},
      );
      return {
        ...item,
        ...statusCounts,
      };
    }) || [];

  const statuses = Array.from(
    new Set(
      data?.flatMap((item) => item.stackedTotal?.map((st) => st.status)) || [],
    ),
  );

  const getColorForStatus = (status: string) => {
    switch (status) {
      case "NotReached":
        return "#8b5cf6"; // violet
      case "Reached":
        return "#3b82f6"; // blue
      case "Open":
        return "#14b8a6"; // teal
      default:
        return "#6b7280"; // gray
    }
  };

  const CustomTooltip = ({
    active,
    payload,
    label,
    percentage,
  }: CustomChartTooltipProps) => {
    if (active && payload?.length) {
      return (
        <Paper px="md" py="sm" withBorder shadow="md" radius="md">
          <Text fz="h6" fw={700}>
            {label}
          </Text>
          {payload.map((item) => (
            <Text key={item.dataKey as string} fz="sm">
              {item.name}: {item.value} {percentage && "%"}
            </Text>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const xKey = "display";
  const dataHeight = (transformedData.length || 1) * 40;
  const expandedStyle = {
    position: "fixed" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: "white",
    padding: "20px",
    overflow: "auto" as const,
  };

  const normalStyle = {
    border: "2px solid #e0e0e0",
    borderRadius: 4,
    height,
    width,
  };

  return (
    <Box
      style={isExpanded ? expandedStyle : normalStyle}
      mt={isExpanded ? 0 : marginTop}
    >
      <Flex dir="row">
        <Text fw={600} ml={4} flex={3} ta={"left"} size="sm">
          {title}
        </Text>
        <Box
          ta="right"
          mr={4}
          style={{ cursor: "pointer" }}
          onClick={toggleExpand}
        >
          {isExpanded ? (
            <IconArrowsMinimize size={14} />
          ) : (
            <IconArrowsMaximize size={14} />
          )}
        </Box>
      </Flex>

      <ScrollArea
        classNames={classes}
        scrollbarSize={6}
        type="always"
        scrollbars={"y"}
        h={isExpanded ? "calc(100% - 40px)" : "90%"}
      >
        <ResponsiveContainer
          width="98%"
          height={
            isExpanded
              ? "100%"
              : dataHeight < height
                ? height * 0.85
                : dataHeight
          }
        >
          <BarChart
            margin={{ left: -5, right: 10, bottom: -5 }}
            layout="horizontal"
            data={transformedData}
            barGap={"1%"}
            barSize={10}
          >
            <CartesianGrid horizontal={false} strokeDasharray="1 1" />
            <YAxis
              minTickGap={1}
              type="number"
              axisLine={false}
              tickLine={false}
              fontSize={12}
              fontWeight={600}
            />
            <XAxis
              axisLine={false}
              tickLine={false}
              dataKey={xKey}
              tickFormatter={(val: string) => {
                val = val.replace(/@/g, "\n@");
                val = val.replace(/ /g, "\n");
                return val;
              }}
              fontSize={10}
              interval={0}
              width={90}
              fontWeight={600}
              tickSize={0}
              type="category"
            />
            <Tooltip
              cursor={{ fill: "transparent" }}
              content={<CustomTooltip percentage={percentage} />}
            />
            <Legend
              verticalAlign="top"
              height={36}
              wrapperStyle={{ fontSize: "12px" }}
            />
            {statuses.map((status) => (
              <Bar
                key={status}
                dataKey={status!}
                name={status}
                stackId="a"
                fill={getColorForStatus(status!)}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </ScrollArea>
    </Box>
  );
}
