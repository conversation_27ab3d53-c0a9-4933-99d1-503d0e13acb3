import { Box, Flex } from "@mantine/core";

import ActiveCasesTab from "../Features/ActiveCases/ActiveCasesTab";
import { type PossibleTabs } from "../Home";
import AppointmentTab from "../Features/Appointment/AppointmentTab";
import CallbackTab from "../Features/Callback/CallbackTab";
import RecentLeadsTab from "../Features/RecentLeads/RecentLeadsTab";
import OmnichannelTab from "../Features/Omnichannel/OmnichannelTab";
import { useLayoutVisibility } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import OpenLeadsTab from "../Features/OpenLeads/OpenLeadsTab";
interface HomeTabsProps {
  activeTab: PossibleTabs;
  setActiveTab: (value: PossibleTabs) => void;
}

export interface TabProps {
  setActiveTab: (value: PossibleTabs) => void;
  isActive: boolean;
}

const tabsConfig: Record<string, string[]> = {
  "Sales & Service": ["RecentLeads"],
  "Sales & Service Supervisor": ["RecentLeads"],
  "Store Manager": [
    "Appointment",
    "Callback",
    "RecentLeads",
    "ActiveCases",
    "OpenLeads",
  ],
  Management: ["Appointment", "Callback", "RecentLeads", "ActiveCases"],
  Default: [],
};

const tabComponents: Record<
  string,
  React.ComponentType<{
    setActiveTab: (value: PossibleTabs) => void;
    isActive: boolean;
  }>
> = {
  Appointment: AppointmentTab,
  Callback: CallbackTab,
  RecentLeads: RecentLeadsTab,
  ActiveCases: ActiveCasesTab,
  Omnichannel: OmnichannelTab,
  OpenLeads: OpenLeadsTab,
};

export default function HomeTabs({ activeTab, setActiveTab }: HomeTabsProps) {
  const { activeNavbar } = useLayoutVisibility();
  const allowedTabs = tabsConfig[activeNavbar] || Object.keys(tabComponents);

  return (
    <Box my={32}>
      <Flex
        wrap="wrap"
        justify={"center"}
        direction={{
          base: "column",
          xs: "column",
          sm: "column",
          md: "row",
          lg: "row",
          xl: "row",
        }}
        gap={16}
      >
        {allowedTabs.map((tabId) => {
          const TabComponent = tabComponents[tabId];
          if (!TabComponent) {
            console.warn(`Tab component for ${tabId} not found`);
            return null;
          }
          return (
            <TabComponent
              key={tabId}
              setActiveTab={setActiveTab}
              isActive={activeTab === tabId}
            />
          );
        })}
      </Flex>
    </Box>
  );
}
