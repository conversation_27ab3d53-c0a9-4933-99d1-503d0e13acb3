import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, NumberInput, Paper } from "@mantine/core";
import { type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { ProductLookup } from "@/components/Lookup/Features/Products/ProductLookupField";
import { RefundLookup } from "@/components/Lookup/Features/Refunds/RefundLookupField";

const formSchema = z.object({
  price: z.coerce.number().nullable(),
  totalPrice: z.coerce.number().nullable(),
  quantityToReturn: z.coerce.number().nullable(),
  stillUsableQuantity: z.coerce.number().nullable(),
  refundId: z.string().nullable(),
  refund: z.object({}).nullable(),
  productId: z.string().nullable(),
  product: z.object({}).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface RefundProductFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function RefundProductForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: RefundProductFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      price: initialValues?.price ?? null,
      totalPrice: initialValues?.totalPrice ?? null,
      quantityToReturn: initialValues?.quantityToReturn ?? null,
      stillUsableQuantity: initialValues?.stillUsableQuantity ?? null,
      refundId: initialValues?.refundId ?? "",
      refund: initialValues?.refund ?? null,
      productId: initialValues?.productId ?? "",
      product: initialValues?.product ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="lg">
              <ProductLookup
                label={t("refundProducts.product")}
                initial={form.getValues().product}
                initialId={form.getValues().productId}
                identifier="productIdRefundProduct"
                mt="sm"
                {...form.getInputProps("productId")}
              />
              <NumberInput
                mt="sm"
                label={t("refundProducts.quantityToReturn")}
                {...form.getInputProps("quantityToReturn")}
              />
              <NumberInput
                mt="sm"
                label={t("refundProducts.stillUsableQuantity")}
                {...form.getInputProps("stillUsableQuantity")}
              />
              <NumberInput
                mt="sm"
                disabled
                label={t("refundProducts.price")}
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("price")}
              />
              <NumberInput
                mt="sm"
                disabled
                label={t("refundProducts.totalPrice")}
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("totalPrice")}
              />
              <RefundLookup
                mt="sm"
                disabled
                label={t("refundProducts.refund")}
                initial={form.getValues().refund}
                initialId={form.getValues().refundId}
                identifier="refundIdRefundProduct"
                {...form.getInputProps("refundId")}
              />
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
