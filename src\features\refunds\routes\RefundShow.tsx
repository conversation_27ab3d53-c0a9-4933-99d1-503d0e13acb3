import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { RefundForm, type FormSchema } from "../components/RefundForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";
import { AddAttachments, HandleDeleteAttachments } from "./RefundCreate";

export function RefundShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<Schemas["RefundPatchDto"]>(
    {
      resourcePath: "/api/Refunds/{id}",
      resourceId: id!,
      queryKey: "refund",
      skipInvalidation: true,
    },
  );
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/refunds";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("refund_" + id);
  };

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Refund"]>({
    resourcePath: "/api/Refunds/{id}",
    resourceId: id!,
    queryKey: "refund",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <RefundForm
      recordState={data.status}
      isCreate={false}
      title={t("refunds.showTitle", { id })}
      initialValues={
        filterFalsyValues({
          ...data,
        }) as FormSchema
      }
      onSubmit={(values, attachments, deleteAttachments) => {
        if (Object.keys(values).length === 0 && attachments.length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(
          { ...filteredValues },
          {
            onSuccess: async () => {
              try {
                await AddAttachments(id!, attachments, "Refunds");
                await HandleDeleteAttachments(deleteAttachments);

                await queryCache.invalidateQueries("refund_" + id);

                if (close) {
                  navigate(redirectTo);
                } else {
                  notifications.show({
                    color: "green",
                    title: t("notifications.createSuccessTitle"),
                    message: t("notifications.createSuccessMessage"),
                  });
                }
              } catch (error) {
                console.error("Error uploading attachments:", error);
              }
            },
          },
        );
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
