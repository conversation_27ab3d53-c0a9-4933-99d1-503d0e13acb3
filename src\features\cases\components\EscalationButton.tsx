import { useDisclosure } from "@mantine/hooks";
import { <PERSON><PERSON>, Button, Text } from "@mantine/core";
import { useTranslation } from "react-i18next";
import {
  IconArrowBigDownLines,
  IconArrowBigUpLines,
} from "@tabler/icons-react";
import { useParams } from "react-router-dom";
import { notifications } from "@mantine/notifications";
import { type SetStateAction, useState } from "react";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useQueryClient } from "react-query";
import { type Schemas } from "@/types";
import { useEntityQuery } from "@/features/entity/queries";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";

export default function EscalateButton() {
  const [opened, { open, close }] = useDisclosure(false);
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const queryCache = useQueryClient();

  const { data: caseRow = {} } = useEntityQuery<Schemas["Case"]>({
    resourcePath: "/api/Cases/{id}",
    resourceId: id!,
    queryKey: ["case", id],
  });

  const { mutate: updateCase } = useEntityUpdateMutation<
    Schemas["Case"],
    Schemas["CasePatchDto"]
  >({
    resourcePath: "/api/Cases/{id}",
    resourceId: id ?? "",
    queryKey: "case",
  });

  const [businessUnitId, setBusinessUnitId] = useState<string | null>(null);

  const caseBU = caseRow.businessUnit ?? null;
  const caseBUId = caseRow.businessUnitId ?? null;
  const caseOriginalBU = caseRow.originalBusinessUnit ?? null;
  const caseOriginalBUId = caseRow.originalBusinessUnitId ?? null;
  const caseStatus = caseRow.status ?? null;

  const handleEscalation = () => {
    if (businessUnitId) {
      updateCase(
        {
          status: "Escalated",
          businessUnitId: businessUnitId,
          originalBusinessUnitId: caseBUId,
        },
        {
          onSuccess: () => {
            notifications.show({
              color: "green",
              title: t("cases.escalationSuccessTitle"),
              message: t("cases.escalationSuccessMessage"),
            });
            void queryCache.invalidateQueries("cases");
            close();
          },
          onError: () => {
            notifications.show({
              color: "red",
              title: t("cases.escalationErrorTitle"),
              message: t("cases.escalationErrorMessage"),
            });
          },
        },
      );
    } else {
      notifications.show({
        color: "red",
        title: t("cases.businessUnitRequiredTitle"),
        message: t("cases.businessUnitRequiredMessage"),
      });
    }
  };

  const handleEscalationReturn = () => {
    updateCase(
      {
        status: "EscalationReturned",
        businessUnitId: caseOriginalBUId,
      },
      {
        onSuccess: () => {
          notifications.show({
            color: "green",
            title: t("cases.escalationSuccessTitle"),
            message: t("cases.escalationSuccessMessage"),
          });
          void queryCache.invalidateQueries("case_" + id);
          close();
        },
        onError: () => {
          notifications.show({
            color: "red",
            title: t("cases.escalationErrorTitle"),
            message: t("cases.escalationErrorMessage"),
          });
        },
      },
    );
  };

  return (
    <>
      <ButtonMain
        label={
          caseStatus != "Escalated"
            ? t("cases.escalate")
            : t("cases.returnEscalation")
        }
        icon={
          caseStatus != "Escalated" ? (
            <IconArrowBigUpLines size={18} />
          ) : (
            <IconArrowBigDownLines size={18} />
          )
        }
        onClick={open}
      />
      <Modal
        opened={opened}
        onClose={close}
        centered
        title={
          caseStatus != "Escalated"
            ? t("cases.escalate")
            : t("cases.returnEscalation")
        }
      >
        {caseStatus == "Escalated" ? (
          <Text>
            {t("cases.returnEscalationText", {
              BusinessUnitCode: caseOriginalBU?.code,
            })}
          </Text>
        ) : (
          <BusinessUnitLookup
            required
            mt="sm"
            label={t("cases.businessUnit")}
            identifier="caseBusinessUnitEscalation"
            initial={caseBU}
            initialId={caseBUId}
            onChange={(value: SetStateAction<string | null>) =>
              setBusinessUnitId(value)
            }
            value={businessUnitId}
          />
        )}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginTop: "1rem",
          }}
        >
          <Button onClick={close} variant="light" w={"48%"}>
            {t("cases.close")}
          </Button>
          <Button
            onClick={
              caseStatus != "Escalated"
                ? handleEscalation
                : handleEscalationReturn
            }
            w={"48%"}
          >
            {t("cases.confirm")}
          </Button>
        </div>
      </Modal>
    </>
  );
}
