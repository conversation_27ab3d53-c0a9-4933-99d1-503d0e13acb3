import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { type Schemas } from "@/types";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function ContactMomentList() {
  const { t } = useTranslation("features");
  return (
    <EntityLayout showBackButton={false}>
      {
        <EntityLayout.TableMantine<
          Schemas["ContactMomentRetrieveDto"],
          Schemas["ContactMomentRetrieveDtoPagedList"]
        >
          resourcePath="/api/ContactMoments"
          queryKey="contactMomentList"
          entityPath="contactMoments"
          columns={[
            {
              accessorKey: "comment",
              header: t("contactMoments.comment"),
              filterVariant: "text",
              Cell: (props) => EntityLinkRenderer(props, "contactMoments"),
            },
            {
              accessorKey: "lead",
              header: t("contactMoments.lead"),
              ...TableRenderer(LeadLookup, "leads", ["lastName", "firstName"]),
            },
            {
              accessorKey: "createdOn",
              header: t("entity.createdOn"),
              sortingFn: "datetime",
              filterVariant: "date-range",
              Cell: DateRenderer,
            },
          ]}
          initialSorting={[
            {
              id: "createdOn",
              desc: true,
            },
          ]}
        />
      }
    </EntityLayout>
  );
}
