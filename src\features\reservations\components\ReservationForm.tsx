import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Select } from "@mantine/core";
import { ReservationStatus } from "@/types/enums";
import { DatePickerInput } from "@mantine/dates";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { UnitList } from "@/features/units/routes/UnitList";

/**
 * @TODO: ownerId
 */

const formSchema = z.object({
  id: z.string(),
  start: z.date().nullable(),
  reservedUntil: z.date().nullable(),
  reservationStatus: z.enum(ReservationStatus as [string]),
  leadId: z.string().nullable(),
  lead: z.object({}).nullable(),
  contactId: z.string().nullable(),
  contact: z.object({}).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface ReservationFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function ReservationForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: ReservationFormProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      start: initialValues?.start ?? null,
      reservedUntil: initialValues?.reservedUntil ?? null,
      reservationStatus: initialValues?.reservationStatus ?? "Active",
      leadId: initialValues?.leadId ?? "",
      lead: initialValues?.lead ?? null,
      contactId: initialValues?.contactId ?? "",
      contact: initialValues?.contact ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <DatePickerInput
                valueFormat={dateFormat.toUpperCase()}
                clearable
                maxDate={
                  form.getInputProps("reservedUntil").value as Date | undefined
                }
                label={t("reservations.start")}
                {...form.getInputProps("start")}
                {...{ labelProps: { style: { flex: "1" } } }}
              />
              <DatePickerInput
                valueFormat={dateFormat.toUpperCase()}
                clearable
                minDate={form.getInputProps("start").value as Date | undefined}
                label={t("reservations.reservedUntil")}
                {...form.getInputProps("reservedUntil")}
                {...{ labelProps: { style: { flex: "1" } } }}
              />
              <Select
                searchable
                disabled
                label={t("leads.reservationStatus")}
                data={ReservationStatus}
                {...form.getInputProps("reservationStatus")}
                {...{ labelProps: { style: { flex: "1" } } }}
              />
              <LeadLookup
                label={t("leads.lead")}
                initial={form.getValues().lead}
                initialId={form.getValues().leadId}
                identifier="leadIdReservation"
                {...form.getInputProps("leadId")}
                {...{ labelProps: { style: { flex: "1" } } }}
              />
              <ContactLookup
                label={t("contacts.contact")}
                initial={form.getValues().contact}
                initialId={form.getValues().contactId}
                identifier="contactIdReservation"
                {...form.getInputProps("contactId")}
                {...{ labelProps: { style: { flex: "1" } } }}
              />
            </Paper>
          </Grid.Col>
          {initialValues?.id ? (
            <>
              <Grid.Col span={{ base: 12, md: 12 }}>
                <UnitList
                  parentEntityId={initialValues?.id ?? ""}
                  parentEntityName="Reservations"
                  parentEntityIdParam="reservationId"
                  visibleColumns={["unitType", "code"]}
                />
              </Grid.Col>
            </>
          ) : null}
        </Grid>
      </EntityLayout>
    </form>
  );
}
