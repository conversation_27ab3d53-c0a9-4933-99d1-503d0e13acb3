# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
public/locales/translations_comparison.*
public/locales/~$translations_comparison.*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env.*
.env
!.env.example

# Tests
coverage

# Sentry Config File
.env.sentry-build-plugin

# Sentry Config File
.env.sentry-build-plugin
