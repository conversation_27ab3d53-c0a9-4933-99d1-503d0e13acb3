import { useState } from "react";
import WizardHeader from "../Common/Header/WizardHeader";
import { Box } from "@mantine/core";
import { type PageName } from "../NoShow/Common/Common";
import PageLeadQuickForm from "./PageLeadQuickForm";

interface LeadsQuickAddWizardProps {
  closeModal?: () => void;
}

const DEFAULT_TOTAL_PAGES = 1;

export default function LeadsQuickAddWizard({
  closeModal,
}: LeadsQuickAddWizardProps) {
  const [totalPages, setTotalPages] = useState(DEFAULT_TOTAL_PAGES);
  const [pages, setPages] = useState<PageName[]>(["COMPLETED"]);
  return (
    <Box h={"80vh"}>
      <WizardHeader
        defaultTotalPages={DEFAULT_TOTAL_PAGES}
        setPages={setPages}
        setTotalPages={setTotalPages}
        pages={pages}
        totalPages={totalPages}
        closeModal={closeModal}
      />
      <PageLeadQuickForm closeModal={closeModal} />
    </Box>
  );
}
