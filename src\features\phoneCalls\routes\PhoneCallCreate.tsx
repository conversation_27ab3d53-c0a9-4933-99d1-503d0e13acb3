import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useSearchParams } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { PhoneCallForm } from "../components/PhoneCallForm";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";
import { type Dispatch, type SetStateAction } from "react";
import { useQueryClient } from "react-query";

interface PhoneCallCreateProps {
  refreshForm?: () => void;
  closeModal?: () => void;
  leadId?: string;
  businessUnitId?: string | null;
  startDate?: string | null;
  isModal?: boolean;
  isCallback?: boolean;
  setPhantomEvent?: Dispatch<SetStateAction<Schemas["PhoneCall"] | null>>;
}

export function PhoneCallCreate({
  refreshForm: refreshAllPhoneCalls,
  closeModal,
  leadId: leadIdProp,
  businessUnitId,
  startDate,
  isModal,
  isCallback,
  setPhantomEvent,
}: PhoneCallCreateProps) {
  const [searchParams] = useSearchParams();
  const queryCache = useQueryClient();
  const { mutate } = useEntityCreateMutation<
    Schemas["PhoneCall"],
    Schemas["PhoneCallCreateDto"]
  >({ resourcePath: "/api/PhoneCalls", queryKey: "phoneCall" });
  const { t } = useTranslation("features");
  const leadId = searchParams.get("leadId");
  const leadIdToUse = leadId ?? leadIdProp;
  const {
    data: lead = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/Leads/{id}",
    resourceId: leadIdToUse!,
    queryKey: "leadPhoneCall",
  });
  const {
    data: businessUnit = {},
    isLoading: isLoadingBu,
    isFetching: isFetchingBU,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/BusinessUnits/{id}",
    resourceId: businessUnitId ?? "",
    queryKey: "businessUnitPhoneCall",
  });
  if (isLoading || isFetching || isLoadingBu || isFetchingBU) {
    return <></>;
  }
  const initialValues = {
    id: "",
    description: "",
    subject: "",
    startDate: startDate ? new Date(startDate) : null,
    endDate: null,
    callbackDate: null,
    businessUnitId: businessUnitId ?? "",
    businessUnit: businessUnit,
    contactId: lead?.existingContactId ?? "",
    contact: lead?.existingContact ?? null,
    callbackId: "",
    callback: null,
    leadId: leadIdToUse ?? "",
    lead: lead,
    phoneCallType: isCallback ? "Callback" : "PhoneCall",
    phoneCallStatus: "Open",
    recordState: "Active",
  };
  return (
    <PhoneCallForm
      isCreate={true}
      isModal={isModal}
      isCallback={isCallback}
      showBackButton={false}
      initialValues={initialValues}
      closeModal={closeModal}
      setPhantomEvent={setPhantomEvent}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: () => {
            void queryCache.invalidateQueries("lead_omnichannel_list");
            void queryCache.invalidateQueries("lead_" + leadIdToUse);
            notifications.show({
              color: "green",
              title: t("phoneCalls.createStateSuccessTitle"),
              message: t("phoneCalls.createStateSuccessMessage"),
            });
            if (refreshAllPhoneCalls) {
              refreshAllPhoneCalls();
            }
            if (closeModal) {
              closeModal();
            }
            if (setPhantomEvent) {
              setPhantomEvent(null);
            }
          },
        });
      }}
    />
  );
}
