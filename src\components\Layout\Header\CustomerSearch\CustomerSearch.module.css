.input {
  background-color: var(--mantine-color-neutral-1);
  border-radius: 32px;
  border: none;
  color: var(--mantine-color-neutral-6);

  margin: 0px !important;
}

.customerCard:hover {
  background-color: var(--mantine-color-neutral-1);
  cursor: pointer;
  border-radius: 8px;
}

.customerCardContainer {
  border-style: solid;
  border-width: 1px;
  border-color: var(--mantine-color-neutral-6);
  border-radius: 8px;
}

.noResults {
  color: var(--mantine-color-neutral-6);
  font-style: italic;
  font-size: 12px;
}

.inputButton {
  background-color: var(--mantine-color-neutral-1);
  border-radius: 32px;
  border: none;
  color: var(--mantine-color-neutral-6);
}

.inputButton:hover {
  background-color: var(--mantine-color-neutral-2);
  border-radius: 32px;
  border: none;
  color: var(--mantine-color-neutral-9);
}

.footerButton {
  background-color: var(--mantine-color-white);
  border: none;
  color: var(--mantine-color-neutral-9);
}

.footerButton:hover {
  background-color: var(--mantine-color-neutral-1);
  border: none;
  color: var(--mantine-color-neutral-9);
}

.option {
  padding: 0px !important;

  &[data-combobox-selected] {
    background-color: var(--mantine-color-primary-1);
    border-radius: 8px;
    border: none;
  }
}

.options {
  margin: 8px !important;
}
