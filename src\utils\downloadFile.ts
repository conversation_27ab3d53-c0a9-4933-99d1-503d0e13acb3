import { api } from "@/lib/api";
import { type AxiosRequestConfig } from "axios";

interface DownloadFileProps {
  id?: string;
  fileName?: string | null;
}

export async function downloadFile({
  id,
  fileName,
}: DownloadFileProps): Promise<void> {
  if (!id || !fileName) {
    throw new Error("Missing required parameters: 'id' or 'fileName'.");
  }
  const config: AxiosRequestConfig = {
    responseType: "blob",
    headers: { "Content-Type": "application/octet-stream" },
  };
  const fileUrl = `/api/Attachments/DownloadAttachmment/${id}`;
  try {
    const response = await api.get<Blob>(fileUrl, config);
    const blob = response.data;
    const url = window.URL.createObjectURL(blob);
    const anchor = document.createElement("a");
    anchor.href = url;
    anchor.download = fileName;
    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    throw new Error(`File download failed: ${(error as Error).message}`);
  }
}
