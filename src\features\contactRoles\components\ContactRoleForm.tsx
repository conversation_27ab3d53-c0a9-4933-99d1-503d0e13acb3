import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Switch, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";

const formSchema = z.object({
  id: z.string(),
  name: z.string(),
  isMain: z.boolean().default(false),
});

type FormSchema = z.infer<typeof formSchema>;

interface ContactRoleFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  isCreate: boolean;
}

export function ContactRoleForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: ContactRoleFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      name: initialValues?.name ?? "",
      isMain: initialValues?.isMain ?? false,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <TextInput
                required
                label={t("contactRoles.name")}
                {...form.getInputProps("name")}
              />
              <Switch
                mt="sm"
                label={t("contactRoles.isMain")}
                checked={form.getValues().isMain}
                {...form.getInputProps("isMain")}
              />
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
