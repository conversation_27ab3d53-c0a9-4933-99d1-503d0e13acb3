import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Select, TextInput } from "@mantine/core";
import { useState, type ReactNode, type MutableRefObject } from "react";
import "@mantine/tiptap/styles.css";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { AppUserList } from "@/features/appUsers/routes/AppUserList";
import { RemoveUserButton } from "./RemoveUserButton";
import { type MRT_RowData, type MRT_TableInstance } from "mantine-react-table";
import { UserGroupType } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";

const formSchema = z.object({
  id: z.string(),
  name: z.string(),
  userGroupType: z.enum(UserGroupType as [string]).nullable(),
});

type FormSchema = z.infer<typeof formSchema>;

interface UserGroupFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title?: string;
  contextRecordId?: string;
  isCreate: boolean;
}

export function UserGroupForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: UserGroupFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const [tableRef, setTableRef] =
    useState<MutableRefObject<MRT_TableInstance<MRT_RowData> | null> | null>(
      null,
    );

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      name: initialValues?.name ?? "",
      userGroupType: initialValues?.userGroupType ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: isCreate ? 12 : 4 }}>
            <Paper shadow="xs" p="lg" ml={10}>
              <FieldValidation isDirty={form.isDirty("name")}>
                <TextInput
                  required
                  label={t("unitTypes.name")}
                  {...form.getInputProps("name")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("userGroupType")}>
                <Select
                  searchable
                  label={t("userGroups.userGroupType")}
                  data={UserGroupType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("userGroups", value)),
                  }))}
                  clearable
                  {...form.getInputProps("userGroupType")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          {!isCreate && (
            <Grid.Col span={{ base: 12, md: 8 }}>
              <AppUserList
                parentEntityId={initialValues?.id}
                parentEntityName="UserGroups"
                toolbarOverride={<RemoveUserButton table={tableRef} />}
                queryKey={`groupUsers`}
                setTableRef={setTableRef}
              />
            </Grid.Col>
          )}
        </Grid>
      </EntityLayout>
    </form>
  );
}
