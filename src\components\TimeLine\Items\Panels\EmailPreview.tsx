import { useEffect, useRef } from "react";

function EmailPreview({ htmlContent }: { htmlContent: string }) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;
    const adjustHeight = () => {
      try {
        const iframeDoc = iframe.contentWindow?.document;
        if (iframeDoc) {
          const iframeHeight = iframeDoc.body.scrollHeight;
          iframe.style.height = `${iframeHeight}px`;
        }
      } catch (err) {
        console.error("Failed to adjust iframe height:", err);
      }
    };
    const onLoad = () => {
      adjustHeight();
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (doc) {
        let head = doc.head;
        // If there is no <head>, create one
        if (!head) {
          head = doc.createElement("head");
          doc.documentElement.insertBefore(head, doc.body);
        }
        // Create a base tag with target _blank
        const base = doc.createElement("base");
        base.setAttribute("target", "_blank");
        // Insert the base tag as the first element in the head
        head.insertBefore(base, head.firstChild);
      }
    };
    adjustHeight();
    iframe.addEventListener("load", onLoad);

    // Cleanup listener on unmount
    return () => {
      iframe.removeEventListener("load", adjustHeight);
    };
  }, [htmlContent]);

  return (
    <iframe
      ref={iframeRef}
      srcDoc={htmlContent}
      style={{ width: "100%", border: "none", transition: "height 0.2s" }}
    />
  );
}

export default EmailPreview;
