import json
import os
import shutil
from datetime import datetime
from openpyxl import load_workbook
from collections import defaultdict

def create_backup(base_dir, languages):
    timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    backup_dir = os.path.join(base_dir, "backup", timestamp)
    
    for lang in languages:
        src_dir = os.path.join(base_dir, lang)
        dst_dir = os.path.join(backup_dir, lang)
        
        if os.path.exists(src_dir):
            shutil.copytree(src_dir, dst_dir)
            print(f"Created backup for {lang} in {dst_dir}")
        else:
            print(f"Warning: Source directory for {lang} does not exist. Skipping backup.")
    
    return backup_dir

def unflatten_dict(d):
    result = {}
    for key, value in d.items():
        parts = key.split('.')
        d = result
        for part in parts[:-1]:
            if part not in d:
                d[part] = {}
            d = d[part]
        d[parts[-1]] = value
    return result

def process_excel_to_json(excel_file, output_dir, languages):
    wb = load_workbook(filename=excel_file, read_only=True)
    
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        headers = [cell.value for cell in ws[1] if cell.value]
        file_languages = [lang for lang in headers[1:] if lang in languages]
        
        data = defaultdict(lambda: defaultdict(dict))
        
        for row in ws.iter_rows(min_row=2, values_only=True):
            if not row[0]:  # Skip empty rows
                continue
            key = row[0]
            for i, lang in enumerate(file_languages):
                if row[i+1]:  # Check if the cell has a value
                    data[lang][key] = row[i+1]
        
        for lang in file_languages:
            # Unflatten the dictionary
            unflattened_data = unflatten_dict(data[lang])
            
            # Ensure the language directory exists
            lang_dir = os.path.join(output_dir, lang)
            os.makedirs(lang_dir, exist_ok=True)
            
            # Write the JSON file
            output_file = os.path.join(lang_dir, f"{sheet_name}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(unflattened_data, f, ensure_ascii=False, indent=2)
            
            print(f"Created {output_file}")

def main():
    base_dir = '.'  # Current directory, adjust if needed
    languages = ['English', 'Dutch']  # Add more languages as needed
    excel_file = 'translations_comparison.xlsx'
    
    # Create backup
    backup_dir = create_backup(base_dir, languages)
    print(f"Backup created in {backup_dir}")
    
    # Process Excel and create new JSON files
    process_excel_to_json(excel_file, base_dir, languages)
    print("JSON files have been created successfully.")

if __name__ == "__main__":
    main()