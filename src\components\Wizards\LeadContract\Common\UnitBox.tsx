// UnitBox.tsx
import { useEntityPostMutation } from "@/features/entity/mutations";
import { type Schemas } from "@/types"; // Ensure this path is correct for your project
import {
  Badge,
  Box,
  Flex,
  Paper,
  Text,
  Group,
  Grid,
  Menu,
} from "@mantine/core";
import {
  IconCalendar,
  IconCurrencyDollar,
  IconDots,
  IconTag,
  IconTrash,
} from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { type PageName } from "../LeadContractWizard";

const formatPrice = (
  price?: number | null,
  currencySymbol?: string | null,
): string => {
  if (price == null || currencySymbol == null) return "N/A";
  return `${currencySymbol}${price.toFixed(2).replace(".", ",")} p/m`;
};

interface UnitBoxProps {
  currentWizardId?: string;
  contractLine: Schemas["ContractLine"];
  setPages: React.Dispatch<React.SetStateAction<PageName[]>>;
  pages: string[];
}

interface UnitInfoItemProps {
  label: string;
  value: React.ReactNode;
}

const UnitInfoItem: React.FC<UnitInfoItemProps> = ({ label, value }) => (
  <Box>
    <Text size="xs" c="dimmed">
      {label}
    </Text>
    {typeof value === "string" ? (
      <Text fw={500} lineClamp={1}>
        {value}
      </Text>
    ) : (
      value
    )}
  </Box>
);

export function UnitBox({ contractLine, currentWizardId }: UnitBoxProps) {
  const { t } = useTranslation("features");

  const { mutate } = useEntityPostMutation({
    resourcePath: `/api/Contracts/${currentWizardId}/RemoveContractLines`,
    queryKey: "unitContractLines",
  });
  const queryClient = useQueryClient();

  const unitNumber = contractLine?.unit?.unitCode ?? "N/A";

  const getUnitTypeName = () => {
    if (
      typeof contractLine?.unit?.unitType === "string" &&
      contractLine?.unit?.unitType
    )
      return contractLine?.unit?.unitType;
    return contractLine?.unit?.unitType ?? t("Unit type");
  };

  const getUnitSizeName = () => {
    if (
      typeof contractLine?.unit?.area === "string" &&
      contractLine?.unit?.area
    )
      return contractLine?.unit?.area;
    return contractLine?.unit?.area ?? t("Unit area");
  };

  const getLocationName = () => {
    if (
      typeof contractLine?.unit?.businessUnit?.locationName === "string" &&
      contractLine?.unit?.businessUnit?.locationName
    )
      return contractLine?.unit?.businessUnit?.locationName;
    return contractLine?.unit?.businessUnit?.locationName ?? t("Name location");
  };

  const getPriceDisplay = () => {
    return formatPrice(contractLine?.unit?.pricePerMonth, "€");
  };

  const unitType = getUnitTypeName() as string;
  const unitSize = getUnitSizeName() as string;
  const location = getLocationName();
  const price = getPriceDisplay();
  const status = contractLine?.unit?.status ?? t("Available");

  const getStatusColor = (statusValue: string) => {
    switch (statusValue?.toLowerCase()) {
      case "available":
      case t("available").toLowerCase():
        return "green";
      case "reserved":
      case t("reserved").toLowerCase():
        return "orange";
      case "occupied":
      case t("occupied").toLowerCase():
        return "red";
      default:
        return "gray";
    }
  };
  const unitTags: Schemas["UnitTag"][] = contractLine?.unitTags ?? [];
  const moveInDate = contractLine?.moveInDate ?? t("N/A");
  return (
    <Paper
      shadow="sm"
      radius="md"
      withBorder
      style={{ border: "1px solid #DFDFDF" }}
    >
      <Flex
        style={{
          border: "1px solid #DFDFDF",
          borderTopRightRadius: 8,
          borderTopLeftRadius: 8,
        }}
        justify="space-between"
        p={8}
        align="center"
        mb="xs"
        bg={"#F6F6F6"}
      >
        <Text fw={700} size="lg">
          {t("Unit#", "Unit#")}
          {unitNumber}
        </Text>
        <Flex gap={24} align="center">
          {/* TODO */}
          <Menu>
            <Menu.Target>
              <IconDots style={{ cursor: "pointer" }} />
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item>
                <Flex align="center" justify={"start"} gap={16}>
                  <IconCalendar color="#282828" size={16} />
                  <Box>
                    <Text fz={14} fw={600} c="#282828">
                      {t("Edit move-in date")}
                    </Text>
                  </Box>
                </Flex>
              </Menu.Item>
              <Menu.Item>
                <Flex align="center" justify={"start"} gap={16}>
                  <IconTag color="#282828" size={16} />
                  <Box>
                    <Text fz={14} fw={600} c="#282828">
                      {t("Change tags")}
                    </Text>
                  </Box>
                </Flex>
              </Menu.Item>
              <Menu.Item>
                <Flex align="center" justify={"start"} gap={16}>
                  <IconCurrencyDollar color="#282828" size={16} />
                  <Box>
                    <Text fz={14} fw={600} c="#282828">
                      {t("Set discount")}
                    </Text>
                  </Box>
                </Flex>
              </Menu.Item>
              <Menu.Item>
                <Flex
                  align="center"
                  justify={"start"}
                  gap={16}
                  onClick={() => {
                    mutate([contractLine?.id], {
                      onSuccess: () => {
                        void queryClient.invalidateQueries(
                          "contractLines_list",
                        );
                      },
                    });
                  }}
                >
                  <IconTrash color="red" size={16} />
                  <Box>
                    <Text fz={14} fw={600} c="red">
                      {t("Remove unit")}
                    </Text>
                  </Box>
                </Flex>
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Flex>
      </Flex>
      <Box pl={16} pr={16} pb={16}>
        <Grid gutter="2">
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Type")} value={unitType} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Size")} value={unitSize} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Location")} value={location} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Move in date")} value={moveInDate} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Price")} value={price} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem
              label={t("Status")}
              value={
                <Badge
                  color={getStatusColor(status)}
                  variant="light"
                  mt={4}
                  fullWidth
                  style={{ textTransform: "capitalize" }}
                >
                  {status}
                </Badge>
              }
            />
          </Grid.Col>
        </Grid>

        {unitTags && unitTags.length > 0 && (
          <Box mt="xs">
            <Text size="xs" c="dimmed" mb={4}>
              {t("Tags")}
            </Text>
            <Group gap="xs" mt={4}>
              {unitTags?.map((tag, index) => (
                <Badge key={index} variant="light" color="blue" radius="xl">
                  {tag.tag?.name ?? ""}
                </Badge>
              ))}
            </Group>
          </Box>
        )}
      </Box>
    </Paper>
  );
}
