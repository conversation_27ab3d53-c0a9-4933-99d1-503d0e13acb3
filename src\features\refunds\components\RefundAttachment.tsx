import { <PERSON>Loader } from "@/components/PageLoader";
import { useEntityListQuery } from "@/features/entity/queries";
import { type PathKeys, type Schemas } from "@/types";
import { downloadFile } from "@/utils/downloadFile";
import {
  ActionIcon,
  Flex,
  Text,
  Center,
  useMantineTheme,
  Tooltip,
} from "@mantine/core";
import {
  IconFile,
  IconPaperclip,
  IconFileTypePdf,
  IconFileTypeTxt,
  IconPhoto,
  IconFileZip,
  IconFileTypeJs,
  IconFileTypeHtml,
  IconFileSpreadsheet,
  IconX,
  IconTrash,
} from "@tabler/icons-react";
import { type Dispatch, type SetStateAction, useRef } from "react";
import classes from "./refundAttachment.module.css";
import { deleteFile } from "@/utils/deleteFile";
import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";
import { modals } from "@mantine/modals";
import { notifications } from "@mantine/notifications";

interface Attachment {
  attachmentType: Schemas["AttachmentTypeEnum"];
  file: File;
}

interface RefundAttachmentProps {
  refundId: string;
  attachmentType: Schemas["AttachmentTypeEnum"];
  attachment: Attachment[];
  setAttachment: Dispatch<SetStateAction<Attachment[]>>;
  deleteAttachments: string[];
  setDeleteAttachments: Dispatch<SetStateAction<string[]>>;
  required?: boolean;
  allowDelete?: boolean;
}

export function RefundAttachment({
  refundId,
  attachmentType,
  setAttachment,
  attachment,
  setDeleteAttachments,
  required = false,
  allowDelete = false,
}: RefundAttachmentProps) {
  const theme = useMantineTheme();
  const { t } = useTranslation("features");
  const queryCache = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    attachmentType: Schemas["AttachmentTypeEnum"],
  ) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      setAttachment((prevFiles) => [
        ...prevFiles.filter((file) => file.attachmentType !== attachmentType),

        ...newFiles.map((file) => ({
          attachmentType: attachmentType,
          file: file,
        })),
      ]);

      setDeleteAttachments((prevIds) => [
        ...prevIds,
        ...(attachmentsData
          ? attachmentsData.map((item) => item.id ?? "")
          : []),
      ]);
    }
  };

  const removeFile = (index: number) => {
    setAttachment((prevFiles) => prevFiles.filter((_, i) => i !== index));

    // Remove IDs where item.attachmentType matches the given attachmentType
    setDeleteAttachments((prevIds) =>
      prevIds.filter(
        (id) =>
          !attachmentsData?.some(
            (item) => item.id === id && item.attachmentType === attachmentType,
          ),
      ),
    );

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDeleteFile = (id: string) => {
    if (id) {
      void deleteFile(id);
      void queryCache.invalidateQueries("refund_" + refundId);
      notifications.show({
        color: "primary",
        title: "Delete",
        message: "File deleted !",
      });
    }
  };

  const replaceAttachment = (id: string, index: number) => {
    if (id || id.trim() !== "") {
      triggerFileInput();
    } else if (index !== undefined && index !== null) {
      triggerFileInput();
    } else {
      return;
    }
  };

  const openDeleteConfirmModal = (id: string) => {
    modals.openConfirmModal({
      title: (
        <Text fw={600} size="lg">
          {`${t("attachments.deleteConfirmationQuestion")}?`}
        </Text>
      ),
      centered: true,
      children: (
        <Text size="sm">{t("attachments.deleteConfirmationWarning")}</Text>
      ),
      labels: {
        confirm: t("attachments.deleteConfirmation"),
        cancel: t("attachments.deleteCancel"),
      },
      confirmProps: { color: "red" },
      onConfirm: () => {
        handleDeleteFile(id);
      },
    });
  };

  const { data, isLoading } = useEntityListQuery<
    Schemas["AttachmentRetrieveDtoPagedList"]
  >({
    resourcePath: `/api/Refunds/${refundId}/attachments` as PathKeys,
    queryKey: "attachment_" + refundId,
    enabled: !!refundId, // Ensure the query only runs if refundId exists
    params: {
      filter: `attachmentType == ${attachmentType}`,
    },
  });

  if (isLoading) {
    return <PageLoader />;
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const getFileIcon = (filename: string) => {
    const extension = filename.split(".").pop()?.toLowerCase();
    const defaultColor = theme.colors.primary?.[7] ?? "black";
    const iconMap: Record<string, React.ReactNode> = {
      pdf: <IconFileTypePdf size={20} stroke={1.5} color={defaultColor} />,
      txt: <IconFileTypeTxt size={20} stroke={1.5} color={defaultColor} />,
      jpg: <IconPhoto size={20} stroke={1.5} color={defaultColor} />,
      jpeg: <IconPhoto size={20} stroke={1.5} color={defaultColor} />,
      png: <IconPhoto size={20} stroke={1.5} color={defaultColor} />,
      zip: <IconFileZip size={20} stroke={1.5} color={defaultColor} />,
      js: <IconFileTypeJs size={20} stroke={1.5} color={defaultColor} />,
      html: <IconFileTypeHtml size={20} stroke={1.5} color={defaultColor} />,
      xlsx: <IconFileSpreadsheet size={20} stroke={1.5} color={defaultColor} />,
    };

    // Default icon if no match found
    return extension && iconMap[extension] ? (
      iconMap[extension]
    ) : (
      <IconFile size={20} stroke={1.5} color={defaultColor} />
    );
  };

  const attachmentsData = data?.data ?? [];

  const items =
    attachment &&
    attachment.length > 0 &&
    attachment.some((x) => x.attachmentType === attachmentType) ? (
      attachment
        .map((item, index) => ({ ...item, originalIndex: index }))
        .filter((x) => x.attachmentType === attachmentType)
        .map((item) => (
          //Attachments data display from user input
          <>
            <Flex key={item.originalIndex} w={"100%"} gap={8} px={8} py={2}>
              <Center>{getFileIcon(item.file.name ?? "")}</Center>

              <Center>
                <Text size="xs" inline lineClamp={1} c={theme.colors.gray[9]}>
                  {item.file.name}
                </Text>
              </Center>
            </Flex>
            <Center>
              <Tooltip label={t("attachments.removeAttachment")}>
                <ActionIcon
                  variant="subtle"
                  color="red"
                  size="24px"
                  onClick={() => removeFile(item.originalIndex)}
                >
                  <IconX style={{ width: "60%", height: "60%" }} />
                </ActionIcon>
              </Tooltip>
            </Center>
            <Center>
              <Tooltip label={t("attachments.replaceAttachment")}>
                <ActionIcon
                  variant="subtle"
                  color="primary"
                  size="24px"
                  onClick={() => replaceAttachment("", item.originalIndex)}
                >
                  <IconPaperclip style={{ width: "60%", height: "60%" }} />
                </ActionIcon>
              </Tooltip>
            </Center>
          </>
        ))
    ) : attachmentsData && attachmentsData.length > 0 ? (
      attachmentsData.map((item, index) => (
        //Attachments data display from DB
        <>
          <Tooltip label={t("attachments.downloadAttachment")}>
            <Flex
              key={item.id}
              w={"100%"}
              //style={{ cursor: "pointer" }}
              classNames={{
                root: classes.root,
              }}
              gap={8}
              px={8}
              py={2}
              onClick={() => {
                void downloadFile({ id: item.id, fileName: item.name });
              }}
            >
              <Center>{getFileIcon(item.name ?? "")}</Center>

              <Center>
                <Text size="xs" inline lineClamp={1} c={theme.colors.gray[9]}>
                  {item.name}
                </Text>
              </Center>
            </Flex>
          </Tooltip>
          {
            <Center>
              <Tooltip label={t("attachments.deleteAttachment")}>
                <ActionIcon
                  disabled={!allowDelete}
                  variant="subtle"
                  color="red"
                  size="24px"
                  onClick={() => openDeleteConfirmModal(item.id ?? "")}
                >
                  <IconTrash style={{ width: "60%", height: "60%" }} />
                </ActionIcon>
              </Tooltip>
            </Center>
          }
          <Center>
            <Tooltip label={t("attachments.replaceAttachment")}>
              <ActionIcon
                variant="subtle"
                color="primary"
                size="24px"
                onClick={() => replaceAttachment(item.id ?? "", index)}
              >
                <IconPaperclip style={{ width: "60%", height: "60%" }} />
              </ActionIcon>
            </Tooltip>
          </Center>
        </>
      ))
    ) : (
      //No current attachments
      <Flex
        w={"100%"}
        classNames={{
          root: classes.root,
        }}
        gap={8}
        px={8}
        py={2}
        onClick={() => {
          triggerFileInput();
        }}
      >
        <Center>
          <IconPaperclip size={16} stroke={1.5} color={theme.colors.gray[5]} />
        </Center>
        <Center>
          <Text size="xs" c={theme.colors.gray[5]} inline>
            {t("attachments.addAttachment")}
          </Text>
        </Center>
      </Flex>
    );

  //valid if not required or attachment of a specified type exists
  const isFieldValid = !!(
    !required ||
    attachment.filter((a) => a.attachmentType === attachmentType).length > 0 ||
    (attachmentsData?.length ?? 0) > 0
  );

  return (
    <>
      <div style={{ position: "relative" }}>
        <input
          name="attachment"
          type="file"
          ref={fileInputRef}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
            handleFileChange(event, attachmentType);
          }}
          style={{
            opacity: 0,
            position: "absolute",
            zIndex: -1,
            width: "640px",
            height: "32px",
          }}
          accept="all"
          required={!isFieldValid}
        />

        <Flex gap={8} justify="space-between" h={32}>
          <Flex
            typeof="Input"
            w={"100%"}
            justify="flex-end"
            gap={8}
            style={{
              border: `1px solid ${theme.colors.gray[4]}`,
              borderRadius: "4px",
              padding: "2px",
            }}
          >
            {items}
          </Flex>
        </Flex>
      </div>
    </>
  );
}
