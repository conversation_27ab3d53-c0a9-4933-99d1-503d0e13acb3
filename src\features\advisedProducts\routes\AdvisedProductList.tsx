import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { AdvisedProductsColumns } from "../table/AdvisedProductsColumns";
import { AdvisedProductCreate } from "./AdvisedProductCreate";

const PATH = "AdvisedProducts";

export function AdvisedProductListInner({
  visibleColumns,
  resourcePath,
  createPath,
  parentEntityId,
  hideCreate = false,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["AdvisedProductRetrieveDto"],
        Schemas["AdvisedProductRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="advisedProduct"
        entityPath="advisedProducts"
        title={t("advisedProducts.title")}
        pageSize={10}
        toolbar={
          !hideCreate ? (
            <Group>
              <EntityLayout.CreateButton
                to={createPath}
                FormComponent={
                  parentEntityId ? AdvisedProductCreate : undefined
                }
                formProps={
                  parentEntityId
                    ? {
                        parentEntityId: parentEntityId,
                        redirectTo: "/app/advisedProducts",
                        usingModal: true,
                      }
                    : undefined
                }
              />
            </Group>
          ) : null
        }
        redirectTo={window.location.pathname}
        visibleColumns={visibleColumns}
        columns={AdvisedProductsColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function AdvisedProductList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
  hideCreate = false,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <AdvisedProductListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
        hideCreate={hideCreate}
      />
    </ListCommandsProvider>
  );
}
