import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import { Group } from "@mantine/core";
import { AdvisedUnitsColumns } from "../table/AdvisedUnitsColumns";
import { AdvisedUnitCreate } from "./AdvisedUnitCreate";

const PATH = "AdvisedUnits";

export function AdvisedUnitListInner({
  visibleColumns,
  resourcePath,
  createPath,
  parentEntityId,
  hideCreate = false,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["AdvisedUnitRetrieveDto"],
        Schemas["AdvisedUnitRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="advisedUnit"
        entityPath="advisedUnits"
        title={t("advisedUnits.title")}
        pageSize={10}
        toolbar={
          !hideCreate ? (
            <Group>
              <EntityLayout.CreateButton
                to={createPath}
                FormComponent={parentEntityId ? AdvisedUnitCreate : undefined}
                formProps={
                  parentEntityId
                    ? {
                        parentEntityId: parentEntityId,
                        redirectTo: "/app/advisedUnits",
                        usingModal: true,
                      }
                    : undefined
                }
              />
            </Group>
          ) : null
        }
        redirectTo={window.location.pathname}
        visibleColumns={visibleColumns}
        columns={AdvisedUnitsColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function AdvisedUnitList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
  hideCreate = false,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <AdvisedUnitListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
        hideCreate={hideCreate}
      />
    </ListCommandsProvider>
  );
}
