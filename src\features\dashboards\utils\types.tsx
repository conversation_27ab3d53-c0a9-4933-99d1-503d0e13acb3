import { type StatisticsParams } from "./logic";

// Represents a single statistic item
export interface Statistic {
  display: string;
  value: number;
  total: number;
  percentage: string;
  totalPercentage: string;
  metric: boolean;
  stackedTotal?: StackedStatisticValue[];
}

export interface StackedStatisticValue {
  count: number;
  status: string;
}

export interface MetricInterface {
  metric: string;
  url: string;
  filter: string;
}

export interface MetricProps {
  metric: string;
  period?: string;
  sales?: boolean;
}

export const formatMetrics = (
  metrics: MetricProps[],
  startDate: string,
  endDate: string,
  businessUnit?: string,
): StatisticsParams[] => {
  const queries: StatisticsParams[] = metrics.map((metric) => {
    const filter = businessUnit ? `businessUnitId == ${businessUnit}` : "";
    return {
      filter: filter,
      metric: metric.metric,
      startDate: startDate,
      endDate: endDate,
    };
  });
  return queries;
};
// Enum for possible EntityTypes
export enum EntityType {
  Lead = "Lead",
  Appointment = "Appointment",
  Combined = "Combined",
}

// Enum for possible StatisticTypes
export enum StatisticType {
  EditedTotal = "EditedTotal",
  TotalReservationsClosedWon = "TotalReservationsClosedWon",
  Reached = "Reached",
  SuccessBookedAppointment = "SuccessBookedAppointment",
  BookedAppointments = "BookedAppointments",
  Priorities = "Priorities",
  NoShow = "NoShow",
  TotalReservations = "TotalReservations",
  FutureAppointments = "FutureAppointments",
}

// Represents the key for each statistic group
export interface StatisticKey {
  StatisticType: StatisticType;
}

// Represents the entire response structure
export interface ReportResponse {
  statistics: Record<string, Statistic[]>;
}

// Type for the parsed and organized report data
export type StatisticsReport = Record<string, Statistic[]>;

// Utility function to parse the key string
export function parseStatisticKey(key: string): StatisticKey | null {
  const match = key.match(/StatisticKey \{ StatisticType = (\w+) \}/);
  if (match) {
    return {
      StatisticType: match[1] as StatisticType,
    };
  }
  return null;
}

// Utility function to generate key string
export function generateKeyString(key: StatisticKey): string {
  return `StatisticKey { StatisticType = ${key.StatisticType} }`;
}

// Utility function to get a typed key for the Report
export function getReportKey(statisticType: StatisticType): string {
  return `${statisticType}`;
}

// Type guard to check if a string is a valid EntityType
export function isEntityType(value: string): value is EntityType {
  return Object.values(EntityType).includes(value as EntityType);
}

// Type guard to check if a string is a valid StatisticType
export function isStatisticType(value: string): value is StatisticType {
  return Object.values(StatisticType).includes(value as StatisticType);
}
