import { useLayoutVisibility } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import { useEntityListQuery } from "@/features/entity/queries";
import { readRecentLeads } from "@/features/leads/localStorage/leadStorage";
import UseUserBusinessUnit from "@/hooks/businessUnit/useUserBusinessUnit";
import { type Schemas } from "@/types";

export default function RecentLeadsQuery() {
  const { userBusinessUnitId } = UseUserBusinessUnit();
  const { activeNavbar } = useLayoutVisibility();
  const isSalesAndServiceApp = activeNavbar === "Sales & Service";
  const start = new Date();
  start.setUTCHours(0, 0, 0, 0);
  const end = new Date();
  end.setUTCHours(23, 59, 59, 999);

  const filterNew = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "ProcessStage == New",
  ]
    .filter(Boolean)
    .join(" && ");

  const { data: newData } = useEntityListQuery<
    Schemas["LeadRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Leads",
    params: {
      filter: filterNew,
    },
    queryKey: `leadListHomeNew`,
    enabled: !isSalesAndServiceApp,
  });

  const filterSpaceTour = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "ProcessStage == SpaceTour",
  ]
    .filter(Boolean)
    .join(" && ");

  const { data: spaceTourData } = useEntityListQuery<
    Schemas["LeadRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Leads",
    params: {
      filter: filterSpaceTour,
    },
    queryKey: `leadListHomeSpaceTour`,
    enabled: !isSalesAndServiceApp,
  });

  const filterWonToday = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "ProcessStage == Won",
    `modifiedOn <= ${end.toISOString().replace("Z", "")}`,
    `modifiedOn >= ${start.toISOString().replace("Z", "")}`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data: wonTodayData } = useEntityListQuery<
    Schemas["LeadRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Leads",
    params: {
      filter: filterWonToday,
    },
    queryKey: `leadListHomeWonToday`,
    enabled: !isSalesAndServiceApp,
  });

  const filterLostToday = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "ProcessStage == Lost",
    `modifiedOn <= ${end.toISOString().replace("Z", "")}`,
    `modifiedOn >= ${start.toISOString().replace("Z", "")}`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data: lostTodayData } = useEntityListQuery<
    Schemas["LeadRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Leads",
    params: {
      filter: filterLostToday,
    },
    queryKey: `leadListHomeLostToday`,
    enabled: !isSalesAndServiceApp,
  });

  const recentLeads = readRecentLeads();
  const totalNew = isSalesAndServiceApp ? 0 : newData?.totalCount;
  const totalSpaceTour = isSalesAndServiceApp ? 0 : spaceTourData?.totalCount;
  const totalWonToday = isSalesAndServiceApp ? 0 : wonTodayData?.totalCount;
  const totalLostToday = isSalesAndServiceApp ? 0 : lostTodayData?.totalCount;
  return {
    totalNew,
    totalSpaceTour,
    totalWonToday,
    totalLostToday,
    recentLeads,
  };
}
