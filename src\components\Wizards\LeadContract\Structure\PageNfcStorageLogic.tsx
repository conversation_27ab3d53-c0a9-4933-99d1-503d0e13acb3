import { useState, useEffect } from "react";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Center,
  Flex,
  Text,
  Stack,
  Paper,
  Loader,
  Alert,
  Progress,
  Group,
} from "@mantine/core";
import {
  IconNfc,
  IconCheck,
  IconX,
  IconWifi,
  IconAlertCircle,
  IconRefresh,
} from "@tabler/icons-react";

interface PageNfcStorageLogicProps extends PageProps<PageName> {}

type NfcStep = "connecting" | "scanning" | "reading" | "success" | "error";

export default function PageNfcStorageLogic({
  setPages,
  pages,
}: PageNfcStorageLogicProps) {
  const { t } = useTranslation("features");
  const [currentStep, setCurrentStep] = useState<NfcStep>("connecting");
  const [progress, setProgress] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");
  const [scannedData, setScannedData] = useState<{
    name?: string;
    idNumber?: string;
    dateOfBirth?: string;
  }>({});

  useEffect(() => {
    // Simulate NFC connection process
    const timer = setTimeout(() => {
      switch (currentStep) {
        case "connecting":
          setProgress(25);
          setCurrentStep("scanning");
          break;
        case "scanning":
          setProgress(50);
          setCurrentStep("reading");
          break;
        case "reading":
          setProgress(75);
          // Simulate successful data read
          setScannedData({
            name: "John Doe",
            idNumber: "123456789",
            dateOfBirth: "1990-01-01",
          });
          setCurrentStep("success");
          setProgress(100);
          break;
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [currentStep]);

  const handleRetry = () => {
    setCurrentStep("connecting");
    setProgress(0);
    setErrorMessage("");
    setScannedData({});
  };

  const handleContinue = () => {
    setPages([...pages, "UNIT_SELECTION"]);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "connecting":
        return (
          <Stack align="center" gap="md">
            <IconWifi size={64} color="blue" />
            <Text fz={20} fw={600}>
              {t("Connecting to NFC StorageLogic App")}
            </Text>
            <Text fz={14} c="dimmed" ta="center">
              {t(
                "Please ensure your device is connected and the app is running",
              )}
            </Text>
            <Loader size="lg" />
          </Stack>
        );

      case "scanning":
        return (
          <Stack align="center" gap="md">
            <IconNfc size={64} color="orange" />
            <Text fz={20} fw={600}>
              {t("Ready to Scan")}
            </Text>
            <Text fz={14} c="dimmed" ta="center">
              {t("Please place the ID document on the NFC reader")}
            </Text>
            <Box
              w={200}
              h={200}
              style={{
                border: "3px dashed orange",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                animation: "pulse 2s infinite",
              }}
            >
              <IconNfc size={80} color="orange" />
            </Box>
          </Stack>
        );

      case "reading":
        return (
          <Stack align="center" gap="md">
            <IconNfc size={64} color="green" />
            <Text fz={20} fw={600}>
              {t("Reading Data")}
            </Text>
            <Text fz={14} c="dimmed" ta="center">
              {t("Please keep the document in place while reading")}
            </Text>
            <Loader size="lg" color="green" />
          </Stack>
        );

      case "success":
        return (
          <Stack align="center" gap="md">
            <IconCheck size={64} color="green" />
            <Text fz={20} fw={600} c="green">
              {t("Verification Successful")}
            </Text>
            <Paper p="md" withBorder w="100%" maw={400}>
              <Stack gap="xs">
                <Group justify="space-between">
                  <Text fw={500}>{t("Name")}:</Text>
                  <Text>{scannedData.name}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>{t("ID Number")}:</Text>
                  <Text>{scannedData.idNumber}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>{t("Date of Birth")}:</Text>
                  <Text>{scannedData.dateOfBirth}</Text>
                </Group>
              </Stack>
            </Paper>
          </Stack>
        );

      case "error":
        return (
          <Stack align="center" gap="md">
            <IconX size={64} color="red" />
            <Text fz={20} fw={600} c="red">
              {t("Verification Failed")}
            </Text>
            <Alert
              icon={<IconAlertCircle size="1rem" />}
              title={t("Error")}
              color="red"
              w="100%"
              maw={400}
            >
              {errorMessage || t("Unable to read NFC data. Please try again.")}
            </Alert>
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      <Center mt={40}>
        <Stack w="100%" maw={600} align="center" gap="xl">
          <Text fz={24} fw={700} ta="center">
            {t("NFC StorageLogic App Verification")}
          </Text>

          <Progress
            value={progress}
            size="lg"
            w="100%"
            color={currentStep === "error" ? "red" : "blue"}
          />

          <Paper p="xl" withBorder w="100%" mih={400}>
            <Center h="100%">{renderStepContent()}</Center>
          </Paper>

          <Flex gap="md" w="100%" justify="center">
            {currentStep === "error" && (
              <Button
                variant="outline"
                leftSection={<IconRefresh size="1rem" />}
                onClick={handleRetry}
              >
                {t("Try Again")}
              </Button>
            )}

            {currentStep === "success" && (
              <Button
                leftSection={<IconCheck size="1rem" />}
                onClick={handleContinue}
              >
                {t("Continue")}
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() => setPages([...pages.slice(0, -1)])}
            >
              {t("Back")}
            </Button>
          </Flex>
        </Stack>
      </Center>
    </Box>
  );
}
