import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import {
  WebformEntryForm,
  type FormSchema,
} from "../components/WebformEntryForm";
import { filterFalsyValues } from "@/utils/filters";
import { Loader } from "@mantine/core";
import { useParams } from "react-router-dom";

interface WebformEntryShowProps {
  refreshForm?: () => void;
  closeModal?: () => void;
  webformEntryId?: string | null;
}

export function WebformEntryShow({
  closeModal,
  webformEntryId,
}: WebformEntryShowProps) {
  const { id } = useParams<{ id: string }>();
  const idToUse = webformEntryId ?? id;
  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["WebformEntry"]>({
    resourcePath: "/api/WebformEntries/{id}",
    resourceId: idToUse!,
    queryKey: "webformEntry",
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <WebformEntryForm
      closeModal={closeModal}
      initialValues={filterFalsyValues(data) as FormSchema}
    />
  );
}
