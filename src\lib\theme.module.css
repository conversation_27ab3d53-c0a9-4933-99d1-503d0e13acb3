@media (width >= 1200px) {
  .fieldsetRoot {
    border: none;
    background-color: transparent;
    padding: 0;
    margin: 0;
  }

  .inputInput {
  }

  .inputWrapper {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .textInputRoot {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .textInputLabel {
    flex: 2.4;
    max-height: 32px !important;
    min-height: 32px !important;
    display: flex;
    align-items: center;
  }

  .textInputInput {
    width: 100%;
    max-height: 32px !important;
    min-height: 32px !important;
  }

  .textInputWrapper {
    flex: 6;
    max-height: 32px !important;
    min-height: 32px !important;
  }

  .colorInputRoot {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .colorInputLabel {
    flex: 2.4;
    max-height: 32px !important;
    min-height: 32px !important;
    display: flex;
    align-items: center;
  }

  .colorInputInput {
    width: 100%;
  }

  .colorInputWrapper {
    flex: 6;
  }

  .textInputControls {
    height: 80% !important;
    display: flex;
    flex-direction: column;
    display: flex;
    align-items: center;
    text-align: center;
  }

  .textInputControl {
    margin: 0;
    padding: 0;
    height: 40% !important;
  }

  .textAreaRoot {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .textAreaInput {
  }

  .textAreaError {
    width: 100%;
    margin-top: 10px;
    text-align: right;
  }

  .textInputError {
    width: 100%;
    margin-top: 10px;
    text-align: right;
  }

  .textAreaLabel {
    flex: 2.4;
    max-height: 32px !important;
    min-height: 32px !important;
    display: flex;
    align-items: center;
  }

  .textAreaWrapper {
    flex: 6;
  }
}

@media (width < 1200px) {
  .fieldsetRoot {
    border: none;
    background-color: transparent;
    padding: 0;
    margin: 0;
  }
}
