import {
  getLeadFormSchema,
  LeadFormProvider,
  type LeadFormSchema,
  useLeadForm,
} from "@/features/leads/providers/form";
import { zodResolver } from "mantine-form-zod-resolver";

import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { LeadSource } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import {
  Text,
  Select,
  TextInput,
  Box,
  Center,
  Grid,
  Button,
  Paper,
} from "@mantine/core";

import { useTranslation } from "react-i18next";
import { IconUserPlus } from "@tabler/icons-react";
import { type Schemas } from "@/types";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { filterFalsyValues } from "@/utils/filters";
import { useState } from "react";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { useNavigate } from "react-router-dom";

interface PageLeadQuickForm {
  lead?: Partial<LeadFormSchema>;
  closeModal?: () => void;
}
export default function PageLeadQuickForm({
  lead,
  closeModal,
}: PageLeadQuickForm) {
  const { t } = useTranslation("features");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleCloseModal = () => {
    if (closeModal) {
      closeModal?.();
    }
  };

  const form = useLeadForm({
    initialValues: {
      firstName: lead?.firstName ?? "",
      lastName: lead?.lastName ?? "",
      email: lead?.email ?? "",
      mobile: lead?.mobile ?? "",
      salutation: lead?.salutation ?? "MrMrs",
      phone: lead?.phone ?? "",
      companyName: lead?.companyName ?? "",
      webformTitle: lead?.webformTitle ?? "",
      webformDetails: lead?.webformDetails ?? "",
      discountText: lead?.discountText ?? "",
      storageValue: lead?.storageValue ?? null,
      spaceTourRemarks: lead?.spaceTourRemarks ?? "",
      callCount: lead?.callCount ?? 0,
      callCountReached: lead?.callCountReached ?? 0,
      callCountNotReached: lead?.callCountNotReached ?? 0,
      quotationEmailSent: lead?.quotationEmailSent ?? false,
      moveInDate: new Date(lead?.moveInDate ?? new Date()),
      nextCallback: null,
      reservationStart: null,
      reservedUntil: null,
      rentAsBusiness: lead?.rentAsBusiness ?? null,
      transportation: lead?.transportation ?? null,
      discount: lead?.discount ?? null,
      storageDuration: lead?.storageDuration ?? null,
      storageUnitReason: lead?.storageUnitReason ?? null,
      processStage: lead?.processStage ?? "New",
      optOutPhone: lead?.optOutPhone ?? false,
      description: lead?.description ?? "",
      type: lead?.type ?? "Private",
      startWithin: lead?.startWithin ?? "Later",
      productType: lead?.productType ?? "StorageSpace",
      leadSource: lead?.leadSource ?? "",
      sizeOfUnit: lead?.sizeOfUnit ?? null,
      businessUnitId: lead?.businessUnitId ?? "",
      businessUnit: lead?.businessUnit ?? null,
      existingContactId: lead?.existingContactId ?? "",
      existingContact: lead?.existingContact ?? null,
      existingCustomerId: lead?.existingCustomerId ?? "",
      existingCustomer: lead?.existingCustomer ?? null,
      contractId: lead?.contractId ?? "",
      contract: lead?.contract ?? null,
      recordState: lead?.recordState ?? "",
      comments: lead?.comments ?? "",
      promotionCode: lead?.promotionCode ?? "",
      fax: lead?.fax ?? "",
      city: lead?.city ?? "",
      zip: lead?.zip ?? "",
      street: lead?.street ?? "",
      countryId: lead?.countryId ?? "",
      country: lead?.country ?? null,
      approvalForAddressUsage: lead?.approvalForAddressUsage ?? false,
      storageTypeId: lead?.storageTypeId ?? "",
      storageType: lead?.storageType ?? null,
      preferredLanguage: lead?.preferredLanguage ?? "Dutch",
      optInType: lead?.optInType ?? "OptIn",
      step: lead?.step ?? "",
      price: lead?.price ?? "",
      unitSize: lead?.unitSize ?? "",
      volume: lead?.volume ?? null,
      amount: lead?.amount ?? null,
      lossReasonId: lead?.lossReasonId ?? "",
      lossReason: lead?.lossReason ?? null,
      makeAnAppointment: null,
    },
    validate: zodResolver(getLeadFormSchema(t)),
  });

  const { mutate } = useEntityCreateMutation<
    Schemas["Lead"],
    Schemas["LeadCreateDto"]
  >({ resourcePath: "/api/Leads", queryKey: "lead" });

  const formValues = form.getValues();
  const navigate = useNavigate();

  return (
    <LeadFormProvider form={form}>
      <form
        onSubmit={form.onSubmit((fields) => {
          setIsLoading(true);

          const filteredFields = getDirtyFormFields(fields, true, form.isDirty);
          const filteredValues = filterFalsyValues(filteredFields);

          mutate(filteredValues, {
            onSuccess: (data) => {
              navigate("/app/leads/" + data.data.id + "/general");
              setIsLoading(false);
              handleCloseModal();
            },
            onError: (error) => {
              setIsLoading(false);
              console.error(error);
            },
          });
          form.resetDirty();
        })}
      >
        <Center>
          <Text fz={16} fw={600} c={"#282828"}>
            {t("wizards.QuickAddLead.Title")}
          </Text>
        </Center>
        <Center>
          <Text fz={10} fw={300} c={"#ADADAD"}>
            {t("wizards.QuickAddLead.Label")}
          </Text>
        </Center>
        <Box p={16} mr={"14%"} ml={"14%"}>
          <Grid mt="lg">
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper p="lg" h="100%">
                <FieldValidation isDirty={form.isDirty("firstName")}>
                  <TextInput
                    required={!form.values.companyName}
                    label={t("leads.firstName")}
                    {...form.getInputProps("firstName")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("lastName")}>
                  <TextInput
                    required
                    label={t("leads.lastName")}
                    {...form.getInputProps("lastName")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("email")}>
                  <TextInput
                    required
                    label={t("leads.email")}
                    {...form.getInputProps("email")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("mobile")}>
                  <TextInput
                    required
                    label={t("leads.mobile")}
                    {...form.getInputProps("mobile")}
                  />
                </FieldValidation>
              </Paper>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper p="lg" h="100%">
                <FieldValidation isDirty={form.isDirty("leadSource")}>
                  <Select
                    required
                    searchable
                    label={t("leads.leadSource")}
                    data={LeadSource.map((value) => ({
                      value,
                      label: t(getEnumTransKey("leads", value)),
                    }))}
                    clearable
                    {...form.getInputProps("leadSource")}
                  />
                </FieldValidation>

                <FieldValidation isDirty={form.isDirty("businessUnitId")}>
                  <BusinessUnitLookup
                    required
                    initial={formValues?.businessUnit}
                    initialId={formValues?.businessUnitId}
                    identifier="businessUnitIdLeadQuick"
                    label={t("leads.businessUnit")}
                    {...form.getInputProps("businessUnitId")}
                  />
                </FieldValidation>
              </Paper>
            </Grid.Col>
          </Grid>
        </Box>
        <Center mt={24}>
          <Button
            type="submit"
            loading={isLoading}
            leftSection={<IconUserPlus width={20} height={20} />}
            fz={14}
            fw={400}
          >
            {t("wizards.QuickAddLead.ConfirmButton")}
          </Button>
        </Center>
      </form>
    </LeadFormProvider>
  );
}
