import { type ViewOption } from "@/components/Table/types";
import { type LeadFormSchema } from "@/features/leads/providers/form";
import {
  type MRT_ColumnFiltersState,
  type MRT_SortingState,
} from "mantine-react-table";
import { useSearchParams } from "react-router-dom";

export function GetAllSearchParams() {
  const [searchParams] = useSearchParams();
  const queryParams = Object.fromEntries(
    searchParams.entries(),
  ) as Partial<LeadFormSchema>;

  const capitalizeFirstLetter = (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1);

  const formattedQueryParams = Object.fromEntries(
    Object.entries(queryParams).map(([key, value]) => [
      key,
      typeof value === "string" ? capitalizeFirstLetter(value) : value,
    ]),
  );

  return formattedQueryParams;
}

export const GetParamsFromURL = (
  searchParams: URLSearchParams,
  viewOptions: ViewOption[],
  defaultViewOption?: ViewOption,
  initialSorting: MRT_SortingState = [],
) => {
  try {
    const activeViewValue = searchParams.get("activeView");
    const activeView =
      viewOptions?.find((v) => v.value === activeViewValue) ??
      defaultViewOption;

    let columnFilters = JSON.parse(
      searchParams.get("filters") || "[]",
    ) as MRT_ColumnFiltersState;
    let sorting = JSON.parse(
      searchParams.get("sorting") || "[]",
    ) as MRT_SortingState;

    if ((!columnFilters || columnFilters.length === 0) && activeView?.filter) {
      columnFilters = activeView.filter;
    }

    if (!sorting?.length) {
      sorting = activeView?.sorting?.length
        ? activeView.sorting
        : initialSorting?.length
          ? initialSorting
          : [];
    }

    return { columnFilters, sorting, activeView };
  } catch (error) {
    console.error("Error parsing filters from URL:", error);
    return {
      columnFilters: [],
      sorting: [],
      activeView: defaultViewOption,
    };
  }
};
