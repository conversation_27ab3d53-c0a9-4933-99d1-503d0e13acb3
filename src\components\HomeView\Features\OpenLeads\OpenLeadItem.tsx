import { type Schemas } from "@/types";
import { lowerCaseNthLetter } from "@/utils/filters";
import { Box, Flex, Badge, Text, SimpleGrid, Stack } from "@mantine/core";
import { IconCaretUpFilled, IconDots } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import classes from "../..//Home.module.css";
import { getEnumTransKey } from "@/utils/trans";

export default function OpenLeadItem({
  lead,
}: {
  lead: Schemas["LeadRetrieveDto"];
}) {
  const { t } = useTranslation("features");
  const navigate = useNavigate();
  const processStage = lead?.processStage ?? "";
  return (
    <Box
      className={classes.listItem}
      onClick={() => {
        navigate(`/app/leads/${lead?.id}`);
      }}
    >
      <Flex justify="space-between" align="center" direction="row" mb={4}>
        <Flex justify="flex-start" align="center" direction="row" gap={8}>
          <Text className={classes.listItemTitle}>{lead.fullName}</Text>
          <Badge leftSection={<IconCaretUpFilled size={12} />}>
            {lead?.priority}
          </Badge>
          <Badge variant="light">
            {t(`leads.${lowerCaseNthLetter(processStage)}`)}
          </Badge>
        </Flex>
        <IconDots width={20} height={20} style={{ display: "none" }} />
      </Flex>
      <SimpleGrid cols={{ xs: 2, sm: 2, md: 3, lg: 6, xl: 6 }} spacing={8}>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>
            {t(`leads.nextCallback`)}
          </Text>
          <Text className={classes.listItemText}>
            {lead?.nextCallback
              ? new Date(lead?.nextCallback).toLocaleString()
              : ""}
          </Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>{t(`leads.mobile`)}</Text>
          <Text className={classes.listItemText}>{lead?.mobile ?? ""}</Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>{t(`leads.email`)}</Text>
          <Text className={classes.listItemText}>{lead?.email ?? ""}</Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>
            {t(`leads.businessUnit`)}
          </Text>
          <Text className={classes.listItemText}>
            {lead?.businessUnit?.code ?? ""}
          </Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>{t(`leads.leadSource`)}</Text>
          <Text className={classes.listItemText}>
            {t(getEnumTransKey("leads", lead?.leadSource ?? ""))}
          </Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>
            {t(`leads.webformTitle`)}
          </Text>
          <Text className={classes.listItemText}>
            {lead?.webformTitle ?? ""}
          </Text>
        </Stack>
      </SimpleGrid>
    </Box>
  );
}
