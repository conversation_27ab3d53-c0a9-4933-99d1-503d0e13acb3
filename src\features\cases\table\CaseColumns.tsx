import {
  Date<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { recordState } from "@/features/entity/utils";
import { CaseStatus } from "@/types/enums";
import { type ComboboxData } from "@mantine/core";
import { lowerCaseNthLetter } from "@/utils/filters";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function CaseColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "number",
      header: t("cases.number"),
      filterVariant: "text",
      Cell: (props) => EntityLink<PERSON>enderer(props, "cases"),
    },
    {
      accessorKey: "subject",
      header: t("cases.subject"),
      filterVariant: "text",
      minSize: 320,
    },
    {
      accessorKey: "contact",
      header: t("cases.contact"),
      ...TableRenderer(ContactLookup, "contacts", ["fullName"]),
    },
    {
      accessorKey: "senderEmail",
      header: t("cases.senderEmail"),
      filterVariant: "text",
    },
    {
      accessorKey: "status",
      header: t("cases.status"),
      filterVariant: "select",
      Cell: ({ cell }) =>
        t("cases." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: CaseStatus as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "firstContactMailbox",
      header: t("cases.caseOwner"),
      filterVariant: "text",
    },
    {
      accessorKey: "businessUnit",
      header: t("cases.businessUnit"),
      ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
    },
    {
      accessorKey: "recordState",
      header: t("cases.recordState"),
      filterVariant: "select",
      Cell: ({ cell }) =>
        t("cases." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: recordState as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "modifiedOn",
      header: t("entity.modifiedOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "originalBusinessUnitId",
      header: t("cases.originalBusinessUnitId"),
      filterVariant: "text",
    },
  ];
  return columns;
}
