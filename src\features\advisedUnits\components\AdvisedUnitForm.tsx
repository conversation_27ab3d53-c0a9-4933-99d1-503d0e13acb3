import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, NumberInput, Paper, Select } from "@mantine/core";
import { useEffect, useRef, type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { AdvisedUnitType, ReservationStatus } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { type Schemas } from "@/types";
import { getEntity } from "@/features/entity/api";
import { type TFunction } from "i18next";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { UnitLookup } from "@/components/Lookup/Features/Units/UnitLookupField";
import { QuoteLookup } from "@/components/Lookup/Features/Quotes/QuoteLookupField";
import { ReservationLookup } from "@/components/Lookup/Features/Reservations/ReservationLookup";

const baseFormSchema = z.object({
  pricePerMonth: z.coerce.number().nullable(),
  pricePerWeek: z.coerce.number().nullable(),
  minPrice: z.coerce.number().nullable(),
  maxPrice: z.coerce.number().nullable(),
  leadId: z.string().nullable(),
  lead: z.object({}).nullable(),
  businessUnitId: z.string().nullable(),
  businessUnit: z.object({}).nullable(),
  unitId: z.string().nullable(),
  unit: z.object({}).nullable(),
  reservationId: z.string().nullable(),
  reservation: z.object({}).nullable(),
  quoteId: z.string().nullable(),
  quote: z.object({}).nullable(),
  type: z.enum(AdvisedUnitType as [string]).nullable(),
  reservationStatus: z.enum(ReservationStatus as [string]),
});

const createFormSchema = (t: TFunction<"features", undefined>) =>
  baseFormSchema.refine(
    (data) => {
      const { pricePerMonth, minPrice, maxPrice } = data;

      if (minPrice == 0 && maxPrice == 0) {
        return true;
      }
      // Only validate if minPrice and maxPrice are not null
      if (minPrice !== null && maxPrice !== null && pricePerMonth !== null) {
        return pricePerMonth >= minPrice && pricePerMonth <= maxPrice;
      }

      return true; // Valid if minPrice or maxPrice are null
    },
    {
      path: ["pricePerMonth"],
      message: t("advisedUnits.priceWarning"),
    },
  );

export type FormSchema = z.infer<typeof baseFormSchema>;

interface AdvisedUnitFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function AdvisedUnitForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: AdvisedUnitFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const formSchema = createFormSchema(t);

  const form = useForm<FormSchema>({
    initialValues: {
      pricePerMonth: initialValues?.pricePerMonth ?? null,
      pricePerWeek: initialValues?.pricePerWeek ?? null,
      minPrice: initialValues?.minPrice ?? null,
      maxPrice: initialValues?.maxPrice ?? null,
      leadId: initialValues?.leadId ?? "",
      lead: initialValues?.lead ?? null,
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      unitId: initialValues?.unitId ?? "",
      unit: initialValues?.unit ?? null,
      reservationId: initialValues?.reservationId ?? "",
      reservation: initialValues?.reservation ?? null,
      quoteId: initialValues?.quoteId ?? "",
      quote: initialValues?.quote ?? null,
      type: initialValues?.type ?? null,
      reservationStatus: initialValues?.reservationStatus ?? "Active",
    },
    validate: zodResolver(formSchema),
  });

  const { unitId, businessUnitId } = form.values;

  // Create a ref to store setFieldValue so it doesn't change
  const setFieldValueRef = useRef(form.setFieldValue);
  const previousUnitIdRef = useRef<string | null>(unitId); // Ref to store previous unitId
  const previousBusinessUnitIdRef = useRef<string | null>(businessUnitId); // Ref to store previous businessUnitId

  useEffect(() => {
    // Trigger effect only if businessUnitId has changed
    if (businessUnitId !== previousBusinessUnitIdRef.current) {
      setFieldValueRef.current("unitId", "");
      setFieldValueRef.current("unit", null);
    }

    // Update previous businessUnitId
    previousBusinessUnitIdRef.current = businessUnitId;
  }, [businessUnitId]);

  useEffect(() => {
    const fetchUnitPrices = async (unitId: string) => {
      const unit = await getEntity<Schemas["UnitRetrieveDto"]>({
        resourcePath: "/api/Units/{id}",
        resourceId: unitId,
      });

      setFieldValueRef.current("pricePerMonth", unit.pricePerMonth ?? null);
      setFieldValueRef.current(
        "pricePerWeek",
        unit.pricePerMonth ? (unit.pricePerMonth * 12) / 52 : null,
      );
      setFieldValueRef.current("minPrice", unit.minPrice ?? null);
      setFieldValueRef.current("maxPrice", unit.maxPrice ?? null);
    };

    // Trigger effect only if unitId has changed
    if (unitId && unitId !== previousUnitIdRef.current) {
      void fetchUnitPrices(unitId);
    } else if (!unitId) {
      setFieldValueRef.current("pricePerMonth", null);
      setFieldValueRef.current("pricePerWeek", null);
      setFieldValueRef.current("minPrice", null);
      setFieldValueRef.current("maxPrice", null);
    }

    // Update previous unitId
    previousUnitIdRef.current = unitId;
  }, [unitId]); // Dependency array includes unitId

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
        disabled={form.getValues().quote !== null}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <BusinessUnitLookup
                label={t("advisedUnits.businessUnit")}
                initial={form.getValues().businessUnit}
                initialId={form.getValues().businessUnitId}
                identifier="businessUnitIdAdvisedUnit"
                {...form.getInputProps("businessUnitId")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <UnitLookup
                businessUnitId={form.getValues().businessUnitId ?? ""}
                label={t("advisedUnits.unit")}
                initial={form.getValues().unit}
                initialId={form.getValues().unitId}
                identifier="unitIdAdvisedUnit"
                {...form.getInputProps("unitId")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <Select
                searchable
                label={t("advisedUnits.type")}
                data={AdvisedUnitType.map((value) => ({
                  value,
                  label: t(getEnumTransKey("advisedUnits", value)),
                }))}
                clearable
                {...form.getInputProps("type")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <NumberInput
                label={t("advisedUnits.pricePerMonth")}
                disabled={!form.values.maxPrice && !form.values.minPrice}
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("pricePerMonth")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <NumberInput
                label={t("advisedUnits.pricePerWeek")}
                disabled
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("pricePerWeek")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <NumberInput
                label={t("advisedUnits.deposit")}
                disabled
                leftSection={config.CURRENCY.symbol}
                value={(form.getValues().pricePerMonth ?? 0) * 2}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <NumberInput
                label={t("advisedUnits.minPrice")}
                disabled
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("minPrice")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <NumberInput
                label={t("advisedUnits.maxPrice")}
                disabled
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("maxPrice")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <LeadLookup
                disabled
                label={t("advisedUnits.lead")}
                initial={form.getValues().lead}
                initialId={form.getValues().leadId}
                identifier="leadIdAdvisedUnit"
                {...form.getInputProps("leadId")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <QuoteLookup
                disabled
                label={t("advisedUnits.quote")}
                initial={form.getValues().quote}
                initialId={form.getValues().quoteId}
                identifier="quoteIdAdvisedUnit"
                {...form.getInputProps("quoteId")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <ReservationLookup
                label={t("advisedUnits.reservation")}
                disabled
                initial={form.getValues().reservation}
                initialId={form.getValues().reservationId}
                identifier="reservationIdAdvisedUnit"
                {...form.getInputProps("reservationId")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
              <Select
                searchable
                disabled
                label={t("leads.reservationStatus")}
                data={ReservationStatus}
                {...form.getInputProps("reservationStatus")}
                {...{ labelProps: { style: { flex: "1.5" } } }}
              />
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
