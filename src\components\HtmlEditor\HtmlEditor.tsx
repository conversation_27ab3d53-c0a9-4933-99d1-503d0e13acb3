import {
  useEffect,
  useRef,
  useCallback,
  useImperativeHandle,
  forwardRef,
} from "react";
import "./HtmlEditor.css";

interface EmailEditorProps {
  html: string;
  setGetFullHtmlRef?: (getFullHtml: () => string) => void;
}

export interface HtmlEditorRef {
  getFullHtml: () => string;
}

const HtmlEditor = forwardRef<HtmlEditorRef, EmailEditorProps>(
  ({ html, setGetFullHtmlRef }, ref) => {
    const fullHtmlRef = useRef<HTMLDivElement>(null);
    const editableRef = useRef<HTMLElement | null>(null);

    const getFullHtml = useCallback(() => {
      return fullHtmlRef.current?.innerHTML || "";
    }, []);

    useImperativeHandle(ref, () => ({
      getFullHtml,
    }));

    useEffect(() => {
      if (setGetFullHtmlRef) {
        setGetFullHtmlRef(getFullHtml);
      }
    }, [setGetFullHtmlRef, getFullHtml]);

    useEffect(() => {
      initializeContent();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [html]);

    const initializeContent = () => {
      if (fullHtmlRef.current) {
        fullHtmlRef.current.innerHTML = html;
        makeEditableArea();
      }
    };

    const makeEditableArea = useCallback(() => {
      if (fullHtmlRef.current) {
        const userContextElement =
          fullHtmlRef.current.querySelector(".user-context");
        if (userContextElement instanceof HTMLElement) {
          userContextElement.contentEditable = "true";
          userContextElement.classList.add("editable-area");
          editableRef.current = userContextElement;
        }
      }
    }, []);

    return (
      <div className="email-show-container">
        <div className="editor-pane">
          <div ref={fullHtmlRef} className="full-html-content" />
        </div>
      </div>
    );
  },
);

HtmlEditor.displayName = "HtmlEditor";
export default HtmlEditor;
