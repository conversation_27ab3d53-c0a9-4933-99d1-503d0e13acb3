import { Route, Routes } from "react-router-dom";
import { ContractList } from "./ContractList";
import { ContractShow } from "./ContractShow";
import { ContractCreate } from "./ContractCreate";

export default function ContractsRoutes() {
  return (
    <Routes>
      <Route index element={<ContractList />} />
      <Route path=":id" element={<ContractShow />}>
        <Route path=":tabValue" element={<ContractShow />} />
      </Route>
      <Route path="create" element={<ContractCreate />} />
    </Routes>
  );
}
