import { Box, Flex, Group, Progress, Text, Tooltip } from "@mantine/core";
import { type PossibleTabs } from "../../Home";
import { type TabProps } from "../../Structure/HomeTabs";

import classes from "../..//Home.module.css";
import RecentLeadsQuery from "./RecentLeadsQuery";
import NumberLoader from "../../Components/numberLoader";
import { useLayoutVisibility } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import { useTranslation } from "react-i18next";

const TAB: PossibleTabs = "RecentLeads";

export default function RecentLeadsTab({ setActiveTab, isActive }: TabProps) {
  const { t } = useTranslation("features");

  const { activeNavbar } = useLayoutVisibility();
  const isSalesAndServiceApp = activeNavbar === "Sales & Service";
  const { totalNew, totalSpaceTour, totalWonToday, totalLostToday } =
    RecentLeadsQuery();
  let wonPercentage = 50;
  let lostPercentage = 50;
  if (totalWonToday != undefined && totalLostToday != undefined) {
    const total = totalWonToday + totalLostToday;
    if (totalWonToday == 0 && totalLostToday != 0) {
      lostPercentage = 100;
      wonPercentage = 0;
    } else if (totalLostToday == 0 && totalWonToday != 0) {
      wonPercentage = 100;
      lostPercentage = 0;
    } else if (totalWonToday == 0 && totalLostToday == 0) {
      wonPercentage = 50;
      lostPercentage = 50;
    } else {
      wonPercentage = (totalWonToday / total) * 100;
      lostPercentage = (totalLostToday / total) * 100;
    }
  }

  if (isSalesAndServiceApp) {
    return (
      <Box
        w={{ base: "100%", xs: "100%", md: "32%", lg: "16%", xl: "16%" }}
        className={isActive ? classes.activeHomeTab : classes.homeTab}
        onClick={() => setActiveTab(TAB)}
      >
        <Flex
          justify="center"
          align="center"
          direction="row"
          wrap="wrap"
          h={"100%"}
        >
          <Text className={classes.tabBoldText}>
            {t(`leads.recentlyViewedLeads`)}
          </Text>
          <Text fz={{ xs: 16, sm: 12, md: 14, lg: 16, xl: 18 }} fw={700}></Text>
        </Flex>
      </Box>
    );
  }

  return (
    <Box
      w={{ base: "100%", xs: "100%", md: "32%", lg: "16%", xl: "16%" }}
      className={isActive ? classes.activeHomeTab : classes.homeTab}
      onClick={() => setActiveTab(TAB)}
    >
      <Flex justify="center" align="center" direction="row" wrap="wrap">
        <Group justify="space-between" gap={4} w={"100%"}>
          <Text w={"100%"} className={classes.tabTitleText}>
            {t(`leads.recentlyViewedLeads`)}
          </Text>
          <Text fz={{ xs: 16, sm: 12, md: 14, lg: 16, xl: 18 }} fw={700}></Text>
        </Group>

        <Group justify="space-between" gap={4} w={"100%"} mt={8}>
          <Text className={classes.tabFooterText}>{t(`leads.newLeads`)}</Text>
          <Text className={classes.tabFooterBoldText}>
            <NumberLoader number={totalNew} />
          </Text>
        </Group>
        <Group justify="space-between" gap={4} w={"100%"} mt={8}>
          <Text className={classes.tabFooterText}>
            {t(`leads.spaceTourLeads`)}
          </Text>
          <Text className={classes.tabFooterBoldText}>
            <NumberLoader number={totalSpaceTour} />
          </Text>
        </Group>
        <Progress.Root w={"100%"} mt={32} radius="xl" size={8}>
          <Tooltip label={`${totalWonToday ?? 0} ${t(`leads.wonLeadsToday`)}`}>
            <Progress.Section value={wonPercentage}></Progress.Section>
          </Tooltip>

          <Tooltip
            label={`${totalLostToday ?? 0} ${t(`leads.lostLeadsToday`)}`}
          >
            <Progress.Section
              value={lostPercentage}
              color="neutral.2"
            ></Progress.Section>
          </Tooltip>
        </Progress.Root>
      </Flex>
    </Box>
  );
}
