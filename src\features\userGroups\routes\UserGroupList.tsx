import { EntityLayout } from "@/features/entity";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import { type PathKeys, type Schemas } from "@/types";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";

const PATH = "UserGroups";

export function UserGroupListInner({
  resourcePath,
  createPath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["UserGroupRetrieveDto"],
        Schemas["UserGroupRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="userGroup"
        entityPath="userGroups"
        title={t("userGroups.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "name",
            header: t("tags.name"),
            filterVariant: "text",
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: false,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function UserGroupList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <UserGroupListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
