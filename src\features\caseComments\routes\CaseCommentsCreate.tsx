import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { CaseCommentsForm } from "../components/CaseCommentsForm";
import { EntityLayout } from "@/features/entity";
import { Group, Loader } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";

interface CaseCommentsCreateProps {
  parentEntityId?: string;
  redirectTo?: string;
  usingModal?: boolean;
  closeModal?: () => void;
}

export function CaseCommentsCreate({
  parentEntityId: propParentEntityId,
  redirectTo: propRedirectTo,
  usingModal: propUsingModal,
  closeModal, // Add closeModal to props
}: CaseCommentsCreateProps) {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["CaseComment"],
    Schemas["CaseCommentCreateDto"]
  >({ resourcePath: "/api/CaseComments", queryKey: "caseComment" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const caseId = propParentEntityId ?? searchParams.get("caseId");
  const redirectTo =
    propRedirectTo ?? searchParams.get("redirectTo") ?? "/app/CaseComments";
  const usingModal = propUsingModal ?? false;

  const { data, isLoading, isFetching } = useEntityQuery<Schemas["Case"]>({
    resourcePath: "/api/Cases/{id}",
    resourceId: caseId!,
    queryKey: ["case", caseId],
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <CaseCommentsForm
      initialValues={{
        caseId: caseId ?? "",
        case: data,
      }}
      isCreate={true}
      title={t("caseComments.createTitle")}
      actionSection={
        <Group>
          {!usingModal && <EntityLayout.SaveButton setClose={setClose} />}
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (usingModal) {
              if (close) {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
                closeModal?.();
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("notifications.createSuccessMessage"),
                });
              }
            } else {
              if (close) {
                navigate(redirectTo);
              } else {
                let navigateTo = `/app/caseComments/${data.data.id}`;
                navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
                navigate(navigateTo);
              }
            }
          },
        });
      }}
    />
  );
}
