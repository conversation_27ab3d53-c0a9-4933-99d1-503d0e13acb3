import { EntityLayout } from "@/features/entity";
import { useNavigate, useParams } from "react-router-dom";
import { type ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { zodResolver } from "mantine-form-zod-resolver";
import { Tabs } from "@mantine/core";
import "@mantine/tiptap/styles.css";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import {
  type CaseFormSchema,
  useCaseForm,
  caseFormSchema,
  CaseFormProvider,
} from "../providers/form";
import { GeneralTab } from "./Tabs/GeneralTab";
import { HistoryTab } from "./Tabs/HistoryTab";

interface CaseFormProps {
  onSubmit: (values: Partial<CaseFormSchema>) => void;
  actionSection?: ReactNode;
  disabledActionSection?: ReactNode;
  headerSection?: ReactNode;
  initialValues?: Partial<CaseFormSchema>;
  title: string;
  recordState?: string;
  isCreate: boolean;
  caseId?: string;
  disabled?: boolean;
}

export function CaseForm({
  onSubmit,
  actionSection = null,
  disabledActionSection = null,
  headerSection = null,
  initialValues,
  title,
  recordState,
  isCreate,
  caseId,
  disabled,
}: CaseFormProps) {
  const { id, tabValue } = useParams();
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const navigate = useNavigate();
  const { t } = useTranslation("features");
  const form = useCaseForm({
    initialValues: {
      number: initialValues?.number ?? "",
      subject: initialValues?.subject ?? "",
      name: initialValues?.name ?? "",
      senderEmail: initialValues?.senderEmail ?? "",
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      originalBusinessUnitId: initialValues?.originalBusinessUnitId ?? "",
      originalBusinessUnit: initialValues?.originalBusinessUnit ?? null,
      caseReasonId: initialValues?.caseReasonId ?? "",
      caseReason: initialValues?.caseReason ?? null,
      originId: initialValues?.originId ?? "",
      origin: initialValues?.origin ?? null,
      parentCaseId: initialValues?.parentCaseId ?? "",
      parentCase: initialValues?.parentCase ?? null,
      complaintReasonId: initialValues?.complaintReasonId ?? "",
      complaintReason: initialValues?.complaintReason ?? null,
      description: initialValues?.description ?? "",
      contactId: initialValues?.contactId ?? "",
      contact: initialValues?.contact ?? null,
      status: initialValues?.status ?? "New",
    },
    validate: zodResolver(caseFormSchema),
  });

  const handleTabChange = (value: string | null) =>
    navigate(`/app/cases/${id}/${value}`);

  return (
    <CaseFormProvider form={form}>
      <form
        onSubmit={form.onSubmit((fields) => {
          const filteredFields = getDirtyFormFields(
            fields,
            isCreate,
            form.isDirty,
          );
          debouncedOnSubmit(filteredFields);
          form.resetDirty();
        })}
      >
        <EntityLayout
          title={title}
          recordState={recordState}
          actionSection={actionSection}
          disabledActionSection={disabledActionSection}
          headerSection={headerSection}
        />
      </form>
      <Tabs
        value={tabValue ?? "general"}
        defaultValue="general"
        onChange={handleTabChange}
      >
        <Tabs.List style={{ pointerEvents: "auto", fontWeight: "bold" }}>
          <Tabs.Tab value="general">{t("cases.general")}</Tabs.Tab>
          <Tabs.Tab value="history" disabled={!caseId}>
            {t("cases.history")}
          </Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="general">
          {(tabValue === "general" || tabValue === undefined) && (
            <GeneralTab
              caseId={caseId}
              isCreate={isCreate}
              disabled={disabled}
            />
          )}
        </Tabs.Panel>
        <Tabs.Panel value="history">
          {tabValue === "history" && <HistoryTab caseId={caseId} />}
        </Tabs.Panel>
      </Tabs>
    </CaseFormProvider>
  );
}
