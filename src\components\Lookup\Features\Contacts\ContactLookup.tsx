import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { SingleLookup } from "../../SingleLookup/SingleLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { t } from "i18next";

const ENTITY = "contacts";
type ENTITY_TYPE = Schemas["ContactRetrieveDto"];

type ContactLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function ContactLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  ...props
}: ContactLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.fullName,
    initialId,
    initial,
    true,
  );
  const queryParams: QueryParams = {
    pageSize: 50,
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["ContactRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/Contacts",
      queryKey: "lead",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.firstName")}</Box>
      <Box>{t("common:lookup.lastName")}</Box>
      <Box>{t("common:lookup.email")}</Box>
      <Box>{t("common:lookup.mobile")}</Box>
      <Box>{t("common:lookup.number")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.firstName, searchTerm, `firstName_${entity.id}`),
        OptionRender(entity.lastName, searchTerm, `lastName_${entity.id}`),
        OptionRender(entity.email, searchTerm, `email_${entity.id}`),
        OptionRender(entity.mobile, searchTerm, `mobile_${entity.id}`),
        OptionRender(entity.number, searchTerm, `number_${entity.id}`),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.fullName!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
