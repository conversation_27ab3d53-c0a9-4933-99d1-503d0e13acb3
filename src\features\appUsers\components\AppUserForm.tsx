import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { Grid, Paper, Select, Tabs, TextInput } from "@mantine/core";
import { zodResolver } from "mantine-form-zod-resolver";
import { timeZoneEnum, timeZoneEnumStrings, languageEnum } from "../utils";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import validator from "validator";
import { AppUserRoles } from "../components/AppUserRoles";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { getEnumTransKey } from "@/utils/trans";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { UserGroupLookup } from "@/components/Lookup/Features/UserGroups/UserGroupLookup";
import { AppLookup } from "@/components/Lookup/Features/Apps/AppLookup";

const formSchema = z.object({
  id: z.string(),
  email: z.string().refine(validator.isEmail),
  name: z.string(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  department: z.string().nullable(),
  division: z.string().nullable(),
  jobTitle: z.string().nullable(),
  timeZone: z.enum(timeZoneEnumStrings as [string]).nullable(),
  language: z.enum(languageEnum as [string]).nullable(),
  currentAppId: z.string().nullable(),
  currentApp: z.object({}).nullable(),
  businessUnitId: z.string().nullable(),
  businessUnit: z.object({}).nullable(),
  userGroupId: z.string().nullable(),
  userGroup: z.object({}).nullable(),
  auth0UserId: z.string(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface AppUserFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  headerSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  appUserId?: string;
  isCreate: boolean;
}

export function AppUserForm({
  onSubmit,
  initialValues,
  actionSection = null,
  headerSection = null,
  title,
  isCreate,
}: AppUserFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      email: initialValues?.email ?? "",
      name: initialValues?.name ?? "",
      firstName: initialValues?.firstName ?? "",
      lastName: initialValues?.lastName ?? "",
      department: initialValues?.department ?? "",
      division: initialValues?.division ?? "",
      jobTitle: initialValues?.jobTitle ?? "",
      language: initialValues?.language ?? "English",
      timeZone: initialValues?.timeZone ?? null,
      currentAppId: initialValues?.currentAppId ?? "",
      currentApp: initialValues?.currentApp ?? null,
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      userGroupId: initialValues?.userGroupId ?? "",
      userGroup: initialValues?.userGroup ?? null,
      auth0UserId: initialValues?.auth0UserId ?? "",
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        disabled={form.getInputProps("recordState").value === "Inactive"}
        title={title}
        actionSection={actionSection}
        headerSection={headerSection}
        recordState={t(
          (form.getInputProps("recordState").value as string) ?? "",
        )}
      >
        <Tabs defaultValue="general">
          <Tabs.List>
            <Tabs.Tab value="general">{t("appUsers.general")}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="general">
            <Grid mt="xs">
              <Grid.Col span={{ base: 12, md: 4 }}>
                <Paper shadow="xs" p="xs" pt="">
                  <FieldValidation isDirty={form.isDirty("name")}>
                    <TextInput
                      label={t("appUsers.name")}
                      {...form.getInputProps("name")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("email")}>
                    <TextInput
                      label={t("appUsers.email")}
                      {...form.getInputProps("email")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("firstName")}>
                    <TextInput
                      label={t("appUsers.firstName")}
                      {...form.getInputProps("firstName")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("lastName")}>
                    <TextInput
                      label={t("appUsers.lastName")}
                      {...form.getInputProps("lastName")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("department")}>
                    <TextInput
                      label={t("appUsers.department")}
                      {...form.getInputProps("department")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("division")}>
                    <TextInput
                      label={t("appUsers.division")}
                      {...form.getInputProps("division")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("jobTitle")}>
                    <TextInput
                      label={t("appUsers.jobTitle")}
                      {...form.getInputProps("jobTitle")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("currentAppId")}>
                    <AppLookup
                      label={t("appUsers.currentApp")}
                      initial={form.getValues().currentApp}
                      initialId={form.getValues().currentAppId}
                      identifier="currentAppIdAppUser"
                      {...form.getInputProps("currentAppId")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("businessUnitId")}>
                    <BusinessUnitLookup
                      label={t("appUsers.businessUnit")}
                      initial={form.getValues().businessUnit}
                      initialId={form.getValues().businessUnitId}
                      identifier="businessUnitIdAppUser"
                      {...form.getInputProps("businessUnitId")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("userGroupId")}>
                    <UserGroupLookup
                      isMovingHelpOnly={false}
                      label={t("appUsers.userGroup")}
                      initial={form.getValues().userGroup}
                      initialId={form.getValues().userGroupId}
                      identifier="userGroupIdAppUser"
                      {...form.getInputProps("userGroupId")}
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("timeZone")}>
                    <Select
                      searchable
                      style={{ display: "none" }}
                      label={t("appUsers.timeZone")}
                      data={timeZoneEnum}
                      {...form.getInputProps("timeZone")}
                      clearable
                    />
                  </FieldValidation>
                  <FieldValidation isDirty={form.isDirty("language")}>
                    <Select
                      searchable
                      label={t("appUsers.language")}
                      data={languageEnum.map((value) => ({
                        value,
                        label: t(getEnumTransKey("appUsers", value)),
                      }))}
                      clearable
                      {...form.getInputProps("language")}
                    />
                  </FieldValidation>
                </Paper>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <AppUserRoles userId={initialValues?.auth0UserId} />
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>
      </EntityLayout>
    </form>
  );
}
