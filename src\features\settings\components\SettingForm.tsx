import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  Grid,
  NumberInput,
  Paper,
  Select,
  Switch,
  Tabs,
  TextInput,
} from "@mantine/core";
import { type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { LossReasonList } from "@/features/lossReasons/routes/LossReasonList";
import { AppointmentTypeTemplateMappingList } from "@/features/appointmentTypeTemplateMappings/routes/AppointmentTypeTemplateMappingList";
import { useNavigate, useParams } from "react-router-dom";
import { UserGroupLookup } from "@/components/Lookup/Features/UserGroups/UserGroupLookup";
import { HtmlTemplateLookup } from "@/components/Lookup/Features/HtmlTemplates/HtmlTemplateLookup";

/**
 * @TODO: ownerId
 */

const formSchema = z.object({
  name: z.string().min(1),
  sendEmails: z.boolean(),
  quoteEmailTemplateId: z.string(),
  quoteEmailTemplate: z.object({}).nullable(),
  callbackStoreManagerEmailTemplateId: z.string(),
  callbackStoreManagerEmailTemplate: z.object({}).nullable(),
  callbackSalesEmailTemplateId: z.string(),
  callbackSalesEmailTemplate: z.object({}).nullable(),
  noShowAppointmentEmailTemplateId: z.string(),
  noShowAppointmentEmailTemplate: z.object({}).nullable(),
  reservationEmailTemplateId: z.string(),
  reservationEmailTemplate: z.object({}).nullable(),
  salesAndServiceGroupId: z.string(),
  salesAndServiceGroup: z.object({}).nullable(),
  movingHelpNorthId: z.string().nullable(),
  movingHelpNorth: z.object({}).nullable(),
  movingHelpWestId: z.string().nullable(),
  movingHelpWest: z.object({}).nullable(),
  movingHelpSouthId: z.string().nullable(),
  movingHelpSouth: z.object({}).nullable(),
  quoteAttachmentTemplateId: z.string(),
  quoteAttachmentTemplate: z.object({}).nullable(),
  quoteExpiration: z.coerce.number().nullable(),
  minimumDepositForUnits: z.coerce.number().nullable(),
  omnichannelPriority: z.coerce.number(),
  dateFormat: z.string(),
});

type FormSchema = z.infer<typeof formSchema>;

interface SettingFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  isCreate: boolean;
}

export function SettingForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: SettingFormProps) {
  const { t } = useTranslation("features");
  const navigate = useNavigate();
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const { id, tabValue } = useParams();
  const handleTabChange = (value: string | null) => {
    if (id) {
      navigate(`/app/settings/${id}/${value}`);
    }
  };
  const form = useForm<FormSchema>({
    initialValues: {
      name: initialValues?.name ?? "",
      sendEmails: initialValues?.sendEmails ?? true,
      quoteEmailTemplateId: initialValues?.quoteEmailTemplateId ?? "",
      quoteEmailTemplate: initialValues?.quoteEmailTemplate ?? null,
      salesAndServiceGroupId: initialValues?.salesAndServiceGroupId ?? "",
      salesAndServiceGroup: initialValues?.salesAndServiceGroup ?? null,
      omnichannelPriority: initialValues?.omnichannelPriority ?? 0,
      movingHelpNorthId: initialValues?.movingHelpNorthId ?? "",
      movingHelpNorth: initialValues?.movingHelpNorth ?? null,
      movingHelpWestId: initialValues?.movingHelpWestId ?? "",
      movingHelpWest: initialValues?.movingHelpWest ?? null,
      movingHelpSouthId: initialValues?.movingHelpSouthId ?? "",
      movingHelpSouth: initialValues?.movingHelpSouth ?? null,
      callbackSalesEmailTemplateId:
        initialValues?.callbackSalesEmailTemplateId ?? "",
      callbackSalesEmailTemplate:
        initialValues?.callbackSalesEmailTemplate ?? null,
      callbackStoreManagerEmailTemplateId:
        initialValues?.callbackStoreManagerEmailTemplateId ?? "",
      callbackStoreManagerEmailTemplate:
        initialValues?.callbackStoreManagerEmailTemplate ?? null,
      noShowAppointmentEmailTemplateId:
        initialValues?.noShowAppointmentEmailTemplateId ?? "",
      noShowAppointmentEmailTemplate:
        initialValues?.noShowAppointmentEmailTemplate ?? null,
      reservationEmailTemplateId:
        initialValues?.reservationEmailTemplateId ?? "",
      reservationEmailTemplate: initialValues?.reservationEmailTemplate ?? null,
      quoteAttachmentTemplateId: initialValues?.quoteAttachmentTemplateId ?? "",
      quoteAttachmentTemplate: initialValues?.quoteAttachmentTemplate ?? null,
      quoteExpiration: initialValues?.quoteExpiration ?? null,
      minimumDepositForUnits: initialValues?.minimumDepositForUnits ?? null,
      dateFormat: initialValues?.dateFormat ?? "yyyy-MM-dd",
    },
    validate: zodResolver(formSchema),
  });
  const dateFormatValues = ["yyyy-MM-dd", "dd-MM-yyyy", "MM-dd-yyyy"];
  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Tabs
          value={tabValue ?? "general"}
          defaultValue="general"
          onChange={handleTabChange}
        >
          <Tabs.List style={{ pointerEvents: "auto", fontWeight: "bold" }}>
            <Tabs.Tab value="general">{t("settings.general")}</Tabs.Tab>
            <Tabs.Tab value="templateMappings" disabled={!id}>
              {t("settings.templateMappings")}
            </Tabs.Tab>
          </Tabs.List>
          <Tabs.Panel value="general">
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper shadow="xs" p="xs">
                  <TextInput
                    mt="xs"
                    label={t("settings.name")}
                    {...form.getInputProps("name")}
                  />
                  <UserGroupLookup
                    mt="xs"
                    label={t("settings.salesAndServiceGroup")}
                    initial={form.getValues().salesAndServiceGroup}
                    initialId={form.getValues().salesAndServiceGroupId}
                    identifier="salesAndServiceGroupIdSetting"
                    {...form.getInputProps("salesAndServiceGroupId")}
                  />
                  <NumberInput
                    mt={"sm"}
                    min={1}
                    max={10}
                    label={t("settings.omnichannelPriority")}
                    {...form.getInputProps("omnichannelPriority")}
                  />

                  <UserGroupLookup
                    mt="xs"
                    label={t("entity.movingHelpNorth")}
                    initial={form.getValues().movingHelpNorth}
                    initialId={form.getValues().movingHelpNorthId}
                    identifier="movingHelpNorthIdSetting"
                    {...form.getInputProps("movingHelpNorthId")}
                  />
                  <UserGroupLookup
                    mt="xs"
                    label={t("entity.movingHelpSouth")}
                    initial={form.getValues().movingHelpSouth}
                    initialId={form.getValues().movingHelpSouthId}
                    identifier="movingHelpSouthIdSetting"
                    {...form.getInputProps("movingHelpSouthId")}
                  />
                  <UserGroupLookup
                    mt="xs"
                    label={t("entity.movingHelpWest")}
                    initial={form.getValues().movingHelpWest}
                    initialId={form.getValues().movingHelpWestId}
                    identifier="movingHelpWestIdSetting"
                    {...form.getInputProps("movingHelpWestId")}
                  />
                  <NumberInput
                    min={1}
                    max={31}
                    mt="xs"
                    label={t("settings.quoteExpiration")}
                    {...form.getInputProps("quoteExpiration")}
                  />

                  <NumberInput
                    mt="xs"
                    label={t("settings.minimumDepositForUnits")}
                    leftSection={config.CURRENCY.symbol}
                    {...form.getInputProps("minimumDepositForUnits")}
                  />
                  <Select
                    label={t("settings.dateFormat")}
                    data={dateFormatValues.map((value) => ({
                      value,
                      label: value,
                    }))}
                    {...form.getInputProps("dateFormat")}
                  />
                  <Switch
                    mt="xs"
                    label={t("settings.sendEmails")}
                    checked={form.getValues().sendEmails}
                    {...form.getInputProps("sendEmails")}
                  />
                </Paper>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
          <Tabs.Panel value="templateMappings">
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper shadow="xs" p="lg">
                  <HtmlTemplateLookup
                    mt="xs"
                    label={t("settings.noShowAppointmentEmailTemplate")}
                    initial={form.getValues().noShowAppointmentEmailTemplate}
                    initialId={
                      form.getValues().noShowAppointmentEmailTemplateId
                    }
                    identifier="noShowAppointmentEmailTemplateIdSetting"
                    {...form.getInputProps("noShowAppointmentEmailTemplateId")}
                  />
                  <HtmlTemplateLookup
                    mt="xs"
                    label={t("settings.callbackStoreManagerEmailTemplate")}
                    initial={form.getValues().callbackStoreManagerEmailTemplate}
                    initialId={
                      form.getValues().callbackStoreManagerEmailTemplateId
                    }
                    identifier="callbackStoreManagerEmailTemplateIdSetting"
                    {...form.getInputProps(
                      "callbackStoreManagerEmailTemplateId",
                    )}
                  />
                  <HtmlTemplateLookup
                    mt="xs"
                    label={t("settings.callbackSalesEmailTemplate")}
                    initial={form.getValues().callbackSalesEmailTemplate}
                    initialId={form.getValues().callbackSalesEmailTemplateId}
                    identifier="callbackSalesEmailTemplateIdSetting"
                    {...form.getInputProps("callbackSalesEmailTemplateId")}
                  />
                  <HtmlTemplateLookup
                    mt="xs"
                    label={t("settings.quoteEmailTemplate")}
                    initial={form.getValues().quoteEmailTemplate}
                    initialId={form.getValues().quoteEmailTemplateId}
                    identifier="quoteEmailTemplateIdSetting"
                    {...form.getInputProps("quoteEmailTemplateId")}
                  />
                  <HtmlTemplateLookup
                    mt="xs"
                    label={t("settings.quoteAttachmentTemplate")}
                    initial={form.getValues().quoteAttachmentTemplate}
                    initialId={form.getValues().quoteAttachmentTemplateId}
                    identifier="quoteAttachmentTemplateIdSetting"
                    {...form.getInputProps("quoteAttachmentTemplateId")}
                  />
                  <HtmlTemplateLookup
                    mt="xs"
                    label={t("settings.reservationEmailTemplate")}
                    initial={form.getValues().reservationEmailTemplate}
                    initialId={form.getValues().reservationEmailTemplateId}
                    identifier="reservationEmailTemplateIdSetting"
                    {...form.getInputProps("reservationEmailTemplateId")}
                  />
                </Paper>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper shadow="xs" p="lg">
                  <LossReasonList pageSize={6} />
                  <AppointmentTypeTemplateMappingList pageSize={6} />
                </Paper>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>
      </EntityLayout>
    </form>
  );
}
