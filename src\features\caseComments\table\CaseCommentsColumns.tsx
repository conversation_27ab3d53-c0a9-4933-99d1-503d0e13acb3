import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { CaseLookup } from "@/components/Lookup/Features/Cases/CaseLookup";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function CaseCommentsColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "text",
      header: t("caseComments.text"),
      filterVariant: "text",
    },
    {
      accessorKey: "case",
      header: t("caseComments.number"),
      ...TableRenderer(CaseLookup, "cases", ["number"]),
    },
    {
      accessorKey: "owner",
      header: t("caseComments.owner"),
      filterVariant: "text",
      ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
    },
  ];
  return columns;
}
