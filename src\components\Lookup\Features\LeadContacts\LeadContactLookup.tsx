import { type Schemas } from "@/types";
import { Box, Center, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
  BoolOptionRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { MultiLookup } from "../../MultiLookup/MultiLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { t } from "i18next";

interface CommmonEntityProps {
  id?: string;
  fullName?: string;
}

type LeadContactLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    multiLookupOnChange: (value: string, id: string, entity: string) => void;
    entity?: string;
    identifier: string;
    initial?: CommmonEntityProps | null;
  };

const ENTITY = "leadcontacts";

export function LeadContactLookup({
  required = false,
  disabled = false,
  multiLookupOnChange,
  initial,
  identifier,
  entity = "contacts",
  ...props
}: LeadContactLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.fullName ?? null,
    initial?.id ?? null,
    initial,
    true,
  );

  useLookup(identifier, initial?.fullName ?? null, initial?.id ?? null);

  const queryParams: QueryParams = {
    pageSize: 50,
    filter: "RecordState==0",
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<
      Schemas["LeadContactLookupDto"],
      Schemas["LeadContactLookupDtoPagedList"]
    >({
      queryParams: queryParams,
      resourcePath: "/api/Leads/withContacts",
      queryKey: "leadContact",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box flex={1.3}>{t("common:lookup.fullName")}</Box>
      <Box flex={1.5}>{t("common:lookup.email")}</Box>
      <Box flex={0.7}>{t("common:lookup.mobile")}</Box>
      <Box flex={0.7}>{t("common:lookup.phone")}</Box>
      <Center flex={0.2}>{t("common:lookup.lead")}</Center>
      <Box flex={0.6}>{t("common:lookup.type")}</Box>
    </React.Fragment>
  );
  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.fullName, searchTerm, `fullName_${entity.id}`, 1.3),
        OptionRender(entity.email, searchTerm, `email_${entity.id}`, 1.5),
        OptionRender(entity.mobile, searchTerm, `mobile_${entity.id}`, 0.7),
        OptionRender(entity.phone, searchTerm, `phone_${entity.id}`, 0.7),
        BoolOptionRender(entity.isLead ?? false, `lead_${entity.id}`, 0.2),
        OptionRender(entity.type, searchTerm, `type_${entity.id}`, 0.6),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.fullName!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <MultiLookup
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      multiLookupOnChange={multiLookupOnChange}
      identifier={identifier}
      entity={ENTITY}
      navigateEntity={entity}
      {...props}
    />
  );
}
