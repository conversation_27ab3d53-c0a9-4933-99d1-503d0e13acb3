import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { type paths } from "@/types/api.generated";
import { useTranslation } from "react-i18next";
import { lowerCaseNthLetter } from "@/utils/filters";

const ENTITY = "rentableItems";
type ENTITY_TYPE = Schemas["RentableItemAvailabilityDto"];

type RentableItemLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
    startDate: Date | null;
    endDate: Date | null;
    businessUnitId: string;
    rentableItemType: string | null;
    appointmentId?: string;
  };

export function RentableItemLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  startDate,
  endDate,
  businessUnitId,
  rentableItemType,
  appointmentId,
  ...props
}: RentableItemLookupProps) {
  const { t } = useTranslation("features");
  const { searchTerm } = useLookup(
    identifier,
    initial?.licensePlate,
    initialId,
    initial,
    true,
  );

  const queryFilters = [];
  if (businessUnitId) {
    queryFilters.push(`businessUnitId == ${businessUnitId}`);
  }
  if (rentableItemType) {
    queryFilters.push(`rentableItemType == ${rentableItemType}`);
  }

  const queryParams = {
    pageSize: 50,
    filter: queryFilters.join(" && "),
    startDate: startDate?.toISOString().replace("Z", ""),
    endDate: endDate?.toISOString().replace("Z", ""),
    appointmentId: appointmentId ?? "",
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["RentableItemAvailabilityDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/RentableItems/availability" as keyof paths,
      queryKey: "RentableItem",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box flex={2}>{t("rentableItems.licensePlate")}</Box>
      <Box flex={3}>{t("rentableItems.brand")}</Box>
      <Box flex={3}>{t("rentableItems.type")}</Box>
      <Box flex={3}>{t("rentableItems.availability")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const isDisabled =
        entity.availability === "NotAvailable" ||
        entity.availability === "InMaintenance";

      const renderedOptions = [
        OptionRender(
          entity.licensePlate,
          searchTerm,
          `licensePlate_${entity.id}`,
          2,
        ),

        OptionRender(entity.brand, searchTerm, `brand_${entity.id}`, 3),
        OptionRender(
          t("rentableItems." + lowerCaseNthLetter(entity.type ?? "")),
          searchTerm,
          `type_${entity.id}`,
          3,
        ),
        OptionRender(
          t("rentableItems." + lowerCaseNthLetter(entity.availability ?? "")),
          searchTerm,
          `availability_${entity.id}`,
          3,
        ),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.licensePlate!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
        isDisabled,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      width={700}
      {...props}
    />
  );
}
