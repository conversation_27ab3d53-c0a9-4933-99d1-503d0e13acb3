import { Route, Routes } from "react-router-dom";
import { SettingList } from "./SettingList";
import { SettingShow } from "./SettingShow";

export default function HtmlTemplatesRoutes() {
  return (
    <Routes>
      <Route index element={<SettingList />} />
      <Route path=":id" element={<SettingShow />}>
        <Route path=":tabValue" element={<SettingShow />} />
      </Route>
    </Routes>
  );
}
