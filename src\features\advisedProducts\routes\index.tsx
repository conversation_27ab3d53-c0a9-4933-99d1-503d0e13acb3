import { Route, Routes } from "react-router-dom";
import { AdvisedProductShow } from "@/features/advisedProducts/routes/AdvisedProductShow";
import { AdvisedProductList } from "@/features/advisedProducts/routes/AdvisedProductList";
import { AdvisedProductCreate } from "@/features/advisedProducts/routes/AdvisedProductCreate";

export default function AdvisedProductsRoutes() {
  return (
    <Routes>
      <Route index element={<AdvisedProductList />} />
      <Route path=":id" element={<AdvisedProductShow />} />
      <Route path="create" element={<AdvisedProductCreate />} />
    </Routes>
  );
}
