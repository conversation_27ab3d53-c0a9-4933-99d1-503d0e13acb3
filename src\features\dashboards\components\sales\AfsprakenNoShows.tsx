import type React from "react";
import { Grid } from "@mantine/core";
import HorizontalBarChart from "../common/HorizontalBarChart";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import { type DashboardContentProps } from "../index";

const metrics: MetricProps[] = [
  { metric: "AllAppointments" },
  { metric: "BookedAppointments" },
  { metric: "EditedLeads" },
  { metric: "TestMetric" },
];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={{ base: 12, md: 12, lg: 7 }}>
      <Grid>
        {["Booked Appointments", "Total Reservations Closed", "No Shows"].map(
          (title, index) => (
            <Grid.Col key={index} span={{ base: 12, md: 12, lg: 12 }}>
              <HorizontalBarChart
                data={data[index]}
                orientation={"horizontal"}
                height={"180"}
                title={title}
              />
            </Grid.Col>
          ),
        )}
      </Grid>
    </Grid.Col>
    <Grid.Col span={{ base: 12, md: 12, lg: 2.5 }}>
      <HorizontalBarChart
        data={data[2]}
        height={"720"}
        title="No Show %"
        percentage
      />
    </Grid.Col>
    <Grid.Col span={{ base: 12, md: 12, lg: 2.5 }}>
      <HorizontalBarChart
        data={data[3]}
        height={"720"}
        title="Total Reservations"
        percentage
      />
    </Grid.Col>
  </Grid>
);

const AfsprakenNoShows: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default AfsprakenNoShows;
