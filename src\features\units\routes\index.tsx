import { Route, Routes } from "react-router-dom";
import { UnitList } from "./UnitList";
import { UnitShow } from "./UnitShow";
import { UnitCreate } from "./UnitCreate";

export default function UnitsRoutes() {
  return (
    <Routes>
      <Route index element={<UnitList />} />
      <Route path=":id" element={<UnitShow />} />
      <Route path="create" element={<UnitCreate />} />
    </Routes>
  );
}
