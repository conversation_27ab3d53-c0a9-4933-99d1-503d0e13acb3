import { EntityLinkRenderer } from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { AppointmentType } from "@/types/enums";
import { lowerCaseNthLetter } from "@/utils/filters";
import { type ComboboxData } from "@mantine/core";
import { HtmlTemplateLookup } from "@/components/Lookup/Features/HtmlTemplates/HtmlTemplateLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function AppointmentTypeTemplateMappingColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "appointmentType",
      header: t("appointmentTypeTemplateMappings.appointmentType"),
      filterVariant: "multi-select",
      Cell: (props) =>
        EntityLinkRenderer(
          {
            ...props,
            renderedCellValue: t(
              "appointments." +
                lowerCaseNthLetter(props.cell.getValue<string>()),
            ),
          },
          "appointmentTypeTemplateMappings",
        ),
      mantineFilterSelectProps: {
        data: AppointmentType as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "htmlTemplate",
      header: t("appointmentTypeTemplateMappings.htmlTemplate"),
      ...TableRenderer(HtmlTemplateLookup, "htmlTemplates", ["name"]),
    },
  ];
  return columns;
}
