import { useState, useEffect } from "react";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Center,
  Flex,
  Text,
  Stack,
  Paper,
  Group,
  Alert,
  Progress,
  Select,
  Loader,
} from "@mantine/core";
import {
  IconExternalLink,
  IconCheck,
  IconX,
  IconAlertCircle,
  IconShield,
  IconBrowser,
} from "@tabler/icons-react";

interface PageIdinVerificationProps extends PageProps<PageName> {}

type IdinStep =
  | "bank_selection"
  | "redirecting"
  | "waiting"
  | "success"
  | "error"
  | "cancelled";

interface Bank {
  id: string;
  name: string;
  logo?: string;
}

export default function PageIdinVerification({
  setPages,
  pages,
}: PageIdinVerificationProps) {
  const { t } = useTranslation("features");
  const [currentStep, setCurrentStep] = useState<IdinStep>("bank_selection");
  const [selectedBank, setSelectedBank] = useState<string>("");
  const [progress, setProgress] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");
  const [verificationData, setVerificationData] = useState<{
    name?: string;
    dateOfBirth?: string;
    bsn?: string;
  }>({});

  // Mock bank data
  const banks: Bank[] = [
    { id: "ing", name: "ING Bank" },
    { id: "rabobank", name: "Rabobank" },
    { id: "abn_amro", name: "ABN AMRO" },
    { id: "sns", name: "SNS Bank" },
    { id: "asn", name: "ASN Bank" },
    { id: "bunq", name: "bunq" },
    { id: "knab", name: "Knab" },
    { id: "triodos", name: "Triodos Bank" },
  ];

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (currentStep === "redirecting") {
      timer = setTimeout(() => {
        setProgress(33);
        setCurrentStep("waiting");
      }, 2000);
    } else if (currentStep === "waiting") {
      timer = setTimeout(() => {
        setProgress(66);
        // Simulate successful verification
        setVerificationData({
          name: "Jan de Vries",
          dateOfBirth: "1985-03-15",
          bsn: "*********",
        });
        setCurrentStep("success");
        setProgress(100);
      }, 5000);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [currentStep]);

  const handleBankSelection = () => {
    if (!selectedBank) return;
    setCurrentStep("redirecting");
    setProgress(10);
  };

  const handleRetry = () => {
    setCurrentStep("bank_selection");
    setSelectedBank("");
    setProgress(0);
    setErrorMessage("");
    setVerificationData({});
  };

  const handleContinue = () => {
    setPages([...pages, "UNIT_SELECTION"]);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "bank_selection":
        return (
          <Stack align="center" gap="lg" w="100%">
            <Group gap="md">
              <IconShield size={48} color="blue" />
              <Stack gap={0}>
                <Text fz={20} fw={600}>
                  {t("iDIN Verification")}
                </Text>
                <Text fz={14} c="dimmed">
                  {t("Secure identity verification via your bank")}
                </Text>
              </Stack>
            </Group>

            <Alert
              icon={<IconShield size="1rem" />}
              title={t("Secure & Trusted")}
              color="blue"
              w="100%"
            >
              {t(
                "iDIN is a secure Dutch identity verification service. Your bank will verify your identity without sharing sensitive information.",
              )}
            </Alert>

            <Box w="100%">
              <Text fw={500} mb="sm">
                {t("Select your bank")}:
              </Text>
              <Select
                placeholder={t("Choose your bank")}
                data={banks.map((bank) => ({
                  value: bank.id,
                  label: bank.name,
                }))}
                value={selectedBank}
                onChange={(value) => setSelectedBank(value || "")}
                size="lg"
                w="100%"
              />
            </Box>

            <Button
              size="lg"
              leftSection={<IconExternalLink size="1rem" />}
              onClick={handleBankSelection}
              disabled={!selectedBank}
              w="100%"
              maw={300}
            >
              {t("Continue with")}{" "}
              {banks.find((b) => b.id === selectedBank)?.name ||
                t("Selected Bank")}
            </Button>
          </Stack>
        );

      case "redirecting":
        return (
          <Stack align="center" gap="md">
            <IconBrowser size={64} color="blue" />
            <Text fz={20} fw={600}>
              {t("Redirecting to your bank")}
            </Text>
            <Text fz={14} c="dimmed" ta="center">
              {t("You will be redirected to")}{" "}
              {banks.find((b) => b.id === selectedBank)?.name}
            </Text>
            <Loader size="lg" />
          </Stack>
        );

      case "waiting":
        return (
          <Stack align="center" gap="md">
            <IconShield size={64} color="orange" />
            <Text fz={20} fw={600}>
              {t("Waiting for verification")}
            </Text>
            <Text fz={14} c="dimmed" ta="center" maw={400}>
              {t(
                "Please complete the verification process in your bank's app or website. This window will update automatically.",
              )}
            </Text>
            <Paper p="md" withBorder>
              <Group gap="xs">
                <IconBrowser size={20} />
                <Text fz={14}>
                  {t("Verifying with")}{" "}
                  {banks.find((b) => b.id === selectedBank)?.name}
                </Text>
              </Group>
            </Paper>
            <Loader size="lg" color="orange" />
          </Stack>
        );

      case "success":
        return (
          <Stack align="center" gap="md">
            <IconCheck size={64} color="green" />
            <Text fz={20} fw={600} c="green">
              {t("Verification Successful")}
            </Text>
            <Paper p="md" withBorder w="100%" maw={400}>
              <Stack gap="xs">
                <Group justify="space-between">
                  <Text fw={500}>{t("Name")}:</Text>
                  <Text>{verificationData.name}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>{t("Date of Birth")}:</Text>
                  <Text>{verificationData.dateOfBirth}</Text>
                </Group>
                <Group justify="space-between">
                  <Text fw={500}>{t("BSN")}:</Text>
                  <Text>{verificationData.bsn}</Text>
                </Group>
              </Stack>
            </Paper>
            <Alert
              icon={<IconCheck size="1rem" />}
              title={t("Verified")}
              color="green"
              w="100%"
              maw={400}
            >
              {t("Your identity has been successfully verified via iDIN.")}
            </Alert>
          </Stack>
        );

      case "error":
        return (
          <Stack align="center" gap="md">
            <IconX size={64} color="red" />
            <Text fz={20} fw={600} c="red">
              {t("Verification Failed")}
            </Text>
            <Alert
              icon={<IconAlertCircle size="1rem" />}
              title={t("Error")}
              color="red"
              w="100%"
              maw={400}
            >
              {errorMessage ||
                t(
                  "The verification process failed. Please try again or contact support.",
                )}
            </Alert>
          </Stack>
        );

      case "cancelled":
        return (
          <Stack align="center" gap="md">
            <IconX size={64} color="orange" />
            <Text fz={20} fw={600} c="orange">
              {t("Verification Cancelled")}
            </Text>
            <Alert
              icon={<IconAlertCircle size="1rem" />}
              title={t("Cancelled")}
              color="orange"
              w="100%"
              maw={400}
            >
              {t(
                "The verification process was cancelled. You can try again or choose a different verification method.",
              )}
            </Alert>
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      <Center mt={40}>
        <Stack w="100%" maw={600} align="center" gap="xl">
          <Text fz={24} fw={700} ta="center">
            {t("iDIN Identity Verification")}
          </Text>

          {currentStep !== "bank_selection" && (
            <Progress
              value={progress}
              size="lg"
              w="100%"
              color={
                currentStep === "error"
                  ? "red"
                  : currentStep === "cancelled"
                    ? "orange"
                    : "blue"
              }
            />
          )}

          <Paper p="xl" withBorder w="100%" mih={400}>
            <Center h="100%">{renderStepContent()}</Center>
          </Paper>

          <Flex gap="md" w="100%" justify="center">
            {(currentStep === "error" || currentStep === "cancelled") && (
              <Button variant="outline" onClick={handleRetry}>
                {t("Try Again")}
              </Button>
            )}

            {currentStep === "success" && (
              <Button
                leftSection={<IconCheck size="1rem" />}
                onClick={handleContinue}
              >
                {t("Continue")}
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() => setPages([...pages.slice(0, -1)])}
              disabled={
                currentStep === "redirecting" || currentStep === "waiting"
              }
            >
              {t("Back")}
            </Button>
          </Flex>
        </Stack>
      </Center>
    </Box>
  );
}
