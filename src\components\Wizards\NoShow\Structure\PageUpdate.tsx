import {
  Box,
  Text,
  Center,
  Flex,
  Button,
  Badge,
  ScrollArea,
  Paper,
} from "@mantine/core";
import { IconCalendarMonth } from "@tabler/icons-react";
import {
  formatDateTime,
  generateTimeSlotsFrom,
  type PageName,
} from "../Common/Common";
import { type Schemas } from "@/types";
import TimeSlotSelector from "../../Common/Time/TimeSlotSelector";
import { useEffect, useState } from "react";
import {
  useEntityCreateMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";
import { type PageProps } from "../../Common/Header/WizardHeader";

interface PageUpdateProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
  appointment?: Schemas["AppointmentRetrieveDto"];
}

export default function PageUpdate({
  setPages,
  pages,
  appointment,
  lead,
}: PageUpdateProps) {
  const { t } = useTranslation("features");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const queryCache = useQueryClient();
  const { mutate: createCall } = useEntityCreateMutation<
    Schemas["PhoneCall"],
    Schemas["PhoneCallCreateDto"]
  >({ resourcePath: "/api/PhoneCalls", queryKey: "phoneCall" });

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Appointment"],
    Schemas["AppointmentPatchDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: appointment?.id ?? "",
    queryKey: "appointment",
  });

  let times: string[] = [];
  const startDate = appointment?.startDate ?? new Date();
  const endDate = appointment?.endDate ?? new Date();
  const [newStartDate, setNewStartDate] = useState<Date>(new Date(startDate));
  const [newEndDate, setNewEndDate] = useState<Date>(new Date(endDate));
  if (startDate) {
    const start = new Date(startDate);
    if (!isNaN(start.getTime())) {
      times = generateTimeSlotsFrom(start);
    }
  }

  // this use effect is only used once, when component is mounted
  // by default we select the next available slot, so that
  // the user could easily click through the wizard.
  useEffect(() => {
    if (times.length > 0) {
      const splitTime = times[0]?.split(":");
      if (times.length > 0 && splitTime != undefined) {
        const [hours, minutes] = splitTime.map(Number);
        const defaultStart = new Date(startDate);
        defaultStart.setHours(hours!, minutes, 0, 0);

        const originalStart = new Date(startDate).getTime();
        const originalEnd = new Date(endDate).getTime();
        const duration = originalEnd - originalStart;

        setNewStartDate(defaultStart);
        setNewEndDate(new Date(defaultStart.getTime() + duration));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onConfirmClick = () => {
    update(
      {
        startDate: newStartDate.toISOString(),
        endDate: newEndDate.toISOString(),
        rescheduledFromWizard: true,
      },
      {
        onSuccess: () => {
          createCall(
            {
              leadId: lead?.id,
              phoneCallStatus: "Reached",
              phoneCallType: "PhoneCall",
              startDate: new Date().toISOString().replace("Z", ""),
              endDate: new Date().toISOString().replace("Z", ""),
            },
            {
              onSuccess: () => {
                void queryCache.invalidateQueries(
                  "lead_" + appointment?.leadId,
                );
                notifications.show({
                  color: "green",
                  title: "Appointment updated",
                  message: "Appointment updated",
                });
                setPages([...pages, "COMPLETED"]);
                setIsLoading(false);
              },
              onError: (error) => {
                setIsLoading(false);
                console.error(error);
              },
            },
          );
        },
        onError: (error) => {
          setIsLoading(false);
          console.error(error);
        },
      },
    );
  };

  return (
    <Box>
      <Center>
        <Text fz={16} fw={600} c={"#282828"}>
          {t("wizards.UpdateStartTime.Title")}
        </Text>
      </Center>
      <Center>
        <Text fz={10} fw={300} c={"#ADADAD"}>
          {t("wizards.UpdateStartTime.Label")}
        </Text>
      </Center>
      <Center mt={40} w={"100%"}>
        <Flex direction={"row"} justify={"center"} align={"center"} p={20}>
          <Paper flex={1} p={32} h={"36vh"}>
            <Flex>
              <Text fz={16} fw={600} c={"#282828"} mr={16}>
                {appointment?.lead?.fullName}
              </Text>
            </Flex>
            <Flex mt={4} gap={8}>
              <Badge fz={8} fw={400}>
                {appointment?.appointmentType}
              </Badge>
              <Badge fz={8} fw={400} color="#ADADAD">
                {appointment?.appointmentStatus}
              </Badge>
            </Flex>
            <Flex>
              <Box mr={16}>
                <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
                  {t("appointments.TimeLabel")}
                </Text>
                <Text fz={14} fw={500} c={"#282828"}>
                  {`${formatDateTime(new Date(startDate)).time} - ${formatDateTime(new Date(endDate)).time}`}
                </Text>
              </Box>
              <Box>
                <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
                  {t("appointments.NewTimeLabel")}
                </Text>
                <Text fz={14} fw={500} c={"#282828"}>
                  {`${formatDateTime(new Date(newStartDate)).time} - ${formatDateTime(new Date(newEndDate)).time}`}
                </Text>
              </Box>
            </Flex>
            <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
              {t("appointments.BusinessUnitLabel")}
            </Text>
            <Text fz={14} fw={500} c={"#282828"}>
              {appointment?.businessUnit?.code}
            </Text>
            <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
              {t("appointments.PhoneLabel")}
            </Text>
            <Text fz={14} fw={500} c={"#282828"}>
              {appointment?.lead?.phone}
            </Text>
            <Text fz={12} fw={400} c={"#ADADAD"} mt={8}>
              {t("appointments.MobileLabel")}
            </Text>
            <Text fz={14} fw={500} c={"#282828"}>
              {appointment?.lead?.mobile}
            </Text>
          </Paper>
          <Paper flex={1} p={32} h={"36vh"}>
            <Center>
              <Text fz={16} fw={600} c={"#282828"}>
                {t("wizards.UpdateStartTime.UpdateTitle")}
              </Text>
            </Center>
            <Text fz={10} fw={400} c={"#ADADAD"}>
              {t("wizards.UpdateStartTime.UpdateLabel")}
            </Text>
            <ScrollArea h={"26vh"} type="always">
              <Box w={"90%"}>
                <TimeSlotSelector
                  enableMultipleTimeSlots={false}
                  defaultSelectedStartTime={times[0]}
                  selectedDate={new Date(startDate)}
                  times={times}
                  onSelectTime={(time) => {
                    if (time instanceof Date) {
                      // Calculate the original duration in milliseconds.
                      const originalStart = new Date(startDate).getTime();
                      const originalEnd = new Date(endDate).getTime();
                      const duration = originalEnd - originalStart;

                      // Set the new start date and update the new end date accordingly.
                      setNewStartDate(time);
                      setNewEndDate(new Date(time.getTime() + duration));
                    }
                  }}
                />
              </Box>
            </ScrollArea>
          </Paper>
        </Flex>
      </Center>
      <Center mt={48}>
        <Button
          loading={isLoading}
          leftSection={<IconCalendarMonth width={20} height={20} />}
          fz={14}
          fw={400}
          style={{ borderRadius: 8 }}
          onClick={() => {
            setIsLoading(true);
            onConfirmClick();
          }}
        >
          {t("wizards.UpdateStartTime.ConfirmButton")}
        </Button>
      </Center>
    </Box>
  );
}
