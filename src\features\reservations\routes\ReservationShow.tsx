import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import {
  ReservationForm,
  type FormSchema,
} from "../components/ReservationForm";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function ReservationShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Reservation"],
    Schemas["ReservationCreateDto"]
  >({
    resourcePath: "/api/Reservations/{id}",
    resourceId: id!,
    queryKey: "reservation",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Reservation"]>({
    resourcePath: "/api/Reservations/{id}",
    resourceId: id!,
    queryKey: "reservation",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("reservation_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <ReservationForm
      isCreate={false}
      title={t("reservations.showTitle", { id })}
      initialValues={
        filterFalsyValues({
          ...data,
          start: data.start ? new Date(data.start) : null,
          reservedUntil: data.reservedUntil
            ? new Date(data.reservedUntil)
            : null,
        }) as FormSchema
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/reservations");
          } else {
            return;
          }
        }
        update(values as Schemas["ReservationCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate("/app/reservations");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
