import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { PhoneCallForm, type FormSchema } from "../components/PhoneCallForm";
import { filterFalsyValues } from "@/utils/filters";
import { notifications } from "@mantine/notifications";
import { Loader } from "@mantine/core";
import { useParams } from "react-router-dom";
import { useQueryClient } from "react-query";

interface PhoneCallShowProps {
  refreshForm?: () => void;
  closeModal?: () => void;
  phoneCallId?: string | null;
}

export function PhoneCallShow({
  refreshForm,
  closeModal,
  phoneCallId,
}: PhoneCallShowProps) {
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const queryCache = useQueryClient();
  const idToUse = phoneCallId ?? id;
  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["PhoneCall"]>({
    resourcePath: "/api/PhoneCalls/{id}",
    resourceId: idToUse!,
    queryKey: "phoneCall",
  });

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["PhoneCall"],
    Schemas["PhoneCallCreateDto"]
  >({
    resourcePath: "/api/PhoneCalls/{id}",
    resourceId: idToUse!,
    queryKey: "phoneCall",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/PhoneCalls/{id}",
    resourceId: idToUse!,
    queryKey: "phoneCall",
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <PhoneCallForm
      isCreate={false}
      isCallback={data.phoneCallType == "Callback" ? true : false}
      closeModal={closeModal}
      initialValues={
        filterFalsyValues({
          ...data,
          startDate: data.startDate ? new Date(data.startDate) : null,
          endDate: data.endDate ? new Date(data.endDate) : null,
          callbackDate: data.callbackDate ? new Date(data.callbackDate) : null,
        }) as FormSchema
      }
      onSubmit={(values) => {
        update(values as Schemas["PhoneCallCreateDto"], {
          onSuccess: () => {
            if (refreshForm) {
              refreshForm();
            }
            void queryCache.invalidateQueries("lead_omnichannel_list");
            void queryCache.invalidateQueries("lead_" + data.leadId);
            notifications.show({
              color: "green",
              title: t("notifications.updateSuccessTitle"),
              message: t("notifications.updateSuccessMessage"),
            });
          },
        });
      }}
      onDelete={async () => {
        await mutateAsync();
        if (!isDeleteError) {
          if (refreshForm) {
            refreshForm();
          }
        }
      }}
    />
  );
}
