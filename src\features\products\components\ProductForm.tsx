import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, NumberInput, Paper, Switch, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { config } from "@/config";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";

const formSchema = z.object({
  name: z.string().nullable(),
  price: z.coerce.number().nullable(),
  afoMigrationId: z.coerce.number().nullable(),
  allowPriceChange: z.boolean().default(false),
});

export type FormSchema = z.infer<typeof formSchema>;

interface ProductFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function ProductForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: ProductFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      name: initialValues?.name ?? "",
      price: initialValues?.price ?? null,
      afoMigrationId: initialValues?.afoMigrationId ?? null,
      allowPriceChange: initialValues?.allowPriceChange ?? false,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        title={title}
        actionSection={actionSection}
        hasUnsavedChanges={form.isDirty()}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <TextInput
                disabled
                label={t("products.afoMigrationId")}
                {...form.getInputProps("afoMigrationId")}
                {...{ labelProps: { style: { flex: 0.5 } } }}
              />
              <FieldValidation isDirty={form.isDirty("name")}>
                <TextInput
                  label={t("products.name")}
                  {...form.getInputProps("name")}
                  {...{ labelProps: { style: { flex: 0.5 } } }}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("price")}>
                <NumberInput
                  label={t("products.price")}
                  leftSection={config.CURRENCY.symbol}
                  {...form.getInputProps("price")}
                  {...{ labelProps: { style: { flex: 0.5 } } }}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("allowPriceChange")}>
                <Switch
                  checked={form.getValues().allowPriceChange}
                  label={t("products.allowPriceChange")}
                  {...form.getInputProps("allowPriceChange")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
