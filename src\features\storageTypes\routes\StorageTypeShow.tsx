import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  StorageTypeForm,
  type FormSchema,
} from "../components/StorageTypeForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function StorageTypeShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["StorageType"],
    Schemas["StorageTypeCreateDto"]
  >({
    resourcePath: "/api/StorageTypes/{id}",
    resourceId: id!,
    queryKey: "storageType",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/storageTypes";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("storageType_" + id);
  };
  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/StorageTypes/{id}",
    resourceId: id!,
    queryKey: "storageType",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["StorageType"]>({
    resourcePath: "/api/StorageTypes/{id}",
    resourceId: id!,
    queryKey: "storageType",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <StorageTypeForm
      isCreate={false}
      title={t("storageTypes.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["StorageTypeCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("storageTypes.delete", { id })}
            modalContent={t("storageTypes.deleteConfirmation", { id })}
            confirmLabel={t("storageTypes.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/storageTypes");
              }
            }}
          />
        </Group>
      }
    />
  );
}
