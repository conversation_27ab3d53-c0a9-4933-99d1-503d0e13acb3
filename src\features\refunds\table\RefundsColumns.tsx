import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DateRenderer,
} from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";
import { CaseLookup } from "@/components/Lookup/Features/Cases/CaseLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function RefundsColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "number",
      header: t("refunds.number"),
      filterVariant: "text",
    },
    {
      accessorKey: "orderNumber",
      header: t("refunds.orderNumber"),
      filterVariant: "text",
    },
    {
      accessorKey: "totalOrderAmount",
      header: t("refunds.totalOrderAmount"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "totalRefundedAmount",
      header: t("refunds.totalRefundedAmount"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "handlingDate",
      header: t("refunds.handlingDate"),
      filterVariant: "range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "customer",
      header: t("refunds.customer"),
      ...TableRenderer(CustomerLookup, "customers", ["name"]),
    },
    {
      accessorKey: "contact",
      header: t("refunds.contact"),
      ...TableRenderer(ContactLookup, "contacts", ["fullName"]),
    },
    {
      accessorKey: "case",
      header: t("refunds.case"),
      ...TableRenderer(CaseLookup, "cases", ["number"]),
    },
  ];
  return columns;
}
