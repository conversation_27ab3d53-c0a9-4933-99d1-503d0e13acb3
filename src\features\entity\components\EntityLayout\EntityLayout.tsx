import {
  Box,
  Button,
  Fieldset,
  <PERSON>lex,
  <PERSON>rollArea,
  Title,
  Tooltip,
} from "@mantine/core";
import { type ReactNode, type PropsWithChildren, useEffect } from "react";
import { DeleteButton } from "../Elements/DeleteButton";
import { EditButton } from "../Elements/EditButton";
import { CreateButton } from "../Elements/CreateButton";
import { AssignRolesButton } from "../Elements/AssignRolesButton";
import { IconArrowLeft } from "@tabler/icons-react";
import { SaveButton } from "../Elements/SaveButton/SaveButton";
import { SaveAndCloseButton } from "../Elements/SaveAndCloseButton/SaveAndCloseButton";
import { RefreshButton } from "../Elements/RefreshButton/RefreshButton";
import { OwnerHeader } from "../Elements/OwnerHeader/OwnerHeader";
import { useTranslation } from "react-i18next";
import { useIsModal } from "@/hooks/useModalContext";
import CaseCloseButtonList from "@/features/cases/components/CaseCloseButtonList";
import { useNavigate } from "react-router-dom";
import { useRouteBlocker } from "@/hooks/blockerContext";
import ViewerComponent from "../Elements/Viewer/ViewerComponent";
import { getEnumTransKey } from "@/utils/trans";
import { TableMantine } from "@/components/Table/TableMantine";

interface EntityLayoutProps {
  title?: string;
  recordState?: string;
  disabled?: boolean;
  hasUnsavedChanges?: boolean;
  actionSection?: ReactNode;
  disabledActionSection?: ReactNode;
  headerSection?: ReactNode;
  showBackButton?: boolean;
  stickyHeader?: boolean;
}

export function EntityLayout({
  title,
  recordState,
  children,
  actionSection,
  disabledActionSection,
  headerSection,
  disabled,
  hasUnsavedChanges = undefined,
  showBackButton = true,
  stickyHeader = true,
}: PropsWithChildren<EntityLayoutProps>) {
  const { t } = useTranslation("features");
  const { isModal, closeModal } = useIsModal();
  const navigate = useNavigate();
  const { setUnsaved } = useRouteBlocker();

  useEffect(() => {
    if (hasUnsavedChanges !== undefined) {
      if (setUnsaved) {
        setUnsaved(hasUnsavedChanges);
      }
    }

    return () => {
      if (setUnsaved) {
        setUnsaved(false);
      }
    };
  }, [hasUnsavedChanges, setUnsaved]);

  const message = t("common.confirmNavigation");
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        event.preventDefault();
        event.returnValue = message;
      }
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasUnsavedChanges, message, t]);

  const HandleBack = () => {
    confirmNavigation(() => {
      if (isModal && closeModal) {
        closeModal();
      } else {
        navigate(-1);
      }
    });
  };

  const confirmNavigation = (callback: () => void) => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(t("common.confirmNavigation"));
      if (!confirmLeave) return;
    }
    callback();
  };
  const stickyStyle = stickyHeader
    ? {
        position: "absolute" as const,
        top: "48px",
        zIndex: 100,
        width: "96%",
        backgroundColor: "white",
        borderBottom: "1px solid #e8e8e8",
      }
    : {};

  const modalStyle = isModal ? { top: "0px" } : {};

  return (
    <Box>
      <Box
        style={{
          display: actionSection ? undefined : "none",
          ...stickyStyle,
          ...modalStyle,
        }}
      >
        <Flex
          align="center"
          direction="row"
          justify={"flex-start"}
          wrap={"nowrap"}
          m={stickyHeader ? 8 : 0}
        >
          <Tooltip label={t("entity.goBack")}>
            <Button
              variant="subtle"
              mr={12}
              w={60}
              onClick={HandleBack}
              display={showBackButton ? undefined : "none"}
              style={{ pointerEvents: "auto" }}
            >
              <IconArrowLeft size={34} />
            </Button>
          </Tooltip>
          <ScrollArea type="auto" scrollbarSize={6}>
            <Flex
              align="center"
              gap={"sm"}
              direction="row"
              justify={"flex-start"}
              wrap={"nowrap"}
              mb={1}
            >
              {!disabled && (actionSection ?? null)}
              {disabled && (disabledActionSection ?? null)}
            </Flex>
          </ScrollArea>
        </Flex>
      </Box>
      <Box mt={stickyHeader ? 48 : 16} ml={12}>
        <Fieldset
          disabled={disabled}
          display={stickyHeader ? undefined : "none"}
        >
          <Flex align="center" direction="row" justify="flex-start" m={16}>
            <Flex
              flex={1}
              align="center"
              gap={"md"}
              direction="row"
              justify="flex-start"
            >
              <Title order={4}>{title}</Title>
              <Title order={6}>
                {recordState && (
                  <>{t(getEnumTransKey("entityLayout", `${recordState}`))}</>
                )}
              </Title>
            </Flex>
            <Flex
              flex={1}
              align="center"
              gap={"md"}
              direction="row"
              justify="flex-end"
              mr={stickyHeader ? 40 : 0}
            >
              {headerSection ?? null}
            </Flex>
          </Flex>
        </Fieldset>
        <Fieldset disabled={disabled}>{children}</Fieldset>
      </Box>
    </Box>
  );
}
EntityLayout.OwnerHeader = OwnerHeader;
EntityLayout.ViewerComponent = ViewerComponent;
EntityLayout.DeleteButton = DeleteButton;
EntityLayout.CaseCloseButtonList = CaseCloseButtonList;
EntityLayout.EditButton = EditButton;
EntityLayout.CreateButton = CreateButton;
EntityLayout.SaveButton = SaveButton;
EntityLayout.SaveAndCloseButton = SaveAndCloseButton;
EntityLayout.RefreshButton = RefreshButton;
EntityLayout.AssignRolesButton = AssignRolesButton;
EntityLayout.TableMantine = TableMantine;
