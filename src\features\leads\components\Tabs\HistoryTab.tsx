import { Box, Grid } from "@mantine/core";
import { AuditList } from "@/features/audits/routes/AuditList";

interface HistoryTabProps {
  leadId?: string;
}

export function HistoryTab({ leadId }: HistoryTabProps) {
  return (
    <Grid mt="lg">
      {leadId ? (
        <>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Box mt={6}>
              <AuditList
                parentEntityId={leadId}
                parentEntityName="Leads"
                parentEntityIdParam="leadId"
              />
            </Box>
          </Grid.Col>
        </>
      ) : null}
    </Grid>
  );
}
