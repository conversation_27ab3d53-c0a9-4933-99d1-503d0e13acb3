import { BarChart, type ChartTooltipProps } from "@mantine/charts";
import { Box, Center, Paper, Text } from "@mantine/core";
import classes from "./report.module.css";
import { type Statistic } from "../../utils/types";
interface CustomChartTooltipProps extends ChartTooltipProps {
  percentage?: boolean;
}

interface HorizontalBarChartProps {
  data: Statistic[] | undefined;
  title: string;
  marginTop?: number;
  orientation?: "vertical" | "horizontal";
  height?: string;
  width?: string;
  percentage?: boolean;
}
function ChartTooltip({ label, payload, percentage }: CustomChartTooltipProps) {
  if (!payload) return null;

  return (
    <Paper px="md" py="sm" withBorder shadow="md" radius="md">
      {payload.map((item) => (
        <Text key={label?.toString()} fz="h6" fw={700}>
          {label}: {item.value} {percentage && "%"}
        </Text>
      ))}
    </Paper>
  );
}
export default function HorizontalBarChart({
  data,
  title,
  marginTop = 20,
  orientation = "vertical",
  height = "220",
  width = "100%",
  percentage = false,
}: HorizontalBarChartProps) {
  const xKey = "display";
  const yKey = percentage ? "percentage" : "value";
  return (
    <Box
      mt={marginTop}
      style={{ border: "2px solid #e0e0e0", borderRadius: 4 }}
      h={height}
      w={width}
    >
      <Center mt={4}>
        <Text size="sm">{title}</Text>
      </Center>
      <BarChart
        pb={30}
        pt={10}
        pl={10}
        pr={10}
        classNames={{
          root: classes.root,
          bar: classes.bar,
          grid: classes.grid,
        }}
        barChartProps={{
          barCategoryGap: 100,
          maxBarSize: 5,
          barSize: 5,
          barGap: 0,
        }}
        type={"default"}
        h={height}
        data={data!}
        dataKey={xKey}
        orientation={orientation}
        yAxisProps={{ width: 100 }}
        tooltipProps={{
          content: ({ label, payload }) => (
            <ChartTooltip
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              label={label}
              payload={payload}
              percentage={percentage}
            />
          ),
        }}
        barProps={{ radius: 10 }}
        series={[{ name: yKey, color: "blue.6" }]}
      ></BarChart>
    </Box>
  );
}
