import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import {
  Button,
  Paper,
  Stack,
  Text,
  Group,
  Select,
  NumberInput,
  TextInput,
  Divider,
  Card,
  Badge,
  Center,
} from "@mantine/core";
import { useState } from "react";

interface PageDiscountProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

interface DiscountRule {
  id: string;
  name: string;
  type: "percentage" | "fixed";
  value: number;
  description: string;
  conditions?: string;
}

const mockDiscountRules: DiscountRule[] = [
  {
    id: "1",
    name: "First Month Free",
    type: "percentage",
    value: 100,
    description: "100% discount on first month",
    conditions: "New customers only",
  },
  {
    id: "2",
    name: "Student Discount",
    type: "percentage",
    value: 15,
    description: "15% discount for students",
    conditions: "Valid student ID required",
  },
  {
    id: "3",
    name: "Long Term Discount",
    type: "percentage",
    value: 10,
    description: "10% discount for 12+ month contracts",
    conditions: "Minimum 12 months",
  },
  {
    id: "4",
    name: "Loyalty Discount",
    type: "fixed",
    value: 25,
    description: "€25 off for returning customers",
    conditions: "Previous customer",
  },
];

export default function PageDiscount({
  lead: _lead,
  setPages,
  pages,
}: PageDiscountProps) {
  const [selectedDiscountRule, setSelectedDiscountRule] = useState<string>("");
  const [customDiscountType, setCustomDiscountType] = useState<
    "percentage" | "fixed"
  >("percentage");
  const [customDiscountValue, setCustomDiscountValue] = useState<number>(0);
  const [discountReason, setDiscountReason] = useState("");

  const selectedRule = mockDiscountRules.find(
    (rule) => rule.id === selectedDiscountRule,
  );

  const handleNext = () => {
    setPages([...pages, "PRODUCTS"]);
  };

  const discountOptions = mockDiscountRules.map((rule) => ({
    value: rule.id,
    label: rule.name,
  }));

  // Mock calculation values
  const subtotal = 380;
  const discountAmount = selectedRule
    ? selectedRule.type === "percentage"
      ? (subtotal * selectedRule.value) / 100
      : selectedRule.value
    : customDiscountValue > 0
      ? customDiscountType === "percentage"
        ? (subtotal * customDiscountValue) / 100
        : customDiscountValue
      : 0;

  const total = subtotal - discountAmount;

  return (
    <Center>
      <Paper p="xl" radius="md" w={"60vw"} h={"70vh"}>
        <Stack gap="xs">
          <Card withBorder p="xs">
            <Stack gap="md">
              <Text fw={500} size="lg">
                Predefined Discounts
              </Text>

              <Select
                label="Select discount rule"
                placeholder="Choose a discount rule"
                data={discountOptions}
                value={selectedDiscountRule}
                onChange={(value) => {
                  setSelectedDiscountRule(value || "");
                  setCustomDiscountValue(0);
                }}
                clearable
              />

              {selectedRule && (
                <Card withBorder p="sm" bg="blue.0">
                  <Stack gap="xs">
                    <Group justify="space-between">
                      <Text fw={500}>{selectedRule.name}</Text>
                      <Badge color="blue">
                        {selectedRule.type === "percentage"
                          ? `${selectedRule.value}%`
                          : `€${selectedRule.value}`}
                      </Badge>
                    </Group>
                    <Text size="sm" c="dimmed">
                      {selectedRule.description}
                    </Text>
                    {selectedRule.conditions && (
                      <Text size="xs" c="orange">
                        Conditions: {selectedRule.conditions}
                      </Text>
                    )}
                  </Stack>
                </Card>
              )}
            </Stack>
          </Card>
          <Card withBorder p="xs">
            <Stack gap="md">
              <Text fw={500} size="lg">
                Custom Discount
              </Text>

              <Group grow>
                <Select
                  label="Discount type"
                  data={[
                    { value: "percentage", label: "Percentage (%)" },
                    { value: "fixed", label: "Fixed amount (€)" },
                  ]}
                  value={customDiscountType}
                  onChange={(value) =>
                    setCustomDiscountType(value as "percentage" | "fixed")
                  }
                  disabled={!!selectedDiscountRule}
                />

                <NumberInput
                  label="Discount value"
                  value={customDiscountValue}
                  onChange={(value) => {
                    setCustomDiscountValue(Number(value) || 0);
                    setSelectedDiscountRule("");
                  }}
                  min={0}
                  max={customDiscountType === "percentage" ? 100 : subtotal}
                  suffix={customDiscountType === "percentage" ? "%" : "€"}
                  disabled={!!selectedDiscountRule}
                />
              </Group>

              <TextInput
                label="Reason for discount"
                placeholder="Enter reason for custom discount"
                value={discountReason}
                onChange={(event) =>
                  setDiscountReason(event.currentTarget.value)
                }
                disabled={!!selectedDiscountRule}
              />
            </Stack>
          </Card>

          <Card withBorder p="md" bg="gray.0">
            <Stack gap="xs">
              <Text fw={500} size="lg">
                Summary
              </Text>
              <Group justify="space-between">
                <Text>Subtotal</Text>
                <Text>€{subtotal.toFixed(2)}</Text>
              </Group>
              {discountAmount > 0 && (
                <Group justify="space-between" c="red">
                  <Text>Discount</Text>
                  <Text>-€{discountAmount.toFixed(2)}</Text>
                </Group>
              )}
              <Divider />
              <Group justify="space-between">
                <Text fw={600} size="lg">
                  Total
                </Text>
                <Text fw={600} size="lg">
                  €{total.toFixed(2)}
                </Text>
              </Group>
            </Stack>
          </Card>

          <Group justify="end" mt="md">
            <Button onClick={handleNext}>Continue to Products</Button>
          </Group>
        </Stack>
      </Paper>
    </Center>
  );
}
