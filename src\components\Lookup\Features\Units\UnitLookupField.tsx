import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { useTranslation } from "react-i18next";

const ENTITY = "units";
type ENTITY_TYPE = Schemas["UnitRetrieveDto"];

type UnitLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
    businessUnitId?: string;
  };

export function UnitLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  businessUnitId,
  ...props
}: UnitLookupProps) {
  const { t } = useTranslation("features");
  const { searchTerm } = useLookup(
    identifier,
    initial?.unitCode,
    initialId,
    initial,
    true,
  );

  const queryParams: QueryParams = {
    pageSize: 50,
  };
  // Status 0 is Availables
  //queryParams.filter = `(Status == 0)`;
  if (businessUnitId && businessUnitId !== null && businessUnitId !== "") {
    queryParams.filter = ` BusinessUnit.Id == ${businessUnitId}`;
  }
  queryParams.orderBy = "Status";
  queryParams.desc = false;
  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["UnitRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/Units/unitsWithAFOStatus",
      queryKey: "unit" + businessUnitId,
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("units.unitCode")}</Box>
      <Box>{t("prices.pricePerMonth")}</Box>
      <Box>{t("units.volume")}</Box>
      <Box>{t("units.floor")}</Box>
      <Box>{t("units.unitType")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.unitCode, searchTerm, `unitCode_${entity.id}`),
        OptionRender(
          entity.pricePerMonth?.toString(),
          searchTerm,
          `pricePerMonth_${entity.id}`,
        ),
        OptionRender(
          entity.volume?.toString(),
          searchTerm,
          `volume_${entity.id}`,
        ),
        OptionRender(entity.floor, searchTerm, `floor_${entity.id}`),
        OptionRender(
          entity.unitType?.name,
          searchTerm,
          `unitType_${entity.id}`,
        ),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.unitCode!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
