{"ActiveReservationExists": {"message": "An active reservation for this lead already exists."}, "ActivityStateNull": {"message": "Activity must contain Activity State"}, "AfoServerError": {"message": "Afo server error"}, "AttachmentFileMissing": {"message": "There is no file attached."}, "Auth0ServerError": {"message": "Auth0 server error"}, "Auth0TokenError": {"message": "Auth0 token error"}, "BlobDoesNotExists": {"message": "A blob with the name {0} ({1}) does not exist."}, "BlobExists": {"message": "A blob with the name {0} already exists."}, "BusinessMustContainCompanyName": {"message": "Business must contain Company Name"}, "BusinessUnitManagerEmpty": {"message": "Business Unit manager is empty."}, "BusinessUnitRequired": {"message": "Business Unit is required."}, "CalendarBlockingError": {"message": "LeadId & ContactId cannot be set for AppointmentType 'BlockingCalendar'."}, "CallbackDateEarlierThanStart": {"message": "Callback Date cannot be earlier than Start Date."}, "CancelAndReserveAgain": {"message": "There is an active reservation. Cancel Reservation and Reserve Advised Unit again."}, "CaseHasNoEmail": {"message": "Unable to reply: <PERSON> has no emails"}, "ChildCannotBeParent": {"message": "Child record cannot be parent."}, "ClaimNull": {"message": "Claim must contain value"}, "CompanyNameNotProvided": {"message": "Company Name is not provided. Cannot create new Customer."}, "ContactDuplicate": {"message": "Contact with provided email or phone already exists."}, "CustomerContactDuplicate": {"message": "Customer Contact with this role already exists."}, "EmailAddressNotFound": {"message": "Email address not found. Make sure that either contact or lead has email address."}, "EmailMustContainEta": {"message": "Email must contain '@'"}, "EmailSendingError": {"message": "Error sending Email: {0}"}, "EmptyId": {"message": "Id cannot be empty"}, "HtmlRenderingFailed": {"message": "Rendering of template has failed: {0}"}, "HtmlTemplateNotFound": {"message": "Selected HTML template was not found."}, "HtmlTemplateNotValid": {"message": "Selected HTML template has validation errors: {0}"}, "IncorrectDates": {"message": "Appointment cannot end earlier than it starts."}, "IncorrectMailbox": {"message": "Cannot send email from a different mailbox than the original case receiver."}, "IncorrectPatchDoc": {"message": "Passed patch document object is incorrect."}, "InvalidArgument": {"message": "Invalid argument value for {0}"}, "InvalidEnumValueInt": {"message": "Invalid int value {0} for enum property {1}"}, "InvalidEnumValueString": {"message": "Invalid string value {0} for enum property {1}"}, "InvalidParams": {"message": "Invalid parameters provided."}, "InvalidRef": {"message": "Ref Id does not contain a valid GUID."}, "ItemNotFound": {"message": "Item not found: {0}, {1}"}, "LeadIsInactive": {"message": "Lead is inactive."}, "LeadLostNull": {"message": "Loosing a lead must contain a loss reason"}, "LeadLostOpenAppointmentsError": {"message": "Cannot close lead while there are open appointments. Close those and retry."}, "MainContactAlreadyExists": {"message": "Customer can only have one Customer Contact with Main role."}, "MatchingLeadFoundInOmnichannel": {"message": "There is an already existing lead that matches provided details, but is currently being worked on in Omnichannel."}, "MessageBirdError": {"message": "Error sending SMS: {0}"}, "NoMailbox": {"message": "BusinessUnit Mailbox is empty"}, "ProcessStageNull": {"message": "Lead must contain Process Stage"}, "QuoteItemsCannotBeEdited": {"message": "Quote items cannot be edited."}, "QuoteNotFound": {"message": "Quote not found."}, "QuoteOrLeadMustBeSelected": {"message": "Quote or Lead must be selected."}, "SameUnitAlreadySelected": {"message": "Cannot add same Unit twice."}, "SelfReferenceNotAllowed": {"message": "Self reference not allowed."}, "UnexpectedReservationStatusValue": {"message": "Unexpected Reservation Status received from AFO: {0}"}, "UnitUnavailable": {"message": "Unit with Id = {0} (AfoMigrationId = {1}) is unavailable."}, "UserCreated": {"message": "User created successfully"}, "UserExists": {"message": "User with Id = {0} already exists"}, "WalkInLeadError": {"message": "It is not possible to create Walk-In Lead. There are {0} existing Leads for this customer. Please use already existing one."}}