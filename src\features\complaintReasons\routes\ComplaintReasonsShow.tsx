import { PageLoader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  ComplaintReasonsForm,
  type FormSchema,
} from "../components/ComplaintReasonsForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function ComplaintReasonsShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["ComplaintReason"],
    Schemas["ComplaintReasonCreateDto"]
  >({
    resourcePath: "/api/ComplaintReasons/{id}",
    resourceId: id!,
    queryKey: "complaintReason",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/ComplaintReasons";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("complaintReason_" + id);
  };
  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/ComplaintReasons/{id}",
    resourceId: id!,
    queryKey: "complaintReason",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["ComplaintReason"]>({
    resourcePath: "/api/ComplaintReasons/{id}",
    resourceId: id!,
    queryKey: "complaintReason",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <ComplaintReasonsForm
      isCreate={false}
      title={t("complaintReasons.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["ComplaintReasonCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("complaintReasons.delete", { id })}
            modalContent={t("complaintReasons.deleteConfirmation", { id })}
            confirmLabel={t("complaintReasons.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate("/app/ComplaintReasons");
              }
            }}
          />
        </Group>
      }
    />
  );
}
