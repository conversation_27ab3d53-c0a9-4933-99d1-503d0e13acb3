import "@mantine/core/styles.css";
import "@mantine/dates/styles.css";
import "@mantine/notifications/styles.css";
import "mantine-react-table/styles.css";
import "@mantine/charts/styles.css";
import "@mantine/dropzone/styles.css";
import "@/styles/globals.css";
import * as Sentry from "@sentry/react";
import { initPromise as i18nInitPromise } from "./lib/i18n";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";

import { App } from "@/App";
async function initializeApp() {
  await i18nInitPromise;

  if (!process.env.NODE_ENV || process.env.NODE_ENV === "development") {
    //Do nothing
  } else {
    Sentry.init({
      dsn: "https://<EMAIL>/4507498247749712",
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.replayIntegration({
          maskAllText: false,
          blockAllMedia: false,
          maskAllInputs: false,
          networkDetailAllowUrls: [window.location.origin],
        }),
        Sentry.feedbackIntegration({
          showBranging: false,
          autoInject: false,
        }),
      ],
      tracesSampleRate: 1.0,
      tracePropagationTargets: [
        "https://unitlogic.nl",
        "https://unitlogic.nl/*",
        "https://unitlogic.nl/**",
        "https://accept.unitlogic.nl",
        "https://accept.unitlogic.nl/*",
        "https://accept.unitlogic.nl/**",
        "https://white-grass-09646b903.4.azurestaticapps.net",
        "https://white-grass-09646b903.4.azurestaticapps.net/*",
        "https://white-grass-09646b903.4.azurestaticapps.net/**",
      ],
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
    });
  }
  const rootElement = document.getElementById("root");
  if (!rootElement) throw new Error("Failed to find the root element");
  const root = createRoot(rootElement);

  root.render(
    <StrictMode>
      <App />
    </StrictMode>,
  );
}

initializeApp().catch(console.error);
