import { Route, Routes } from "react-router-dom";
import { CustomerContactList } from "./CustomerContactList";
import { CustomerContactShow } from "./CustomerContactShow";
import { CustomerContactCreate } from "./CustomerContactCreate";

export default function CustomerContactsRoutes() {
  return (
    <Routes>
      <Route index element={<CustomerContactList />} />
      <Route path=":id" element={<CustomerContactShow />} />
      <Route path="create" element={<CustomerContactCreate />} />
    </Routes>
  );
}
