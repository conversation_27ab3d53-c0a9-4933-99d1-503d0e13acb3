import { Route, Routes } from "react-router-dom";
import { AdvisedUnitList } from "./AdvisedUnitList";
import { AdvisedUnitShow } from "./AdvisedUnitShow";
import { AdvisedUnitCreate } from "./AdvisedUnitCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<AdvisedUnitList hideCreate />} />
      <Route path=":id" element={<AdvisedUnitShow />} />
      <Route path="create" element={<AdvisedUnitCreate />} />
    </Routes>
  );
}
