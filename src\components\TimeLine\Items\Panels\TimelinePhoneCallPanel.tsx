import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Anchor, Box, Center, Flex, Loader, Stack } from "@mantine/core";
import { DateTime } from "luxon";
import { useTranslation } from "react-i18next";
interface TimelinePhoneCallPanelProps {
  phoneCallId: string;
}

export function TimelinePhoneCallPanel({
  phoneCallId,
}: TimelinePhoneCallPanelProps) {
  const { t } = useTranslation("features");
  const { dateFormat } = useSettingsContext();
  const { data, isLoading } = useEntityQuery<Schemas["PhoneCallRetrieveDto"]>({
    resourcePath: `/api/PhoneCalls/{id}`,
    resourceId: phoneCallId,
    queryKey: ["phoneCall", phoneCallId],
  });

  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  return (
    <Box ml={8}>
      <Stack gap="xs">
        <Flex>
          {t("common.owner")}:
          <Anchor
            href={`/app/appUsers/${data?.ownerId}`}
            style={{ cursor: "pointer" }}
            ml={8}
          >
            {data?.owner?.name ?? data?.owner?.email}
          </Anchor>
        </Flex>
        <Flex>
          {t("common.createdOn")}:
          <Box ml={8}>
            {DateTime.fromJSDate(new Date(data?.createdOn ?? "")).toFormat(
              dateFormat + " HH:mm:ss",
            )}
          </Box>
        </Flex>
        <Flex>
          {t("phoneCalls.startDate")}:
          <Box ml={8}>
            {DateTime.fromJSDate(new Date(data?.startDate ?? "")).toFormat(
              dateFormat + " HH:mm:ss",
            )}
          </Box>
        </Flex>
        {data?.phoneCallType !== "Callback" && (
          <Flex>
            {t("phoneCalls.callbackOn")}:
            <Box ml={8}>
              {DateTime.fromJSDate(new Date(data?.callbackDate ?? "")).isValid
                ? DateTime.fromJSDate(
                    new Date(data?.callbackDate ?? ""),
                  ).toFormat(dateFormat + " HH:mm:ss")
                : "-"}
            </Box>
          </Flex>
        )}

        <Flex>
          {t("phoneCalls.description")}:<Box ml={8}>{data?.description}</Box>
        </Flex>
      </Stack>
    </Box>
  );
}
