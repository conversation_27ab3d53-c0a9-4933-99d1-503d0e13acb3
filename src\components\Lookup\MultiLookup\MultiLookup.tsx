import { type ComboboxOptionProps, type ComboboxStore } from "@mantine/core";
import { type UseFormReturnType } from "node_modules/@mantine/form/lib/types";
import { BaseLookup } from "../Base/BaseLookup";
import { HasLeadBeenChosen } from "./LeadContactFunctionality";
import { useLookup } from "@/components/Lookup/Context/useLookup";

type MultiLookupProps<T> = ReturnType<UseFormReturnType<T>["getInputProps"]> & {
  combobox: ComboboxStore;
  required?: boolean;
  disabled?: boolean;
  entity: string;
  navigateEntity?: string;
  options: (JSX.Element | null)[] | undefined;
  multiLookupOnChange: (value: string, id: string, entity: string) => void;
  header: JSX.Element;
  identifier: string;
  isFetching: boolean;
  currentFocus: number;
};

export function MultiLookup<T>({
  combobox,
  required = false,
  disabled = false,
  options,
  header,
  isFetching,
  entity,
  identifier,
  navigateEntity,
  currentFocus,
  multiLookupOnChange,
  ...props
}: MultiLookupProps<T>) {
  const { setLookupId, setLookupValue, setSearchTerm } = useLookup(identifier);
  return (
    <BaseLookup
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={entity}
      navigateEntity={navigateEntity}
      onOptionSubmit={(value: string, optionProps: ComboboxOptionProps) => {
        combobox.closeDropdown();
        setLookupId(value);
        setLookupValue(optionProps.display as string);
        setSearchTerm("");
        try {
          //currently only available for lead and contact
          const isLead = HasLeadBeenChosen(options ?? [], optionProps.value);
          if (typeof isLead == "boolean") {
            if (isLead)
              multiLookupOnChange(
                optionProps.display as string,
                value,
                "leads",
              );
            if (!isLead)
              multiLookupOnChange(
                optionProps.display as string,
                value,
                "contacts",
              );
          }
        } catch {
          console.warn("was not able to set child lookup");
        }
      }}
      onRightSectionClick={() => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        props.onChange(null);
        setLookupId(null);
        setLookupValue(null);
        setSearchTerm("");
      }}
      {...props}
    />
  );
}
