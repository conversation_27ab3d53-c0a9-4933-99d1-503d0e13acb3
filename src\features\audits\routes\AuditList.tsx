import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { DateRenderer } from "@/components/Table/CellRenderers";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";
import { type MRT_ColumnDef } from "mantine-react-table"; // Import MRT_ColumnDef
import { useEntityListQuery } from "@/features/entity/queries";
import { type BasicEntity } from "@/components/Table/types";

const PATH = "Audits";

// 1. Define the new flattened row structure
interface FlattenedAuditEntry extends BasicEntity {
  uniqueId: string; // Unique key for MRT/React
  createdOn: Schemas["AuditRetrieveDto"]["createdOn"];
  createdBy: Schemas["AuditRetrieveDto"]["createdBy"];
  operation: Schemas["AuditRetrieveDto"]["operation"];
  fieldName: Schemas["AuditEntryRetrieveDto"]["fieldName"];
  oldValue: Schemas["AuditEntryRetrieveDto"]["oldValue"];
  newValue: Schemas["AuditEntryRetrieveDto"]["newValue"];
  // You might need the original audit ID or change ID for actions later
  auditId?: Schemas["AuditRetrieveDto"]["id"];
  changeId?: Schemas["AuditEntryRetrieveDto"]["id"];
}

// 2. Create the data transformation function
// This assumes the data fetched is Schemas["AuditRetrieveDtoPagedList"]
const flattenAuditData = (
  data: Schemas["AuditRetrieveDto"][],
): FlattenedAuditEntry[] => {
  if (data.length === 0) {
    return [];
  }

  const flattened: FlattenedAuditEntry[] = [];
  data.forEach((audit) => {
    // Ensure 'changes' exists and is an array before iterating
    if (audit.changes && Array.isArray(audit.changes)) {
      audit.changes.forEach((change) => {
        flattened.push({
          uniqueId: `${audit.id}-${change.id}`, // Create a unique ID for the row
          auditId: audit.id,
          changeId: change.id,
          createdOn: audit.createdOn,
          createdBy: audit.createdBy,
          operation: audit.operation,
          fieldName: change.fieldName,
          oldValue: change.oldValue,
          newValue: change.newValue,
        });
      });
    }
  });
  return flattened;
};

export function AuditListInner({ resourcePath }: InnerListProps) {
  const { t } = useTranslation(["features", "audits"]); // Add 'audits' namespace if needed
  const { tableRef } = useListCommands();
  const { data, isLoading } = useEntityListQuery<
    Schemas["AuditRetrieveDtoPagedList"]
  >({
    resourcePath: resourcePath as PathKeys,
    queryKey: "audit",
    params: {
      pageSize: 10,
    },
  });

  const rawData = flattenAuditData(data?.data ?? []);

  // Define columns for the *flattened* structure
  // Use MRT_ColumnDef with the correct row type
  const columns: MRT_ColumnDef<Schemas["AuditRetrieveDto"]>[] = [
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer, // DateRenderer should work fine
      size: 120,
    },
    {
      accessorKey: "createdBy",
      header: t("entity.createdBy"),
      ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
      size: 120,
    },
    {
      accessorKey: "operation",
      header: t("audits:audits.operation", "Operation"), // Use specific namespace if defined
      filterVariant: "text",
      size: 100,
    },
    // NEW Columns from the flattened changes
    {
      accessorKey: "fieldName",
      header: t("audits:audits.fieldName", "Field Name"),
      filterVariant: "text",
      size: 150,
    },
    {
      accessorKey: "oldValue",
      header: t("audits:audits.oldValue", "Old Value"),
      enableColumnFilter: false, // Often not useful to filter/sort raw values
      enableSorting: false,
      size: 250,
    },
    {
      accessorKey: "newValue",
      header: t("audits:audits.newValue", "New Value"),
      enableColumnFilter: false, // Often not useful to filter/sort raw values
      enableSorting: false,
      size: 250,
    },
  ];

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["AuditRetrieveDto"],
        Schemas["AuditRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        loading={isLoading}
        resourcePath={resourcePath as PathKeys}
        queryKey="audit"
        rawData={rawData}
        entityPath="audits"
        title={t("features:audits.title")}
        toolbar={<></>}
        redirectTo={window.location.pathname}
        columns={columns}
        enableGrouping={true}
        grouping={["createdOn"]}
        selectionEnabled={false}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function AuditList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath =
    `/api/${PATH}?entityName=${parentEntityName}&id=${parentEntityId}` as PathKeys;
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <AuditListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
