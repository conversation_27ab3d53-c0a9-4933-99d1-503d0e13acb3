import { Box, Text } from "@mantine/core";
import OmnichannelQuery from "./OmnichannelQuery";
import OmnichannelItem from "./OmnichannelItem";
import classes from "../..//Home.module.css";
import { useTranslation } from "react-i18next";

export default function OmnichannelList() {
  const { t } = useTranslation("features");

  const { activeUsers } = OmnichannelQuery();

  if (activeUsers.length == 0) {
    return (
      <Box className={classes.listContainer}>
        <Box className={classes.noListItem}>
          <Text w={"100%"} className={classes.listNoItemsText}>
            {t(`home.noOmnichannelUsers`)}
          </Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box className={classes.listContainer}>
      {activeUsers.map((user, index) => {
        return <OmnichannelItem key={index} user={user} />;
      })}
    </Box>
  );
}
