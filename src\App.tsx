import { AppProvider } from "@/providers/app";
import { router } from "@/routes";
import { RouterProvider } from "react-router-dom";
import { RouteBlockerProvider } from "./hooks/blockerContext";
import { AuthProvider } from "./providers/auth";

export function App() {
  return (
    <AuthProvider>
      <AppProvider>
        <RouteBlockerProvider>
          <RouterProvider router={router} />
        </RouteBlockerProvider>
      </AppProvider>
    </AuthProvider>
  );
}
