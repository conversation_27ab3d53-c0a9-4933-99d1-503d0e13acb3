{"lookup": {"name": "<PERSON><PERSON>", "fullName": "Volledige naam", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Achternaam", "email": "E-mail", "mobile": "Mobiel", "number": "<PERSON><PERSON><PERSON>", "phone": "Telefoon", "code": "Code", "subject": "Onderwerp", "contractId": "Contract-ID", "contractNumber": "Contractnummer", "customer": "<PERSON><PERSON>", "contact": "Contact", "contactRole": "Contactrol", "from": "<PERSON>", "to": "<PERSON><PERSON>", "status": "Status", "lead": "Lead", "type": "Type", "startDate": "Startdatum", "caseNumber": "Zaaknummer", "contactName": "Contactnaam", "customerName": "Klantnaam", "businessUnitName": "<PERSON><PERSON> vestiging"}, "commonActions": {"feedback": "Forum", "newAppointment": "<PERSON><PERSON><PERSON>", "newCase": "Nieuwe Case", "newLead": "Nieuwe Lead"}, "dashboards": {"selectDashboard": "Ki<PERSON> een dashboard"}, "entity": {"save": "Opsla<PERSON>"}, "forbidden": {"description": "Unfortunately, this is only a 403 page. Which means you do not have the right permissions to view this page/resource.", "goBack": "Take me back to home page", "logout": "Logout", "title": "You do not have the right permissions."}, "sentryFeedback": {"formTitle": "Rapporteer een bug", "cancelButtonLabel": "<PERSON><PERSON><PERSON>", "submitButtonLabel": "<PERSON><PERSON><PERSON>", "messageLabel": "Beschrijving", "messagePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> het probleem of geef je feedback...", "emailLabel": "Je e-mail", "successNotificationMessage": "<PERSON>ed<PERSON> ve<PERSON><PERSON><PERSON>", "emailPlaceholder": "e-mail...", "nameLabel": "Je naam", "namePlaceholder": "Naam...", "successMessageText": "Bedankt voor je feedback!"}, "navbar": {"Default": "<PERSON><PERSON><PERSON>", "Sales & Service": "Sales & Service", "Sales & Service Supervisor": "Sales & Service Supervisor", "Store Manager": "Vestigingsmanager", "Management": "Management", "Administrations": "Instellingen", "advisedProducts": "Geadviseerde <PERSON>en", "advisedUnits": "Geadviseerde Units", "appSettings": "App-instellingen", "appUsers": "App-gebruikers", "businessUnits": "Vestigingen", "calendar": "Agenda", "caseComments": "<PERSON> opmerkingen", "caseReasons": "<PERSON><PERSON><PERSON><PERSON>", "cases": "Cases", "complaintReasons": "Klachtredenen", "connections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contactRoles": "Contactrollen", "contacts": "<PERSON><PERSON>", "contractLines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contractManagement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contracts": "Contracten", "countries": "Landen", "customers": "Klanten", "dashboard": "Dashboard", "dashboards": "Dashboards", "home": "Home", "htmlTemplates": "HTML-sjablonen", "leads": "Leads", "locations": "Locaties", "lossReasons": "Verliesredenen", "mailboxes": "E-Mailboxen ", "omnichannel": "Omnichannel", "originCategories": "Bron Categorieën", "origins": "<PERSON><PERSON>", "products": "<PERSON><PERSON>", "quotes": "Offertes", "refunds": "Retouren", "reportBug": "Rapporteer een bug", "reservations": "Reserveringen", "roles": "<PERSON><PERSON>", "security": "Beveiliging", "service": "Service", "settings": "Instellingen", "storageTypes": "Opslagtypes", "tags": "Tags", "unitTypes": "Unittypes", "units": "Units", "userGroups": "Gebruikersgroep", "work": "Werk", "rentableItems": "Huurbare items", "dataImport": "Gegevensimport"}, "notFound": {"description": "<PERSON><PERSON><PERSON> is dit een 404-pagina. U heeft mogelijk het adres verkeerd getypt, of de pagina is verplaatst naar een andere URL.", "goBack": "Terug naar de startpagina", "logout": "Logout", "title": "U heeft een geheime plek gevonden."}, "userMenu": {"application": "Applicatie", "feedback": "Feedback forum", "language": "Taal", "logout": "Uitloggen"}, "validation": {"invalidEmail": "Ongeldig e-mailadres", "invalidPhone": "Ongeldig telefoonnummer", "invalidBusinessUnit": "Ongeldig bedrijfs eenheid", "invalidLeadSource": "Leadbron is verplicht", "requiredLeadName": "Voornaam en achternaam zijn verp<PERSON>t", "requiredCompanyName": "Bed<PERSON>jfs<PERSON><PERSON> is verplicht", "emailRequired": "E-mail is verplicht", "phoneRequired": "Telefoonnummer is verplicht"}}