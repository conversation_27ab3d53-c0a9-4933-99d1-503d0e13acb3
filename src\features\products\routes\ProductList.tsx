import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import {
  Date<PERSON>ender<PERSON>,
  CurrencyRenderer,
} from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { BooleanRenderer } from "@/components/Table/CellRenderers/BooleanRenderer";

const PATH = "Products";
export function ProductListInner({ resourcePath, createPath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["ProductRetrieveDto"],
        Schemas["ProductRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="product"
        entityPath="products"
        title={t("products.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "name",
            header: t("products.name"),
            filterVariant: "text",
          },
          {
            accessorKey: "price",
            header: t("products.price"),
            filterVariant: "range",
            Cell: CurrencyRenderer,
          },
          {
            accessorKey: "allowPriceChange",
            header: t("products.allowPriceChange"),
            Cell: BooleanRenderer,
            filterVariant: "multi-select",
            mantineFilterSelectProps: {
              data: [
                { value: "true", label: t("yes") },
                { value: "false", label: t("no") },
              ],
            },
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function ProductList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <ProductListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
