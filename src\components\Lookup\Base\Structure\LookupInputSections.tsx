import { Anchor, Center, CloseButton, Combobox } from "@mantine/core";
import { IconEye } from "@tabler/icons-react";
import classes from "../Lookup.module.css";

export function LookupLeftSection({
  lookupId,
  entity,
  navigateEntity,
}: {
  lookupId: string | null;
  entity: string | undefined;
  navigateEntity?: string;
}) {
  return (
    <Anchor
      underline="always"
      target="_blank"
      href={`${document.location.origin}/app/${navigateEntity ?? entity}/${lookupId}?redirectTo=${document.location.pathname}`}
      className={classes.eyeAnchor}
    >
      <Center>
        <IconEye />
      </Center>
    </Anchor>
  );
}

export function LookupRightSection({
  disabled,
  lookupId,
  onClick,
}: {
  disabled: boolean;
  lookupId: string | null;
  onClick: () => void;
}) {
  if (!lookupId) return <Combobox.Chevron />;

  return (
    <CloseButton
      size="sm"
      onMouseDown={(event) => event.preventDefault()}
      disabled={disabled}
      onClick={onClick}
      aria-label="Clear value"
    />
  );
}
