import { useState, useEffect } from "react";
import { Button, Center, Modal, Text } from "@mantine/core";
import * as signalR from "@microsoft/signalr";
import { useAuth0 } from "@auth0/auth0-react";
import { useEntityPostMutation } from "@/features/entity/mutations";
import { type Schemas } from "@/types";
import { notifications } from "@mantine/notifications";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";

interface LeadListenersProps {
  leadId?: string;
  lossReason: string | null;
  phoneCallStatus?: "Reached" | "NotReached";
}

export default function LeadListeners({
  leadId,
  lossReason,
  phoneCallStatus,
}: LeadListenersProps) {
  const [modalOpened, setModalOpened] = useState(false);
  const queryCache = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const { getAccessTokenSilently } = useAuth0();
  const { t } = useTranslation("features");
  const { mutate: loseLead } = useEntityPostMutation<
    Schemas["Lead"],
    Schemas["LeadLoseDto"]
  >({
    resourcePath: "/api/Leads/loseLead",
    queryKey: "lead_" + leadId!,
  });
  useEffect(() => {
    const connection = new signalR.HubConnectionBuilder()
      .withUrl(`${import.meta.env.VITE_PUBLIC_API_URL}/api/mainHub`, {
        withCredentials: true,
        transport: signalR.HttpTransportType.LongPolling,
        accessTokenFactory: async () => {
          const token = await getAccessTokenSilently();
          return token;
        },
      })
      .withAutomaticReconnect()
      .build();

    connection.on("OpenAppointmentsOnLeadLost", () => {
      setModalOpened(true);
    });

    connection
      .start()
      .catch((err) => console.error("SignalR Connection Error: ", err));

    return () => {
      void connection.stop();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onConfirm = () => {
    loseLead(
      {
        leadId: leadId!,
        lossReasonId: lossReason!,
        phoneCallStatus: phoneCallStatus,
        cancelAllOpenAppointments: true,
      },
      {
        onSuccess: async () => {
          notifications.show({
            color: "green",
            title: t("leads.leadLossSuccessTitle"),
            message: t("leads.leadLossSuccessMessage"),
          });
          setIsLoading(false);
          await queryCache.invalidateQueries("lead_" + leadId);
          close();
        },
        onError: () => {
          notifications.show({
            color: "green",
            title: t("leads.leadLossErrorTitle"),
            message: t("leads.leadLossErrorMessage"),
          });
          setIsLoading(false);
          close();
        },
      },
    );
  };

  return (
    <Modal
      opened={modalOpened}
      centered
      size={"lg"}
      closeOnClickOutside={false}
      withCloseButton={false}
      onClose={() => setModalOpened(false)}
    >
      <Center>
        <Text fz={18} fw={600} mb={16}>
          {t("features:leads.leadLossWarning")}
        </Text>
      </Center>
      <Text mb={16}>{t("features:leads.leadLossMessage")}</Text>
      <Center w={"100%"} mt={48}>
        <Button
          loading={isLoading}
          w={"25%"}
          color="red"
          onClick={() => {
            setIsLoading(true);
            onConfirm();
          }}
        >
          {t("features:leads.leadLossConfirm")}
        </Button>
        <Button
          variant="outline"
          loading={isLoading}
          w={"25%"}
          ml={32}
          onClick={() => {
            setModalOpened(false);
          }}
        >
          {t("features:leads.leadLossExit")}
        </Button>
      </Center>
    </Modal>
  );
}
