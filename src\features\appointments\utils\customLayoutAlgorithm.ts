import { type Schemas } from "@/types";

type EventData = Schemas["Appointment"];

interface SlotMetrics {
  getRange: (
    start: Date,
    end: Date,
  ) => {
    start: number;
    startDate: Date;
    end: number;
    endDate: Date;
    top: number;
    height: number;
  };
}

interface Accessors {
  start: (event: EventData) => Date;
  end: (event: EventData) => Date;
  titleAccessor?: (event: EventData) => string;
  allDayAccessor?: (event: EventData) => boolean;
}

class Event {
  start: number;
  end: number;
  top: number;
  height: number;
  data: EventData;
  rows?: Event[];
  leaves?: Event[];
  container?: Event;
  row?: Event;
  idx?: number;
  size?: number;
  friends?: Event[];
  lastInRow?: boolean = false;
  clusterIndex?: number;
  smallestSize?: number;

  constructor(
    data: EventData,
    {
      accessors,
      slotMetrics,
    }: { accessors: Accessors; slotMetrics: SlotMetrics },
  ) {
    const { start, end, top, height } = slotMetrics.getRange(
      accessors.start(data),
      accessors.end(data),
    );

    this.start = start;
    this.end = end;
    this.top = top;
    this.height = height;
    this.data = data;
  }

  get _width(): number {
    if (this.rows) {
      const columns =
        this.rows.reduce(
          (max, row) => Math.max(max, (row.leaves?.length ?? 0) + 1),
          0,
        ) + 1;
      return 100 / columns;
    }

    if (this.leaves) {
      const availableWidth = 100 - this.container!._width;
      return availableWidth / (this.leaves.length + 1);
    }

    return this.row!._width;
  }

  get width(): number {
    const overlap = Math.min(100, this.size! * 1.7);
    const noOverlap = this.container ? this.size ?? 0 : overlap;

    if (this.lastInRow && this.leaves && this.leaves.length === 0) {
      return noOverlap;
    }

    if (this.lastInRow && this.leaves) {
      return overlap;
    }

    if (this.rows && !this.lastInRow) {
      return overlap;
    }

    if (this.leaves && !this.lastInRow) {
      return overlap;
    }

    if (this.lastInRow && !this.leaves) {
      return noOverlap;
    }

    if (!this.container) {
      return noOverlap;
    }

    if (!this.rows && !this.leaves && !this.lastInRow) {
      return overlap;
    }

    const leaves = this.row!.leaves;
    const index = leaves!.indexOf(this);
    return index === leaves!.length - 1 ? noOverlap : overlap;
  }

  get xOffset(): number {
    if (this.rows) return 0;
    if (this.leaves) return this.container!._width;

    const { leaves, xOffset, _width } = this.row!;
    const index = (leaves?.indexOf(this) ?? 0) + 1;
    return xOffset + (index - 1) * _width;
  }
}

/**
 * Return true if event a and b are considered to be on the same row.
 */
function onSameRow(
  a: Event,
  b: Event,
  minimumStartDifference: number,
): boolean {
  return (
    Math.abs(b.start - a.start) < minimumStartDifference ||
    (b.start >= a.start && b.start < a.end)
  );
}

function sortByRender(events: Event[]): Event[] {
  // Create a new array and copy events to avoid mutating the original
  const sortedByTime = [...events];

  sortedByTime.sort((a, b) => {
    // Primary sort: by start time (ascending)
    if (a.start !== b.start) return a.start - b.start;
    // Secondary sort: by end time (descending)
    return b.end - a.end;
  });

  // Create a new array and push sorted events into it
  const sortedEvents: Event[] = [];
  for (const event of sortedByTime) {
    sortedEvents.push(event);
  }

  return sortedEvents;
}

function setFriends(events: Event[]): void {
  for (let i = 0; i < events.length; i++) {
    for (let j = i + 1; j < events.length; j++) {
      const e1 = events[i];
      const e2 = events[j];

      if (!e1 || !e2) continue; // Ensure neither is undefined

      // Ensure both friends arrays exist
      e1.friends = e1.friends || [];
      e2.friends = e2.friends || [];

      if (e2.top < e1.top + e1.height && e2.top + e2.height > e1.top) {
        e1.friends.push(e2);
        e2.friends.push(e1);
      }
    }
  }
}

function setIdx(events: Event[]): void {
  //set up idx
  for (const event of events) {
    const bitmap = Array(100).fill(1);
    for (const friend of event.friends || []) {
      if (friend.idx !== undefined) bitmap[friend.idx] = 0;
    }
    event.idx = bitmap.indexOf(1);
  }
}
function setSize(events: Event[]): void {
  for (const event of events) {
    if (event.size) continue;
    const allFriends: Event[] = [];
    const maxIdx = getMaxIdxDFS(event, 0, allFriends);
    const size = 100 / (maxIdx + 1);
    event.size = size;
    allFriends.forEach((f) => (f.size = size));
  }
}

function getMaxIdxDFS(node: Event, maxIdx: number, visited: Event[]): number {
  for (const friend of node.friends || []) {
    if (visited.includes(friend)) continue;
    maxIdx = Math.max(maxIdx, friend.idx ?? 0);
    visited.push(friend);
    maxIdx = Math.max(maxIdx, getMaxIdxDFS(friend, maxIdx, visited));
  }
  return maxIdx;
}

//set clusters
function assignClusterIndices(events: Event[]): void {
  let clusterIndex = 0;
  const visited = new Set<Event>();

  function dfs(event: Event, currentCluster: number) {
    if (visited.has(event)) return;
    visited.add(event);
    event.clusterIndex = currentCluster; // Assign cluster index

    for (const other of events) {
      if (other !== event && eventsOverlap(event, other)) {
        dfs(other, currentCluster);
      }
    }
  }

  for (const event of events) {
    if (!visited.has(event)) {
      dfs(event, clusterIndex);
      clusterIndex++; // Move to next cluster after finishing one
    }
  }
}

// Helper function to check if two events overlap
function eventsOverlap(a: Event, b: Event): boolean {
  return a.start < b.end && b.start < a.end;
}

//group events by cluster index
function groupEventsByCluster(events: Event[]): Event[][] {
  const clusters = new Map<number, Event[]>();

  for (const event of events) {
    const clusterIndex = event.clusterIndex ?? -1; // Assign a default if undefined

    if (!clusters.has(clusterIndex)) {
      clusters.set(clusterIndex, []);
    }
    clusters.get(clusterIndex)!.push(event);
  }

  return Array.from(clusters.values());
}

function findSmallestEventSize(events: Event[]): void {
  let minSize: number | null = null;

  // Find the smallest event size
  for (const event of events) {
    if (event.size !== undefined) {
      if (minSize === null || event.size < minSize) {
        minSize = event.size;
      }
    }
  }

  // Assign `minSize` to all events
  if (minSize !== null) {
    for (const event of events) {
      event.smallestSize = minSize; // Ensure `Event` type includes `minSize`
    }
  }
}

function setEventsSize(events: Event[]): void {
  //set size of events
  for (const event of events) {
    const maxFriendIdx = Math.max(
      ...(event.friends?.map((f) => f.idx ?? 0) || [0]),
    );
    if (maxFriendIdx <= (event.idx ?? 0)) {
      event.size = 100 - (event.idx ?? 0) * event.size!;
    }
  }
}

function flagLastInRowEvents(events: Event[]): void {
  const groupedByTop: Record<number, Event[]> = {};
  //const lastEventFromPreviousGroup: Event | null = null;
  // Group events by their `top` value
  for (const event of events) {
    const topKey = event.top;
    if (!groupedByTop[topKey]) {
      groupedByTop[topKey] = [];
    }
    groupedByTop[topKey]!.push(event);
  }

  // Assign `lastInRow` flag to the last event in each group
  for (const top in groupedByTop) {
    const events = groupedByTop[top];
    if (events && events.length > 0) {
      const lastEvent = events[events.length - 1]; // This is guaranteed to exist.
      if (!lastEvent) continue;
      const friends = lastEvent?.friends;
      if (!friends) continue;
      if (friends?.length > 0) {
        const maxIdx = Math.max(...friends.map((f) => f.idx! ?? 0));
        if (lastEvent.idx! >= maxIdx) {
          lastEvent.lastInRow = true;
        } else {
          lastEvent.lastInRow = false;
        }
      } else {
        lastEvent.lastInRow = true;
      }
    }
  }
}

function setEventRows(events: Event[], minimumStartDifference: number): void {
  //group events by clusters and then perform calculations on each cluster separately, in the end merge them
  const containerEvents: Event[] = [];

  //goes through all events on proxy(day events)
  //set up container, rows and leaves
  for (const event of events) {
    event.rows = [];
    event.leaves = [];
    const container = containerEvents.find(
      (c) =>
        c.end > event.start ||
        Math.abs(event.start - c.start) < minimumStartDifference,
    );

    //if container did not exist current event is container
    if (!container) {
      containerEvents.push(event);
      continue;
    }

    event.container = container;
    let row = null;

    for (const containerRow of container.rows!.reverse()) {
      if (onSameRow(containerRow, event, minimumStartDifference)) {
        row = containerRow;
        break;
      }
    }

    if (row) {
      event.row = row;
      row.leaves!.push(event);
    } else {
      container.rows!.push(event);
    }
  }
}

/**
 * Main function to return styled events.
 */
export default function getStyledEvents({
  events,
  minimumStartDifference,
  slotMetrics,
  accessors,
}: {
  events: EventData[];
  minimumStartDifference: number;
  slotMetrics: SlotMetrics;
  accessors: Accessors;
}): {
  event: EventData;
  style: { top: number; height: number; width: string; xOffset: string };
}[] {
  // Create proxy events and order them so that we don't have to fiddle with z-indexes.
  const proxies = events.map(
    (event: EventData) => new Event(event, { slotMetrics, accessors }), // Explicitly typing the event
  );
  const eventsInRenderOrder = sortByRender(proxies);

  //set clusters of events that overlap each other
  assignClusterIndices(eventsInRenderOrder);
  const eventsInClusters = groupEventsByCluster(eventsInRenderOrder);

  for (const events of eventsInClusters) {
    setFriends(events);
    setIdx(events);
    setSize(events); //sets event size based on how many columns exists (maxIdx)
    setEventsSize(events); // ?? needed to expand events to full width
    findSmallestEventSize(events); // required to calculate offset
    setEventRows(events, minimumStartDifference); // required to calculate widths of events
    flagLastInRowEvents(events); // required to calculate width of last event in row
  }
  // Return the original events, along with their styles.
  return eventsInRenderOrder.map((event) => ({
    event: event.data,
    style: {
      top: event.top,
      height: event.height,
      width: `calc(${event.width}%)`, // Example with padding applied
      xOffset: `calc(${event.idx! * event.smallestSize!}% + 1px)`, // Example with padding applied
    },
  }));
}
