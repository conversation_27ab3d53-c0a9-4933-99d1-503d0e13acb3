import { Box, Text, Center, Stack, Button, Grid } from "@mantine/core";
import {
  IconCalendarBolt,
  IconCalendarTime,
  IconUserCancel,
  IconUserQuestion,
} from "@tabler/icons-react";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../Common/Common";
import classes from "../NoShow.module.css";
import {
  useEntityCreateMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { type Schemas } from "@/types";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import WizardCard from "../Common/WizardCard";
import AppointmentWizardItem from "../Common/AppointmentWizardItem";
import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";

interface PageShowUpProps extends PageProps<PageName> {
  appointment?: Schemas["AppointmentRetrieveDto"];
  lead?: Schemas["LeadRetrieveDto"];
}

export default function PageShowUp({
  setPages,
  pages,
  appointment,
  lead,
}: PageShowUpProps) {
  const { t } = useTranslation("features");
  const queryCache = useQueryClient();

  const { mutate: createCall } = useEntityCreateMutation<
    Schemas["PhoneCall"],
    Schemas["PhoneCallCreateDto"]
  >({ resourcePath: "/api/PhoneCalls", queryKey: "phoneCall" });

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Appointment"],
    Schemas["AppointmentPatchDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: appointment?.id ?? "",
    queryKey: "appointment",
  });

  const onConfirmClick = () => {
    update(
      {
        appointmentStatus: "NoShow",
      },
      {
        onSuccess: () => {
          void queryCache.invalidateQueries("lead_" + appointment?.leadId);
          createCall(
            {
              leadId: lead?.id,
              phoneCallStatus: "NotReached",
              phoneCallType: "PhoneCall",
              startDate: new Date().toISOString().replace("Z", ""),
              callbackDate: new Date(new Date().getTime() + 4 * 60 * 60 * 1000)
                .toISOString()
                .replace("Z", ""),
              endDate: new Date().toISOString().replace("Z", ""),
              isAutoCreated: true,
            },
            {
              onSuccess: () => {
                void queryCache.invalidateQueries(
                  "lead_" + appointment?.leadId,
                );
                notifications.show({
                  color: "green",
                  title: "Appointment updated",
                  message: "Appointment updated",
                });
                setPages([...pages, "COMPLETED"]);
                setIsLoading(false);
              },
              onError: (error) => {
                setIsLoading(false);
                console.error(error);
              },
            },
          );
        },
        onError: (error) => {
          setIsLoading(false);
          console.error(error);
        },
      },
    );
  };
  return (
    <Box>
      <Center>
        <Text fz={16} fw={600} c={"#282828"}>
          {t("wizards.NoShow.Title")}
        </Text>
      </Center>
      <Center>
        <Text fz={10} fw={300} c={"#ADADAD"}>
          {t("wizards.NoShow.Label")}
        </Text>
      </Center>
      <Center mt={32}>
        <AppointmentWizardItem appointment={appointment} />
      </Center>
      <Center>
        <Grid
          justify="center"
          grow
          w={"100%"}
          p={48}
          ml={48}
          mr={48}
          gutter={24}
        >
          <Grid.Col span={{ base: 3, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}>
            <WizardCard
              isLoading={isLoading}
              pages={pages}
              setPages={setPages}
              pageName={"UPDATE"}
              headerText={t("wizards.NoShow.UpdateStartTimeTitle")}
              headerIcon={<IconCalendarBolt size={40} />}
              descriptionText={t("wizards.NoShow.UpdateStartTimeDescription")}
              footerText={t("wizards.NoShow.UpdateStartTimeFooter")}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 3, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}>
            <WizardCard
              isLoading={isLoading}
              pages={pages}
              setPages={setPages}
              pageName={"RESCHEDULE"}
              headerText={t("wizards.NoShow.RescheduleTitle")}
              headerIcon={<IconCalendarTime size={40} />}
              descriptionText={t("wizards.NoShow.RescheduleDescription")}
              footerText={t("wizards.NoShow.RescheduleFooter")}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 3, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}>
            <WizardCard
              isLoading={isLoading}
              pages={pages}
              setPages={setPages}
              pageName={"CANCEL"}
              headerText={t("wizards.NoShow.CancelTitle")}
              headerIcon={<IconUserCancel size={40} />}
              descriptionText={t("wizards.NoShow.CancelDescription")}
              footerText={t("wizards.NoShow.CancelFooter")}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 3, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}>
            <Box className={classes.wizardButtonConfirm}>
              <Stack align="stretch" justify="center" gap="md">
                <Center>
                  <IconUserQuestion size={40} />
                </Center>
                <Text fz={24} ta="center" fw={600} c="#282828">
                  {t("wizards.NoShow.NotReachedTitle")}
                </Text>
                <Text fz={12} fw={400} c="#282828">
                  {t("wizards.NoShow.NotReachedDescription")}
                </Text>
                <Text fz={10} fw={600} c="#ADADAD">
                  {t("wizards.NoShow.NotReachedFooter")}
                </Text>
                <Center>
                  <Button
                    variant="light"
                    loading={isLoading}
                    w={"75%"}
                    m={8}
                    style={{ borderRadius: 8 }}
                    onClick={() => {
                      setIsLoading(true);
                      onConfirmClick();
                    }}
                  >
                    {t("wizards.NoShow.NotReachedConfirmButton")}
                  </Button>
                </Center>
              </Stack>
            </Box>
          </Grid.Col>
        </Grid>
      </Center>
    </Box>
  );
}
