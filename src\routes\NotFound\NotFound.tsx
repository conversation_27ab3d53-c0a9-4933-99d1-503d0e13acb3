import { Title, Text, Button, Container, Group } from "@mantine/core";
import classes from "./NotFound.module.css";
import { useTranslation } from "react-i18next";

export function NotFound() {
  const { t } = useTranslation("common");

  return (
    <Container className={classes.root}>
      <div
        className={classes.label}
        style={{ fontFamily: "var(--mantine-font-family-headings" }}
      >
        404
      </div>
      <Title
        style={{ fontFamily: "var(--mantine-font-family-headings" }}
        className={classes.title}
      >
        {t("notFound.title")}
      </Title>
      <Text c="dimmed" size="lg" ta="center" className={classes.description}>
        {t("notFound.description")}
      </Text>
      <Group justify="center">
        <Button
          variant="subtle"
          size="md"
          onClick={() => {
            window.open("/app/home", "_self");
          }}
        >
          {t("notFound.goBack")}
        </Button>
      </Group>
    </Container>
  );
}
