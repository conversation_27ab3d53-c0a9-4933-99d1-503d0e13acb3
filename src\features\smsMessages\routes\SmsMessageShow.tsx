import { useEntityDeleteMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { SmsMessageForm, type FormSchema } from "../components/SmsMessageForm";
import { filterFalsyValues } from "@/utils/filters";
import { Loader } from "@mantine/core";
import { useParams } from "react-router-dom";

interface SmsMessageShowProps {
  refreshForm?: () => void;
  closeModal?: () => void;
  smsMessageId?: string | null;
}

export function SmsMessageShow({
  refreshForm,
  closeModal,
  smsMessageId,
}: SmsMessageShowProps) {
  const { id } = useParams<{ id: string }>();
  const idToUse = smsMessageId ?? id;
  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["SmsMessageRetrieveDto"]>({
    resourcePath: "/api/SmsMessages/{id}",
    resourceId: idToUse!,
    queryKey: "smsMessage",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/SmsMessages/{id}",
    resourceId: idToUse!,
    queryKey: "smsMessage",
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <SmsMessageForm
      isCreate={false}
      closeModal={closeModal}
      initialValues={
        filterFalsyValues({
          ...data,
        }) as FormSchema
      }
      onDelete={async () => {
        await mutateAsync();
        if (!isDeleteError) {
          if (refreshForm) {
            refreshForm();
          }
        }
      }}
    />
  );
}
