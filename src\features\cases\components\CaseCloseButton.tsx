import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { type Schemas } from "@/types";
import { Text } from "@mantine/core";
import { modals } from "@mantine/modals";
import { notifications } from "@mantine/notifications";
import {
  IconSquareRoundedMinus,
  IconSquareRoundedPlus,
} from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

interface CaseCloseButtonProps {
  caseId: string;
  close: boolean;
}

export default function CaseCloseButton({
  caseId,
  close,
}: CaseCloseButtonProps) {
  const { t } = useTranslation("features");

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Case"],
    Schemas["CasePatchDto"]
  >({
    resourcePath: "/api/Cases/{id}",
    resourceId: caseId,
    queryKey: "case",
  });
  const onConfirm = () => {
    update(
      {
        status: close ? "Closed" : "Reopened",
      },
      {
        onSuccess: () => {
          notifications.show({
            color: "green",
            title: t("notifications.updateSuccessTitle"),
            message: t("notifications.updateSuccessMessage"),
          });
        },
      },
    );
  };

  const openCloseModal = () => {
    modals.openConfirmModal({
      withCloseButton: false,
      centered: true,
      children: (
        <Text size="sm">
          {t(
            close
              ? "cases.caseCloseConfirmation"
              : "cases.caseOpenConfirmation",
          )}
        </Text>
      ),
      labels: {
        confirm: t(close ? "cases.caseCloseConfirm" : "cases.caseOpenConfirm"),
        cancel: t("cases.caseCloseCancel"),
      },
      confirmProps: { color: "red" },
      onConfirm: onConfirm,
    });
  };
  return (
    <ButtonMain
      label={t(close ? "cases.caseClose" : "cases.caseOpen")}
      icon={
        close ? (
          <IconSquareRoundedMinus size={18} />
        ) : (
          <IconSquareRoundedPlus size={18} />
        )
      }
      onClick={openCloseModal}
    />
  );
}
