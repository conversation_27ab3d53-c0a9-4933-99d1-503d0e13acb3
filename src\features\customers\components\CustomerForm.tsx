import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Box, Grid, Paper, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import validator from "validator";
import i18next from "i18next";
import { getDirtyFormFields } from "@/features/entity/utils";
import { CustomerContactList } from "@/features/customerContacts/routes/CustomerContactList";
import { CountryLookup } from "@/components/Lookup/Features/Countries/CountryLookup";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";

const formSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  website: z.string(),
  email: z.string().refine(
    (value) => {
      if (value) {
        return validator.isEmail(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidEmail") },
  ),
  mobile: z.string().refine(
    (value) => {
      if (value) {
        return validator.isMobilePhone(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidPhone") },
  ),
  phone: z.string().refine(
    (value) => {
      if (value) {
        return validator.isMobilePhone(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidPhone") },
  ),
  fax: z.string(),
  city: z.string(),
  zip: z.string(),
  street: z.string(),
  countryId: z.string().nullable(),
  country: z.object({}).nullable(),
  parentCustomerId: z.string().nullable(),
  parentCustomer: z.object({}).nullable(),
});

type FormSchema = z.infer<typeof formSchema>;

interface CustomerFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  isCreate: boolean;
}

export function CustomerForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: CustomerFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      name: initialValues?.name ?? "",
      website: initialValues?.website ?? "",
      email: initialValues?.email ?? "",
      mobile: initialValues?.mobile ?? "",
      phone: initialValues?.phone ?? "",
      fax: initialValues?.fax ?? "",
      city: initialValues?.city ?? "",
      zip: initialValues?.zip ?? "",
      street: initialValues?.street ?? "",
      country: initialValues?.country ?? null,
      countryId: initialValues?.countryId ?? "",
      parentCustomer: initialValues?.parentCustomer ?? null,
      parentCustomerId: initialValues?.parentCustomerId ?? "",
    },
    validate: zodResolver(formSchema),
  });
  const formValues = form.getValues();
  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="xs" pt="">
              <TextInput
                required
                label={t("customers.name")}
                {...form.getInputProps("name")}
              />
              <TextInput
                label={t("customers.website")}
                {...form.getInputProps("website")}
              />
              <TextInput
                label={t("customers.email")}
                {...form.getInputProps("email")}
              />
              <TextInput
                required
                label={t("customers.mobile")}
                {...form.getInputProps("mobile")}
              />
              <TextInput
                label={t("customers.phone")}
                {...form.getInputProps("phone")}
              />
              <TextInput
                label={t("customers.fax")}
                {...form.getInputProps("fax")}
              />
              <TextInput
                label={t("customers.city")}
                {...form.getInputProps("city")}
              />
              <TextInput
                label={t("customers.zip")}
                {...form.getInputProps("zip")}
              />
              <TextInput
                label={t("customers.street")}
                {...form.getInputProps("street")}
              />
              <CountryLookup
                label={t("customers.country")}
                initial={formValues?.country}
                initialId={formValues?.countryId}
                identifier="countryIdCustomer"
                {...form.getInputProps("countryId")}
                shiftLeft="23vw"
              />
              <TextInput
                label={t("customers.afoMigrationId")}
                {...form.getInputProps("afoMigrationId")}
              />
              <CustomerLookup
                label={t("customers.parentCustomer")}
                initial={form.getValues().parentCustomer}
                initialId={form.getValues().parentCustomerId}
                identifier="parentCustomerIdCustomer"
                {...form.getInputProps("parentCustomerId")}
              />
            </Paper>
          </Grid.Col>
          {initialValues?.id ? (
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Box mb={6}>
                <CustomerContactList
                  parentEntityId={initialValues?.id}
                  parentEntityName="customers"
                  parentEntityIdParam="customerId"
                  visibleColumns={["contact", "contactRole"]}
                />
              </Box>
            </Grid.Col>
          ) : null}
        </Grid>
      </EntityLayout>
    </form>
  );
}
