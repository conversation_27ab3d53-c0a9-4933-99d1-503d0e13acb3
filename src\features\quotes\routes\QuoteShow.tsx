import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { QuoteForm, type FormSchema } from "../components/QuoteForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function QuoteShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<Schemas["QuotePatchDto"]>({
    resourcePath: "/api/Quotes/{id}",
    resourceId: id!,
    queryKey: "quote",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/quotes";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("quote_" + id);
  };

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Quote"]>({
    resourcePath: "/api/Quotes/{id}",
    resourceId: id!,
    queryKey: "quote",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <QuoteForm
      recordState={data.status}
      isCreate={false}
      title={t("quotes.showTitle", { id })}
      initialValues={
        filterFalsyValues({
          ...data,
        }) as FormSchema
      }
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(
          { ...filteredValues },
          {
            onSuccess: () => {
              if (close) {
                navigate(redirectTo);
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.updateSuccessTitle"),
                  message: t("notifications.updateSuccessMessage"),
                });
              }
            },
          },
        );
      }}
      actionSection={
        <Group>
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
