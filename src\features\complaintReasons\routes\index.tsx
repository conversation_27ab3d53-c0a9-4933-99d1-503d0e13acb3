import { Route, Routes } from "react-router-dom";
import { ComplaintReasonsShow } from "./ComplaintReasonsShow";
import { ComplaintReasonsCreate } from "./ComplaintReasonsCreate";
import { ComplaintReasonsList } from "./ComplaintReasonsList";

export default function ComplaintReasonsRoutes() {
  return (
    <Routes>
      <Route index element={<ComplaintReasonsList />} />
      <Route path=":id" element={<ComplaintReasonsShow />} />
      <Route path="create" element={<ComplaintReasonsCreate />} />
    </Routes>
  );
}
