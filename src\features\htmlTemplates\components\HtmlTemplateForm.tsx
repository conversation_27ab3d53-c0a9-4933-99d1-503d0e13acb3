import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Flex, Tabs } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { useState, useEffect } from "react";
import { getDirtyFormFields } from "@/features/entity/utils";
import { useNavigate, useParams } from "react-router-dom";
import { HtmlTemplateContentGrid } from "./HtmlTemplateContentGrid";
import { TemplateType } from "@/types/enums";

const formSchema = z.object({
  name: z.string().min(1),
  html: z.string().nullable().optional(),
  htmlNL: z.string().nullable().optional(),
  subjectNL: z.string().nullable().optional(),
  subjectEN: z.string().nullable().optional(),
  replyTo: z.string().nullable().optional(),
  templateType: z.enum(TemplateType as [string]),
});

export type FormSchema = z.infer<typeof formSchema>;

interface HtmlTemplateFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  isCreate: boolean;
}

export function HtmlTemplateForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: HtmlTemplateFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const { id, tabValue } = useParams();
  const navigate = useNavigate();

  const form = useForm<FormSchema>({
    initialValues: {
      name: initialValues?.name ?? "",
      html: initialValues?.html ?? null,
      htmlNL: initialValues?.htmlNL ?? null,
      subjectEN: initialValues?.subjectEN ?? null,
      subjectNL: initialValues?.subjectNL ?? null,
      replyTo: initialValues?.replyTo ?? null,
      templateType: initialValues?.templateType ?? "Leads",
    },
    validate: zodResolver(formSchema),
  });

  const handleTabChange = (value: string | null) => {
    if (id) {
      navigate(`/app/htmlTemplates/${id}/${value}`);
    }
  };

  const [htmlContent, setHtmlContent] = useState(initialValues?.html);
  const [htmlContentNL, setHtmlContentNL] = useState(initialValues?.htmlNL);

  useEffect(() => {
    if (htmlContent !== form.values.html) {
      form.setValues({ html: htmlContent });
    }
  }, [htmlContent, form]);

  useEffect(() => {
    if (htmlContentNL !== form.values.htmlNL) {
      form.setValues({ htmlNL: htmlContentNL });
    }
  }, [htmlContentNL, form]);

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Tabs
          value={tabValue ?? "dutch"}
          defaultValue="dutch"
          onChange={handleTabChange}
        >
          <Tabs.List style={{ pointerEvents: "auto", fontWeight: "bold" }}>
            <Tabs.Tab value="dutch">{t("htmlTemplates.dutch")}</Tabs.Tab>
            <Tabs.Tab value="english" disabled={!id}>
              {t("htmlTemplates.english")}
            </Tabs.Tab>
          </Tabs.List>

          <Flex> </Flex>
          <Tabs.Panel value="dutch">
            <HtmlTemplateContentGrid
              label={t("htmlTemplates.htmlNL")}
              content={htmlContentNL ?? ""}
              onChange={(e) => setHtmlContentNL(e.target.value)}
              nameProps={form.getInputProps("name")}
              replyToProps={form.getInputProps("replyTo")}
              subjectProps={form.getInputProps("subjectNL")}
              templateTypeProps={form.getInputProps("templateType")}
              previewContent={htmlContentNL ?? ""}
            />
          </Tabs.Panel>

          <Tabs.Panel value="english">
            <HtmlTemplateContentGrid
              label={t("htmlTemplates.html")}
              content={htmlContent ?? ""}
              onChange={(e) => setHtmlContent(e.target.value)}
              nameProps={form.getInputProps("name")}
              replyToProps={form.getInputProps("replyTo")}
              subjectProps={form.getInputProps("subjectEN")}
              templateTypeProps={form.getInputProps("templateType")}
              previewContent={htmlContent ?? ""}
            />
          </Tabs.Panel>
        </Tabs>
      </EntityLayout>
    </form>
  );
}
