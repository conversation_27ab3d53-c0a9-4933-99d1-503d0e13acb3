import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  YAxis,
  Tooltip,
  CartesianGrid,
  ResponsiveContainer,
  type TooltipProps,
} from "recharts";
import { type Statistic } from "../../utils/types";
import {
  type NameType,
  type ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import {
  Box,
  Flex,
  Paper,
  ScrollArea,
  Text,
  useMantineTheme,
} from "@mantine/core";
import { IconArrowsMaximize, IconArrowsMinimize } from "@tabler/icons-react";
import classes from "./report.module.css";
import { useCallback, useRef, useState } from "react";

interface CustomBarChartStackedVerticalProps {
  data: Statistic[];
  title: string;
  marginTop?: number;
  height?: number;
  width?: string;
  percentage?: boolean;
}

interface CustomChartTooltipProps extends TooltipProps<ValueType, NameType> {
  percentage?: boolean;
}

export default function CustomBarChartStackedVertical({
  title,
  marginTop = 0,
  height = 120,
  width = "100%",
  data,
  percentage = false,
}: CustomBarChartStackedVerticalProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const colorMapping = useRef<Record<string, string>>({});
  const theme = useMantineTheme();
  const toggleExpand = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const getRandomColor = () => {
    return `#${Math.floor(Math.random() * 16777215).toString(16)}`;
  };

  const colorSet = theme.colors.chartColors ?? [];
  let colorIndex = 0; // Index to track which color should be assigned next

  function getNextColorFromSet() {
    const color = colorSet[colorIndex] ?? "#6495ED";
    colorIndex++;
    return color;
  }

  const transformedData =
    data?.map((item: Statistic) => {
      const statusCounts = item.stackedTotal?.reduce(
        (acc, { status, count }) => ({ ...acc, [status]: count }),
        {},
      );
      return {
        ...item,
        ...statusCounts,
      };
    }) || [];

  const statuses = Array.from(
    new Set(
      data?.flatMap((item) => item.stackedTotal?.map((st) => st.status)) || [],
    ),
  ).sort();

  const getColorForStatus = (status: string) => {
    let assignedColor: string;

    if (!colorMapping.current[status]) {
      switch (status) {
        case "Open": {
          assignedColor = "primary";
          break;
        }
        case "NotReached": {
          assignedColor = "red.6";
          break;
        }
        case "Reached": {
          assignedColor = "green.6";
          break;
        }
        default: {
          if (colorSet.length === 0 || colorIndex >= colorSet.length) {
            assignedColor = getRandomColor();
          } else {
            assignedColor = getNextColorFromSet();
          }
          break;
        }
      }
      colorMapping.current[status] = assignedColor;
    }
    return colorMapping.current[status];
  };

  const CustomTooltip = ({
    active,
    payload,
    label,
    percentage,
  }: CustomChartTooltipProps) => {
    if (active && payload?.length) {
      return (
        <Paper px="md" py="sm" withBorder shadow="md" radius="md">
          <Text fz="h6" fw={700}>
            {label}
          </Text>
          {payload.map((item) => (
            <Text c={item.color} key={item.dataKey as string} fz="sm">
              {item.name}: {item.value} {percentage && "%"}
            </Text>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const xKey = "display";
  const dataHeight = (transformedData.length || 1) * 40;
  const expandedStyle = {
    position: "fixed" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: "white",
    padding: "20px",
    overflow: "auto" as const,
  };

  const normalStyle = {
    border: "2px solid #e0e0e0",
    borderRadius: 4,
    height,
    width,
  };

  return (
    <Box
      style={isExpanded ? expandedStyle : normalStyle}
      mt={isExpanded ? 0 : marginTop}
    >
      <Flex dir="row">
        <Text fw={600} ml={4} flex={3} ta={"left"} size="sm">
          {title}
        </Text>
        <Box
          ta="right"
          mr={4}
          style={{ cursor: "pointer" }}
          onClick={toggleExpand}
        >
          {isExpanded ? (
            <IconArrowsMinimize size={14} />
          ) : (
            <IconArrowsMaximize size={14} />
          )}
        </Box>
      </Flex>
      <Flex dir="row" h={isExpanded ? "96%" : "100%"}>
        <Box w="80%">
          <ScrollArea
            classNames={classes}
            scrollbarSize={4}
            type="auto"
            scrollbars={"y"}
            h={isExpanded ? "calc(100%)" : "90%"}
          >
            <ResponsiveContainer
              width="98%"
              height={
                isExpanded
                  ? "100%"
                  : dataHeight < height
                    ? height * 0.85
                    : dataHeight
              }
            >
              <BarChart
                margin={{ left: -5, right: 10, bottom: -5 }}
                layout="vertical"
                data={transformedData}
                barGap={4}
                barSize={8}
              >
                <CartesianGrid horizontal={false} strokeDasharray="1 1" />
                <XAxis
                  orientation="top"
                  minTickGap={1}
                  type="number"
                  axisLine={false}
                  tickLine={false}
                  fontSize={12}
                  fontWeight={600}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  dataKey={xKey}
                  tickFormatter={(val: string) => {
                    val = val.replace(/@/g, "\n@");
                    val = val.replace(/ /g, "\n");
                    return val;
                  }}
                  fontSize={10}
                  interval={0}
                  width={90}
                  fontWeight={600}
                  tickSize={0}
                  type="category"
                ></YAxis>
                <Tooltip
                  cursor={{ fill: "transparent" }}
                  content={<CustomTooltip percentage={percentage} />}
                />
                {statuses.map((status) => (
                  <Bar
                    key={status}
                    dataKey={status!}
                    name={status}
                    stackId="a"
                    fill={getColorForStatus(status!)}
                  />
                ))}
              </BarChart>
            </ResponsiveContainer>
          </ScrollArea>
        </Box>
        <Box w="20%">
          <ScrollArea
            classNames={classes}
            scrollbarSize={4}
            type="auto"
            scrollbars={"y"}
            h={isExpanded ? "calc(100%)" : "90%"}
          >
            {statuses.map((status) => (
              <Text
                key={status}
                ta="center"
                color={getColorForStatus(status!)}
                size="xs"
              >
                {status}
              </Text>
            ))}
          </ScrollArea>
        </Box>
      </Flex>
    </Box>
  );
}
