import { Route, Routes } from "react-router-dom";
import { QuoteShow } from "@/features/quotes/routes/QuoteShow";
import { QuoteList } from "@/features/quotes/routes/QuoteList";
import { QuoteCreate } from "@/features/quotes/routes/QuoteCreate";

export default function QuotesRoutes() {
  return (
    <Routes>
      <Route index element={<QuoteList />} />
      <Route path=":id" element={<QuoteShow />} />
      <Route path="create" element={<QuoteCreate />} />
    </Routes>
  );
}
