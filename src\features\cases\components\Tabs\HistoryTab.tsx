import { Box, Grid } from "@mantine/core";
import { AuditList } from "@/features/audits/routes/AuditList";

interface HistoryTabProps {
  caseId?: string;
}

export function HistoryTab({ caseId }: HistoryTabProps) {
  return (
    <Grid mt="lg">
      {caseId ? (
        <>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Box mt={6}>
              <AuditList
                parentEntityId={caseId}
                parentEntityName="Cases"
                parentEntityIdParam="caseId"
              />
            </Box>
          </Grid.Col>
        </>
      ) : null}
    </Grid>
  );
}
