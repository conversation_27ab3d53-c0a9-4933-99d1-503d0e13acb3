import { useEffect, useState } from "react";
import { HubConnectionBuilder } from "@microsoft/signalr";
import { Avatar, Tooltip } from "@mantine/core";
import * as signalR from "@microsoft/signalr";
import UseUser from "@/hooks/useUser";
import { useAuth0 } from "@auth0/auth0-react";

interface ViewerUser {
  id: string;
  name: string;
  email: string;
}

const VIEWER_COUNT = 4;

const ViewerComponent = ({
  leadId,
  color = "cyan",
  avatarSize = undefined,
}: {
  leadId: string;
  visual?: boolean;
  color: string;
  avatarSize?: number;
}) => {
  const { isLoading, finalUser } = UseUser();
  const { getAccessTokenSilently } = useAuth0();
  const [viewerList, setViewerList] = useState<ViewerUser[]>([]);
  useEffect(() => {
    const connection = new HubConnectionBuilder()
      .withUrl(
        `${import.meta.env.VITE_PUBLIC_API_URL}/api/viewerHub?leadId=${leadId}`,
        {
          withCredentials: true,
          transport: signalR.HttpTransportType.LongPolling,
          accessTokenFactory: async () => {
            const token = await getAccessTokenSilently();
            return token;
          },
        },
      )
      .build();

    const connect = () => {
      if (isLoading) return;
      connection
        .start()
        .catch((err) => console.error("SignalR Connection Error:", err));
      connection.on(`UpdateViewerList[${leadId}]`, (users: ViewerUser[]) => {
        setViewerList(users);
      });
    };

    void connect();
    return () => {
      if (!connection) return;
      void connection.stop();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [finalUser]);

  const getInitials = (name: string) => {
    const nameArray = name.split(" ");
    const initials =
      nameArray.length > 1
        ? nameArray[0]?.charAt(0) + (nameArray[1] ? nameArray[1].charAt(0) : "")
        : nameArray[0]?.charAt(0);
    return initials ? initials.toUpperCase() : "";
  };

  const generateViewerListTooltip = () => {
    return viewerList
      .slice(VIEWER_COUNT - 1)
      .map((user) => `${user.name} (${user.email})`)
      .join(", ");
  };

  return (
    <div style={{ display: "flex", flexWrap: "wrap" }} key={"viewerList"}>
      {viewerList.length <= VIEWER_COUNT ? (
        viewerList.map((user, index) => (
          <Tooltip label={`${user.name} (${user.email})`} key={user.id + index}>
            <Avatar color={color} radius="xl" mr={4} size={avatarSize}>
              {getInitials(user.name)}
            </Avatar>
          </Tooltip>
        ))
      ) : (
        <>
          {viewerList.slice(0, VIEWER_COUNT - 1).map((user) => (
            <Tooltip label={`${user.name} (${user.email})`} key={user.id}>
              <Avatar color={color} radius="xl" mr={4}>
                {getInitials(user.name)}
              </Avatar>
            </Tooltip>
          ))}
          <Tooltip label={generateViewerListTooltip()} key={"otherViewers"}>
            <Avatar color={color} radius="xl">
              {`+${viewerList.length - VIEWER_COUNT}`}
            </Avatar>
          </Tooltip>
        </>
      )}
    </div>
  );
};

export default ViewerComponent;
