import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Badge,
  Box,
  Button,
  Center,
  Divider,
  Flex,
  Paper,
  Stack,
  Text,
} from "@mantine/core";

interface PageStartProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

export default function PageStart({ lead, setPages, pages }: PageStartProps) {
  const { t } = useTranslation("features");
  return (
    <Box>
      <Center>
        <Text fz={22} fw={600} c={"#282828"}>
          {t("Contract Data")}
        </Text>
      </Center>
      <Center>
        <Text fz={12} fw={300} c={"#ADADAD"}>
          {t("Verify draft contract data")}
        </Text>
      </Center>
      <Center w={"100%"} mt={40}>
        <Paper bg={"#F6F6F6"} w={"48vw"} p={24} style={{ borderRadius: 8 }}>
          <Flex direction={"row"} justify={"space-between"} gap={56}>
            <Box flex={4}>
              <Flex direction={"row"} justify={"space-between"} mt={16}>
                <Text fz={22} fw={600} c={"#282828"}>
                  {t("Information")}
                </Text>
                <Button variant="subtle">
                  <Text fz={16} fw={600} c={"primary"}>
                    {t("Edit")}
                  </Text>
                </Button>
              </Flex>
              <Flex direction={"row"} justify={"space-between"} mt={16}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("Contact")}
                </Text>
                <Text fz={14} fw={400}>
                  {t("John Doe")}
                </Text>
              </Flex>
              <Flex direction={"row"} justify={"space-between"} mt={4}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("Identification Status")}
                </Text>
                <Badge bg={"#F3F0FF"}>
                  <Text fz={10} fw={700} c={"#6A55BF"}>
                    {t("Not Verified")}
                  </Text>
                </Badge>
              </Flex>
              <Flex direction={"row"} justify={"space-between"} mt={4}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("Type")}
                </Text>
                <Text fz={14} fw={400}>
                  {t("Personal")}
                </Text>
              </Flex>
              <Divider my="md" />
              <Flex direction={"row"} justify={"space-between"} mt={4}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("Contract Status")}
                </Text>
                <Badge bg={"#F3F0FF"}>
                  <Text fz={10} fw={700} c={"#6A55BF"}>
                    {t("draft")}
                  </Text>
                </Badge>
              </Flex>
              <Flex direction={"row"} justify={"space-between"} mt={4}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("Contract ID")}
                </Text>
                <Badge bg={"lightgrey"}>
                  <Text fz={10} fw={500} c={"black"}>
                    {t("0000000000001")}
                  </Text>
                </Badge>
              </Flex>
              <Divider my="md" />
              <Flex direction={"row"} justify={"space-between"} mt={4}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("Payment")}
                </Text>
                <Badge bg={"#F3F0FF"}>
                  <Text fz={10} fw={700} c={"#6A55BF"}>
                    {t("Payment Pending")}
                  </Text>
                </Badge>
              </Flex>
              <Divider my="md" />
              <Flex direction={"row"} justify={"space-between"} mt={4}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("xxxxxxx")}
                </Text>
                <Badge bg={"lightgrey"}>
                  <Text fz={10} fw={500} c={"black"}>
                    {t("yyyyyy")}
                  </Text>
                </Badge>
              </Flex>
              <Flex direction={"row"} justify={"space-between"} mt={4}>
                <Text fz={14} fw={400} c={"#282828"}>
                  {t("xxxxxxx")}
                </Text>
                <Badge bg={"lightgrey"}>
                  <Text fz={10} fw={500} c={"black"}>
                    {t("yyyyyy")}
                  </Text>
                </Badge>
              </Flex>
            </Box>
            <Box flex={3}>
              <Stack gap={0}>
                <Text fz={14} fw={200}>
                  {t("First Name")}
                </Text>
                <Text fz={14} fw={400}>
                  {lead?.firstName}
                </Text>
              </Stack>
              <Stack gap={0} mt={8}>
                <Text fz={14} fw={200}>
                  {t("Last Name")}
                </Text>
                <Text fz={14} fw={400}>
                  {lead?.lastName}
                </Text>
              </Stack>
              <Stack gap={0} mt={8}>
                <Text fz={14} fw={200}>
                  {t("Phone number")}
                </Text>
                <Text fz={14} fw={400}>
                  {lead?.mobile}
                </Text>
              </Stack>
              <Stack gap={0} mt={8}>
                <Text fz={14} fw={200}>
                  {t("Email address")}
                </Text>
                <Text fz={14} fw={400}>
                  {lead?.email}
                </Text>
              </Stack>
              <Stack gap={0} mt={8}>
                <Text fz={14} fw={200}>
                  {t("Identification Number")}
                </Text>
                <Text fz={14} fw={400}>
                  {lead?.id}
                </Text>
              </Stack>
            </Box>
          </Flex>
        </Paper>
      </Center>
      <Center w={"100%"} mt={40}>
        <Flex direction={"row"} justify={"space-between"} gap={56}>
          <Button
            style={{ borderRadius: 12 }}
            onClick={() => {
              setPages([...pages, "IDENTIFICATION"]);
            }}
          >
            Person Verification
          </Button>
          <Button
            style={{ borderRadius: 12 }}
            onClick={() => {
              setPages([...pages, "UNIT_SELECTION"]);
            }}
          >
            Confirm contact details
          </Button>
        </Flex>
      </Center>
    </Box>
  );
}
