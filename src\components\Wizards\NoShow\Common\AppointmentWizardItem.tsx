import { type Schemas } from "@/types";
import { <PERSON><PERSON>, Box, Flex, SimpleGrid, <PERSON>ack, Text } from "@mantine/core";

import classes from "../NoShow.module.css";
import { IconDots } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { lowerCaseNthLetter } from "@/utils/filters";
interface AppointmentWizardItemProps {
  appointment?: Schemas["AppointmentRetrieveDto"];
}

export default function AppointmentWizardItem({
  appointment,
}: AppointmentWizardItemProps) {
  const { t } = useTranslation("features");
  const appointmentName =
    appointment?.lead?.fullName ?? appointment?.contact?.fullName ?? "";
  return (
    <Box
      className={classes.listItem}
      onClick={() => {
        window.open(`/app/leads/${appointment?.leadId}`, "_blank");
      }}
    >
      <Flex justify="space-between" align="center" direction="row">
        <Flex justify="flex-start" align="center" direction="row" mb={8}>
          <Text className={classes.listItemTitle}>{appointmentName}</Text>
          <Badge bg={"#FFDEAD"} c={"black"} fz={10} fw={400} ml={8}>
            {t(
              `appointments.${lowerCaseNthLetter(appointment?.appointmentType ?? "")}`,
            )}
          </Badge>
        </Flex>
        <IconDots width={20} height={20} style={{ display: "none" }} />
      </Flex>
      <SimpleGrid
        cols={{ base: 1, xs: 2, sm: 3, md: 3, lg: 5, xl: 5 }}
        spacing={8}
      >
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>
            {t(`appointments.email`)}
          </Text>
          <Text className={classes.listItemText}>
            {appointment?.lead?.email ?? ""}
          </Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>{t(`leads.mobile`)}</Text>
          <Text className={classes.listItemText}>
            {appointment?.lead?.mobile ?? ""}
          </Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>
            {t(`appointments.nextCallback`)}
          </Text>
          <Text className={classes.listItemText}>
            {new Date(appointment?.lead?.nextCallback ?? "").toLocaleTimeString(
              [],
              {
                year: "numeric",
                month: "numeric",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
                hour12: false,
              },
            )}
            {}
          </Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>
            {t(`appointments.priority`)}
          </Text>
          <Text className={classes.listItemText}>
            {appointment?.lead?.priority ?? ""}
          </Text>
        </Stack>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>
            {t(`appointments.callCount`)}
          </Text>
          <Text className={classes.listItemText}>
            {appointment?.lead?.callCount ?? ""}
          </Text>
        </Stack>
      </SimpleGrid>
    </Box>
  );
}
