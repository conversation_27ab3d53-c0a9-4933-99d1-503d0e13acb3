import { DateRenderer } from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";
import { ContactRoleLookup } from "@/components/Lookup/Features/ContactRoles/ContactRoleLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function CustomerContactColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "customer",
      header: t("customerContacts.customer"),
      ...TableRenderer(CustomerLookup, "customers", ["name"]),
    },
    {
      accessorKey: "contact",
      header: t("customerContacts.contact"),
      ...TableRenderer(ContactLookup, "contacts", ["fullName"]),
    },
    {
      accessorKey: "contactRole",
      header: t("customerContacts.contactRole"),
      ...TableRenderer(ContactRoleLookup, "contactRoles", ["name"]),
    },
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "modifiedOn",
      header: t("entity.modifiedOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
  ];
  return columns;
}
