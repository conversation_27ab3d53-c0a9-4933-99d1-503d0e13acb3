name: Azure Static Web Apps CI/CD

pr:
  branches:
    include:
      - acceptance

trigger:
  branches:
    include:
      - acceptance
variables:
  - group: frontend-test-group
stages:
  - stage: Test_Lint_Build
    displayName: Test, lint, build
    jobs:
      - job: lint_job
        displayName: Run Linter
        condition: and(succeeded(), or(eq(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.Reason'], 'PullRequestMerge'), eq(variables['Build.Reason'], 'Manual'), eq(variables['Build.Reason'], 'IndividualCI')))
        pool:
          vmImage: ubuntu-latest
        steps:
          - checkout: self
            submodules: true
          - script: |
              npm install eslint --save-dev
              npm run lint
            displayName: Run Linter

      # - job: locize_job
      #   displayName: Locize Download
      #   condition: and(succeeded(), or(eq(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.Reason'], 'PullRequestMerge'), eq(variables['Build.Reason'], 'Manual'), eq(variables['Build.Reason'], 'IndividualCI')))
      #   dependsOn: lint_job
      #   pool:
      #     vmImage: ubuntu-latest
      #   steps:
      #     - checkout: self
      #       submodules: true
      #     - script: |
      #         npm install
      #         npm run locize-download
      #       displayName: Locize

      - job: build_job
        displayName: Build
        condition: and(succeeded(), or(eq(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.Reason'], 'PullRequestMerge'), eq(variables['Build.Reason'], 'Manual'), eq(variables['Build.Reason'], 'IndividualCI')))
        dependsOn: lint_job
        pool:
          vmImage: ubuntu-latest
        steps:
          - checkout: self
            submodules: true
          - script: |
              npm install
              npm run locize-download
              npm run build
            displayName: Build

  - stage: Release
    displayName: Release
    dependsOn: Test_Lint_Build
    condition: succeeded()
    jobs:
      - job: release_job
        displayName: Deploy to Azure Static Web Apps (Manual)
        condition: and(succeeded(), or(eq(variables['Build.Reason'], 'PullRequestMerge'), eq(variables['Build.Reason'], 'Manual'), eq(variables['Build.Reason'], 'IndividualCI')))
        pool:
          vmImage: ubuntu-latest
        steps:
          - checkout: self
            submodules: true
          - script: |
              npm install
              npm run build
          - task: AzureStaticWebApp@0
            inputs:
              azure_static_web_apps_api_token: $(test-unitlogic-client_token)
              app_location: "/" # App source code path
              api_location: "" # Api source code path - optional
              output_location: "dist" # Built app content directory - optional

