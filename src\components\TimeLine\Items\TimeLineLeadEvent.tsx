import { type Schemas } from "@/types";
import { Accordion, Anchor, Box, Flex, rem, Text } from "@mantine/core";
import { IconFlag3 } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { TimelineLeadEventPanel } from "./Panels/TimelineLeadEventPanel";
import { DateTime } from "luxon";
import { lowerCaseNthLetter } from "@/utils/filters";

interface TimelineLeadEventProps {
  activity: Schemas["TimelineRetrieveDto"];
  openActivities: string[];
}
export function TimelineLeadEvent({
  activity,
  openActivities,
}: TimelineLeadEventProps) {
  const { t } = useTranslation("features");

  function lowerCaseFirstLetter(value: string) {
    if (!value) return value;
    return value.charAt(0).toLowerCase() + value.slice(1);
  }

  const isAccordionOpen = openActivities.includes(activity.id ?? "");
  const createdOn = DateTime.fromJSDate(new Date(activity.createdOn ?? ""));

  return (
    <Accordion.Item
      value={activity.id ?? ""}
      style={{ backgroundColor: "#fff" }}
    >
      <Accordion.Control>
        <Flex justify="space-between" align="center" direction="row">
          <Text mr={4} fw={600} size={"xs"} style={{ whiteSpace: "nowrap" }}>
            {createdOn.toFormat("HH:mm:ss")}
          </Text>
          <Anchor
            style={{ cursor: "pointer" }}
            onClick={(event) => {
              event.stopPropagation();
            }}
          >
            <Flex align="center">
              <IconFlag3
                style={{
                  width: rem(15),
                  height: rem(15),
                  marginRight: rem(5),
                }}
              />
              {t(
                "leads." +
                  (activity.timelineActivityType
                    ? lowerCaseFirstLetter(activity.timelineActivityType)
                    : ""),
              )}
            </Flex>
          </Anchor>
          <Box ml="auto" style={{ whiteSpace: "nowrap" }}>
            {t("leadEventEntries." + lowerCaseNthLetter(activity.status ?? ""))}
          </Box>
        </Flex>
      </Accordion.Control>
      <Accordion.Panel>
        {isAccordionOpen && (
          <TimelineLeadEventPanel leadEventId={activity.id!} />
        )}
      </Accordion.Panel>
    </Accordion.Item>
  );
}
