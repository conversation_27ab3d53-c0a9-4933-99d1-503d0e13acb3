import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import {
  Button,
  Paper,
  Stack,
  Text,
  Group,
  ActionIcon,
  NumberInput,
  Card,
  Badge,
  Divider,
  Grid,
  ScrollArea,
  Box,
} from "@mantine/core";
import { IconPlus, IconX, IconMinus } from "@tabler/icons-react";
import { useState } from "react";

interface PageProductsProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

interface Product {
  id: string;
  name: string;
  price: number;
  inStock: number;
  quantity: number;
}

interface UnitSummary {
  unitNumber: string;
  accessories: number;
  deposit: number;
}

export default function PageProducts({
  lead: _lead,
  setPages,
  pages,
}: PageProductsProps) {
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([
    {
      id: "1",
      name: "Verhuisdozen",
      price: 10,
      inStock: 10000,
      quantity: 2,
    },
    {
      id: "2",
      name: "Mailbox",
      price: 10,
      inStock: 10000,
      quantity: 1,
    },
  ]);

  // Mock data for summary
  const unitSummaries: UnitSummary[] = [
    { unitNumber: "Unit#0123", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#4444", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#7777", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#7777", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#7777", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#7777", accessories: 67, deposit: 80 },
  ];

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    setSelectedProducts((prev) =>
      prev
        .map((product) =>
          product.id === productId
            ? { ...product, quantity: Math.max(0, newQuantity) }
            : product,
        )
        .filter((product) => product.quantity > 0),
    );
  };

  const handleRemoveProduct = (productId: string) => {
    setSelectedProducts((prev) =>
      prev.filter((product) => product.id !== productId),
    );
  };

  const handleAddProduct = () => {
    setPages([...pages, "ADD_PRODUCT"]);
  };

  const handleNext = () => {
    setPages([...pages, "SIGN_CONTRACT"]);
  };

  // Calculations
  // const productsTotal = selectedProducts.reduce(
  //   (sum, product) => sum + product.price * product.quantity,
  //   0,
  // );
  // const unitsTotal = unitSummaries.reduce(
  //   (sum, unit) => sum + unit.accessories + unit.deposit,
  //   0,
  // );
  const depositTotal = 160; // One-time
  const serviceCosts = 20; // One-time
  const subtotal = 80; // Recurring
  const taxes = 79.8;
  const total = 380;

  return (
    <Paper p="xl" radius="md" h={"70vh"}>
      <Stack gap="lg">
        <Grid>
          <Grid.Col span={8}>
            <Card withBorder p="md">
              <Stack gap="md">
                <Group justify="space-between" align="center">
                  <Text fw={500} size="lg">
                    Selected products
                  </Text>
                  <Button
                    leftSection={<IconPlus size={16} />}
                    variant="light"
                    size="sm"
                    onClick={handleAddProduct}
                  >
                    Add Product
                  </Button>
                </Group>

                {selectedProducts.map((product) => (
                  <Card key={product.id} withBorder p="sm">
                    <Group justify="space-between" align="center">
                      <Stack gap="xs" style={{ flex: 1 }}>
                        <Group justify="space-between" align="center">
                          <Text fw={500}>{product.name}</Text>
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={() => handleRemoveProduct(product.id)}
                          >
                            <IconX size={14} />
                          </ActionIcon>
                        </Group>
                        <Group gap="xs">
                          <Text size="sm" fw={500}>
                            €{product.price}
                          </Text>
                          <Text size="xs" c="dimmed">
                            /pcs
                          </Text>
                          <Badge color="green" size="xs">
                            {product.inStock.toLocaleString()} in stock
                          </Badge>
                        </Group>
                      </Stack>

                      <Group gap="xs" align="center">
                        <ActionIcon
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleQuantityChange(
                              product.id,
                              product.quantity - 1,
                            )
                          }
                          disabled={product.quantity <= 1}
                        >
                          <IconMinus size={14} />
                        </ActionIcon>
                        <NumberInput
                          value={product.quantity}
                          onChange={(value) =>
                            handleQuantityChange(product.id, Number(value) || 0)
                          }
                          min={0}
                          max={product.inStock}
                          size="sm"
                          style={{ width: 60 }}
                          hideControls
                        />
                        <ActionIcon
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleQuantityChange(
                              product.id,
                              product.quantity + 1,
                            )
                          }
                          disabled={product.quantity >= product.inStock}
                        >
                          <IconPlus size={14} />
                        </ActionIcon>
                        <Text
                          fw={500}
                          style={{ minWidth: 60, textAlign: "right" }}
                        >
                          €{(product.price * product.quantity).toFixed(2)}
                        </Text>
                      </Group>
                    </Group>
                  </Card>
                ))}

                {selectedProducts.length === 0 && (
                  <Text c="dimmed" ta="center" py="xl">
                    No products selected
                  </Text>
                )}
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={4}>
            <Card withBorder p="md">
              <Stack gap={4}>
                <Text fw={500} size="lg">
                  Summary
                </Text>
                <ScrollArea.Autosize mah={"38vh"} type="always" scrollbars="y">
                  <Box mr={"1vw"}>
                    {unitSummaries.map((unit) => (
                      <Stack
                        key={unit.unitNumber}
                        gap={2}
                        m={4}
                        p={4}
                        style={{ border: "1px solid #DFDFDF", borderRadius: 8 }}
                      >
                        <Text fw={500} size="sm">
                          {unit.unitNumber}
                        </Text>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Accessories
                          </Text>
                          <Text size="sm">€{unit.accessories}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Deposit
                          </Text>
                          <Text size="sm">€{unit.deposit}</Text>
                        </Group>
                      </Stack>
                    ))}
                  </Box>
                </ScrollArea.Autosize>
                <Divider />

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Deposit (one-time)
                  </Text>
                  <Text size="sm">€{depositTotal}</Text>
                </Group>

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Service costs (one-time)
                  </Text>
                  <Text size="sm">€{serviceCosts}</Text>
                </Group>

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Subtotal (recurring)
                  </Text>
                  <Text size="sm">€{subtotal}</Text>
                </Group>

                <Divider />

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Taxes (btw)
                  </Text>
                  <Text size="sm">€{taxes.toFixed(2)}</Text>
                </Group>

                <Group justify="space-between">
                  <Text fw={600} size="lg">
                    Total
                  </Text>
                  <Text fw={600} size="lg">
                    €{total}
                  </Text>
                </Group>

                <Button fullWidth mt="md" onClick={handleNext}>
                  Confirm Selected Products
                </Button>
              </Stack>
            </Card>
          </Grid.Col>
        </Grid>
      </Stack>
    </Paper>
  );
}
