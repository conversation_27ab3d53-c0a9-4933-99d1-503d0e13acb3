import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import {
  Grid,
  Paper,
  Select,
  Tabs,
  TextInput,
  Textarea,
  NumberInput,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { zodResolver } from "mantine-form-zod-resolver";
import { UnitGroup } from "@/types/enums";
import { type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { ContractLookup } from "@/components/Lookup/Features/Contracts/ContractLookup";
import { StorageTypeLookup } from "@/components/Lookup/Features/StorageTypes/StorageTypeLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

/**
 * @TODO: contractId, ownerId
 */
const formSchema = z.object({
  externalReference: z.string(),
  remarks: z.string(),
  combinationLock: z.string(),
  insuranceStart: z.date().nullable(),
  insuranceEnd: z.date().nullable(),
  from: z.date().nullable(),
  to: z.date().nullable(),
  vat: z.coerce.number().nullable(),
  quantity: z.coerce.number().nullable(),
  discount: z.coerce.number().nullable(),
  price: z.coerce.number().nullable(),
  totalPrice: z.coerce.number().nullable(),
  insuranceValue: z.coerce.number().nullable(),
  storageValue: z.coerce.number().nullable(),
  contractId: z.string().nullable(),
  contract: z.object({}).nullable(),
  unitId: z.string().nullable(),
  unit: z.object({}).nullable(),
  storageTypeId: z.string().nullable(),
  storageType: z.object({}).nullable(),
  unitGroup: z.enum(UnitGroup as [string]).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface ContractLineFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  headerSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  contractLineId?: string;
  isCreate: boolean;
}

export function ContractLineForm({
  onSubmit,
  initialValues,
  actionSection = null,
  headerSection = null,
  title,
  isCreate,
}: ContractLineFormProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      externalReference: initialValues?.externalReference ?? "",
      remarks: initialValues?.remarks ?? "",
      combinationLock: initialValues?.combinationLock ?? "",
      insuranceStart: initialValues?.insuranceStart ?? null,
      insuranceEnd: initialValues?.insuranceEnd ?? null,
      from: initialValues?.from ?? null,
      to: initialValues?.to ?? null,
      vat: initialValues?.vat ?? null,
      quantity: initialValues?.quantity ?? null,
      discount: initialValues?.discount ?? null,
      price: initialValues?.price ?? null,
      totalPrice: initialValues?.totalPrice ?? null,
      insuranceValue: initialValues?.insuranceValue ?? null,
      storageValue: initialValues?.storageValue ?? null,
      contractId: initialValues?.contractId ?? "",
      contract: initialValues?.contract ?? null,
      unitId: initialValues?.unitId ?? "",
      unit: initialValues?.unit ?? null,
      storageTypeId: initialValues?.storageTypeId ?? "",
      storageType: initialValues?.storageType ?? null,
      unitGroup: initialValues?.unitGroup ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        disabled={form.getInputProps("recordState").value === "Inactive"}
        title={title}
        actionSection={actionSection}
        headerSection={headerSection}
        recordState={t(
          (form.getInputProps("recordState").value as string) ?? "",
        )}
      >
        <Tabs defaultValue="general">
          <Tabs.List>
            <Tabs.Tab value="general">{t("contractLines.general")}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="general">
            <Grid mt="lg">
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper shadow="xs" p="xs" pt="">
                  <ContractLookup
                    label={t("contractLines.contractId")}
                    initial={form.getValues().contract}
                    initialId={form.getValues().contractId}
                    identifier="contractIdContractLine"
                    {...form.getInputProps("contractId")}
                    {...{ labelProps: { style: { flex: "2.2" } } }}
                  />
                  <TextInput
                    label={t("Unit")}
                    disabled
                    {...{ labelProps: { style: { flex: "2.2" } } }}
                  />
                  <DatePickerInput
                    valueFormat={dateFormat.toUpperCase()}
                    clearable
                    maxDate={form.getInputProps("to").value as Date | undefined}
                    label={t("contractLines.from")}
                    {...form.getInputProps("from")}
                    {...{ labelProps: { style: { flex: "2.2" } } }}
                  />
                  <DatePickerInput
                    valueFormat={dateFormat.toUpperCase()}
                    clearable
                    minDate={
                      form.getInputProps("from").value as Date | undefined
                    }
                    label={t("contractLines.to")}
                    {...form.getInputProps("to")}
                    {...{ labelProps: { style: { flex: "2.2" } } }}
                  />
                  <TextInput
                    label={t("contractLines.externalReference")}
                    {...form.getInputProps("externalReference")}
                    {...{ labelProps: { style: { flex: "2.2" } } }}
                  />
                  <TextInput
                    label={t("contractLines.combinationLock")}
                    {...form.getInputProps("combinationLock")}
                    {...{ labelProps: { style: { flex: "2.2" } } }}
                  />
                  <Textarea
                    label={t("contractLines.remarks")}
                    {...form.getInputProps("remarks")}
                    {...{ labelProps: { style: { flex: "2.2" } } }}
                  />
                </Paper>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper shadow="xs" p="xs" pt="">
                  <NumberInput
                    label={t("contractLines.discount")}
                    leftSection={config.PERCENTAGE.symbol}
                    clampBehavior="strict"
                    min={0}
                    max={100}
                    {...form.getInputProps("discount")}
                    {...{ labelProps: { style: { flex: "1.6" } } }}
                  />
                  <NumberInput
                    label={t("contractLines.vat")}
                    leftSection={config.PERCENTAGE.symbol}
                    clampBehavior="strict"
                    min={0}
                    max={100}
                    {...form.getInputProps("vat")}
                    {...{ labelProps: { style: { flex: "1.6" } } }}
                  />
                  <NumberInput
                    label={t("contractLines.quantity")}
                    allowDecimal={false}
                    min={0}
                    {...form.getInputProps("quantity")}
                    {...{ labelProps: { style: { flex: "1.6" } } }}
                  />
                  <NumberInput
                    label={t("contractLines.price")}
                    leftSection={config.CURRENCY.symbol}
                    decimalScale={2}
                    thousandSeparator=" "
                    {...form.getInputProps("price")}
                    {...{ labelProps: { style: { flex: "1.6" } } }}
                  />
                  <NumberInput
                    label={t("contractLines.totalPrice")}
                    leftSection={config.CURRENCY.symbol}
                    decimalScale={2}
                    thousandSeparator=" "
                    {...form.getInputProps("totalPrice")}
                    {...{ labelProps: { style: { flex: "1.6" } } }}
                  />
                  <Select
                    searchable
                    label={t("contractLines.unitGroup")}
                    data={UnitGroup}
                    {...form.getInputProps("unitGroup")}
                    {...{ labelProps: { style: { flex: "1.6" } } }}
                    clearable
                  />
                  <StorageTypeLookup
                    label={t("contractLines.storageType")}
                    initial={form.getValues().storageType}
                    initialId={form.getValues().storageTypeId}
                    identifier="storageTypeIdContractLine"
                    {...form.getInputProps("storageTypeId")}
                    {...{ labelProps: { style: { flex: "1.6" } } }}
                  />
                </Paper>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Paper shadow="xs" p="xs" pt="">
                  <NumberInput
                    label={t("contractLines.storageValue")}
                    leftSection={config.CURRENCY.symbol}
                    decimalScale={2}
                    thousandSeparator=" "
                    {...form.getInputProps("storageValue")}
                  />
                  <NumberInput
                    label={t("contractLines.insuranceValue")}
                    leftSection={config.CURRENCY.symbol}
                    decimalScale={2}
                    thousandSeparator=" "
                    {...form.getInputProps("insuranceValue")}
                  />
                  <DatePickerInput
                    valueFormat={dateFormat.toUpperCase()}
                    clearable
                    maxDate={
                      form.getInputProps("insuranceEnd").value as
                        | Date
                        | undefined
                    }
                    label={t("contractLines.insuranceStart")}
                    {...form.getInputProps("insuranceStart")}
                  />
                  <DatePickerInput
                    valueFormat={dateFormat.toUpperCase()}
                    clearable
                    minDate={
                      form.getInputProps("insuranceStart").value as
                        | Date
                        | undefined
                    }
                    label={t("contractLines.insuranceEnd")}
                    {...form.getInputProps("insuranceEnd")}
                  />
                </Paper>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>
      </EntityLayout>
    </form>
  );
}
