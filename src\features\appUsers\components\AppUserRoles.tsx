import { useState, useEffect } from "react";
import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import {
  But<PERSON>,
  Modal,
  Loader,
  Box,
  Tooltip,
  Checkbox,
  Stack,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useEntityListQuery } from "@/features/entity/queries";
import {
  useRemoveRolesMutation,
  useEntityPostMutation,
} from "@/features/entity/mutations";
import { notifications } from "@mantine/notifications";
import UseAllRoles from "@/hooks/useAllRoles";
import { EntityLinkRenderer } from "@/components/Table/CellRenderers/EntityLinkRenderer";
import { IconUsers } from "@tabler/icons-react";

interface AppUserRolesProps {
  userId: string | undefined;
}

export function AppUserRoles({ userId }: AppUserRolesProps) {
  const { t } = useTranslation("features");
  const [showModal, setShowModal] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [unselectedRoles, setUnselectedRoles] = useState<string[]>([]);
  const [roles, setRoles] = useState<Schemas["RoleDto"][]>([]);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [rolesChanged, setRolesChanged] = useState(false);
  const [tableKey, setTableKey] = useState<string>(Date.now().toString());

  const { allRolesResponse, allRolesLoading, refetchAllRoles } = UseAllRoles();

  const {
    data: userRolesResponse,
    isLoading: userRolesLoading,
    refetch: refetchUserRoles,
  } = useEntityListQuery<Schemas["RoleDtoPagedList"]>({
    resourcePath: `/auth/users/${userId}/roles` as PathKeys,
    queryKey: "userRoles",
  });

  const resetRoleSelections = () => {
    const userRolesIds = userRolesResponse?.data?.map((role) => role.id) ?? [];
    setSelectedRoles(userRolesIds.filter(Boolean));
    setUnselectedRoles([]);
  };

  const toggleModal = () => {
    if (showModal) {
      resetRoleSelections();
    }
    setShowModal(!showModal);
  };

  useEffect(() => {
    if (showModal && rolesChanged) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      refetchUserRoles();
    }
  }, [
    showModal,
    userRolesResponse,
    userRolesLoading,
    refetchUserRoles,
    rolesChanged,
  ]);

  useEffect(() => {
    if (showModal && !allRolesResponse && !allRolesLoading) {
      void refetchAllRoles();
    }
  }, [showModal, allRolesResponse, allRolesLoading, refetchAllRoles]);

  useEffect(() => {
    if (allRolesResponse) {
      setRoles(allRolesResponse.data ?? []);
    }
  }, [allRolesResponse]);

  useEffect(() => {
    if (userRolesResponse) {
      const userRolesIds = userRolesResponse.data?.map((role) => role.id) ?? [];
      setUserRoles(userRolesIds.filter(Boolean));
      setSelectedRoles(userRolesIds.filter(Boolean));
    }
  }, [userRolesResponse]);

  const handleRoleSelect = (roleId: string) => {
    const isSelected = selectedRoles.includes(roleId);
    const isUnselected = !selectedRoles.includes(roleId);
    setSelectedRoles(
      isSelected
        ? selectedRoles.filter((id) => id !== roleId)
        : [...selectedRoles, roleId],
    );
    setUnselectedRoles(
      isUnselected
        ? unselectedRoles.filter((id) => id !== roleId)
        : [...unselectedRoles, roleId],
    );
  };
  const { mutate: assignRoles } = useEntityPostMutation<
    Schemas["RoleDto"],
    Schemas["RoleRequest"]
  >({
    resourcePath: `/auth/users/${userId}/roles`,
    queryKey: "assignRoles",
  });

  const { mutate: removeRoles } = useRemoveRolesMutation({
    resourcePath: "/auth/users/{id}/roles",
    resourceId: userId ?? "",
    queryKey: "assignRoles",
  });
  function handleAssignRoles() {
    const commonRoles = userRoles.filter((role) =>
      selectedRoles.includes(role),
    );
    if (
      commonRoles.length === userRoles.length &&
      commonRoles.length === selectedRoles.length
    ) {
      notifications.show({
        color: "red",
        title: t("appUsers.nothingChangedTitle"),
        message: t("appUsers.nothingChangedMessage"),
      });
    } else {
      setRolesChanged(true);
      if (selectedRoles.length > 0) {
        assignRoles(
          { roles: selectedRoles },
          {
            onSuccess: () => {
              toggleModal();
              notifications.show({
                color: "green",
                title: t("roles.successTitle"),
                message: t("roles.successMessage"),
              });
              setTableKey(Date.now().toString());
            },
          },
        );
      }
      if (unselectedRoles.length > 0) {
        removeRoles(unselectedRoles, {
          onSuccess: () => {
            toggleModal();
            notifications.show({
              color: "green",
              title: t("roles.successTitle"),
              message: t("roles.successMessage"),
            });
            setTableKey(Date.now().toString());
          },
        });
      }
    }
  }

  return (
    <Box mb="xs">
      <Modal
        opened={showModal}
        onClose={toggleModal}
        title={t("appUsers.allRoles")}
        size="sm"
      >
        {allRolesLoading || userRolesLoading ? (
          <Loader />
        ) : (
          <Stack mb="md">
            {roles
              .sort((a, b) => {
                const aSelected = selectedRoles.includes(a.id ?? "");
                const bSelected = selectedRoles.includes(b.id ?? "");
                return aSelected === bSelected ? 0 : aSelected ? -1 : 1;
              })
              .map((role) => (
                <Checkbox
                  key={role.id}
                  label={role.name}
                  checked={selectedRoles.includes(role.id ?? "")}
                  onChange={() => handleRoleSelect(role.id ?? "")}
                />
              ))}
          </Stack>
        )}

        <Button w="100%" onClick={handleAssignRoles}>
          {t("appUsers.confirm")}
        </Button>
      </Modal>
      <EntityLayout stickyHeader={false}>
        <EntityLayout.TableMantine<
          Schemas["RoleDto"],
          Schemas["RoleDtoPagedList"]
        >
          key={tableKey}
          resourcePath={`/auth/users/${userId}/roles` as PathKeys}
          queryKey="roles"
          entityPath="roles"
          title={t("appUsers.assignedRoles")}
          toolbar={
            <>
              <Tooltip label={t("appUsers.manageRoles")} withArrow>
                <Button
                  onClick={toggleModal}
                  variant="subtle"
                  styles={(theme) => ({
                    root: {
                      padding: 0,
                      backgroundColor: "transparent",
                      "&:hover": {
                        backgroundColor: theme.colors.gray[1],
                      },
                    },
                  })}
                >
                  <IconUsers size={16} />
                </Button>
              </Tooltip>
            </>
          }
          redirectTo={window.location.pathname}
          columns={[
            {
              accessorKey: "name",
              header: t("roles.name"),
              filterVariant: "text",
              Cell: (props) => EntityLinkRenderer(props, "roles"),
            },
            {
              accessorKey: "id",
              header: t("roles.id"),
              filterVariant: "text",
            },
          ]}
          visibleColumns={["name"]}
          initialSorting={[
            {
              id: "createdOn",
              desc: true,
            },
          ]}
        />
      </EntityLayout>
    </Box>
  );
}
