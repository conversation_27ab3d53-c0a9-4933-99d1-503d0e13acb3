import { useEntityListQuery } from "@/features/entity/queries";
import UseUserBusinessUnit from "@/hooks/businessUnit/useUserBusinessUnit";
import { type Schemas } from "@/types";

export default function AppointmentQuery() {
  const start = new Date();
  start.setUTCHours(0, 0, 0, 0);
  const end = new Date();
  end.setUTCHours(23, 59, 59, 999);
  const { userBusinessUnitId } = UseUserBusinessUnit();

  const allowedTypes = [
    "SpaceTour",
    "EngineRoomTour",
    "RoboticStorageTour",
    "BankSafe",
  ];
  const appointmentTypeFilter =
    "(" +
    allowedTypes.map((type) => `appointmentType == ${type}`).join(" || ") +
    ")";

  // sort by startDate
  const filterOpen = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "appointmentStatus == 0",
    appointmentTypeFilter,
    `endDate <= ${end.toISOString().replace("Z", "")}`,
    `startDate >= ${start.toISOString().replace("Z", "")}`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data } = useEntityListQuery<
    Schemas["AppointmentRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Appointments",
    params: {
      filter: filterOpen,
      orderBy: "startDate",
      desc: false,
    },
    queryKey: `appointmentListHome`,
  });

  const filterDone = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "(appointmentStatus == 1 || appointmentStatus == 2)",
    `endDate <= ${end.toISOString().replace("Z", "")}`,
    `startDate >= ${start.toISOString().replace("Z", "")}`,
  ]
    .filter(Boolean)
    .join(" && ");

  const { data: dataCompleted } = useEntityListQuery<
    Schemas["AppointmentRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Appointments",
    params: {
      filter: filterDone,
    },
    queryKey: `appointmentCompletedMetricHome`,
  });
  const totalCount = data?.totalCount;
  const totalCompleted = dataCompleted?.totalCount;
  const appointments = data?.data ?? [];
  return { totalCount, totalCompleted, appointments };
}
