import {
  Combobox,
  type ComboboxOptionProps,
  type ComboboxStore,
} from "@mantine/core";
import { type UseFormReturnType } from "node_modules/@mantine/form/lib/types";
import { useEffect, useRef } from "react";
import { LookupOptions } from "./Structure/LookupOptions";
import { LookupTarget } from "./Structure/LookupTarget";

type BaseLookupProps<T> = ReturnType<UseFormReturnType<T>["getInputProps"]> & {
  combobox: ComboboxStore;
  required?: boolean;
  disabled?: boolean;
  options: (JSX.Element | null)[] | undefined;
  header: JSX.Element;
  isFetching: boolean;
  currentFocus: number;
  entity: string;
  identifier: string;
  navigateEntity?: string;
  width?: string | number;
  left?: string | number;
  onOptionSubmit: (value: string, optionProps: ComboboxOptionProps) => void;
  onRightSectionClick: () => void;
};

export function BaseLookup<T>({
  combobox,
  required = false,
  disabled = false,
  options,
  header,
  isFetching,
  currentFocus,
  entity,
  identifier,
  navigateEntity,
  onOptionSubmit,
  onRightSectionClick,
  width = "70vw",
  left = "15vw",
  ...props
}: BaseLookupProps<T>) {
  const viewportRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    viewportRef.current
      ?.querySelectorAll(".mantine-Combobox-option")
      ?.[currentFocus]?.scrollIntoView({ block: "nearest" });
  }, [currentFocus]);

  return (
    <Combobox
      store={combobox}
      withinPortal={true}
      width={width}
      onOptionSubmit={onOptionSubmit}
    >
      <Combobox.Target>
        <LookupTarget
          disabled={disabled}
          required={required}
          entity={entity}
          identifier={identifier}
          navigateEntity={navigateEntity}
          onClick={() => {
            combobox.toggleDropdown();
          }}
          onRightSectionClick={onRightSectionClick}
          {...props}
        />
      </Combobox.Target>
      <Combobox.Dropdown style={{ left: left }}>
        <Combobox.Options>
          <LookupOptions
            options={options}
            header={header}
            isFetching={isFetching}
            viewportRef={viewportRef}
          />
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}
