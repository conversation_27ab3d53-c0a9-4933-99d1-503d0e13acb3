import { api } from "@/lib/api";

export interface CustomErrorData {
  fallbackMessage: string;
}
export async function EmailSend(
  recordId: string,
  entity: "Cases" | "Leads",
  emailBody: string,
  attachment: File[] | null,
  to: string,
  cc: string,
  bcc: string,
  subject: string,
  selectedMailbox: string | null,
) {
  const url = `/api/${entity}/${recordId}/sendEmail`;
  const formData = new FormData();
  formData.append("body", emailBody);
  formData.append(entity == "Leads" ? "leadId" : "caseId", recordId);
  formData.append("to", to);
  formData.append("cc", cc);
  formData.append("bcc", bcc);
  formData.append("subject", subject);
  formData.append("mailboxId", selectedMailbox ?? "");

  if (attachment && attachment.length > 0) {
    attachment.forEach((file) => {
      formData.append(`Files`, file);
    });
  }

  try {
    const response = await api.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    if (response.status !== 200) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error("Error in EmailSendReply:", error);
    throw error;
  }
}

export async function EmailForward(
  recordId: string,
  entity: "Cases",
  emailBody: string,
  attachment: File[] | null,
  to: string,
  subject: string,
  selectedMailbox: string | null,
) {
  const url = `/api/${entity}/${recordId}/forward`;
  const formData = new FormData();
  formData.append("body", emailBody);
  formData.append(entity == "Cases" ? "caseId" : "", recordId);
  formData.append("to", to);
  formData.append("subject", subject);
  formData.append("mailboxId", selectedMailbox ?? "");

  if (attachment && attachment.length > 0) {
    attachment.forEach((file) => {
      formData.append(`Files`, file);
    });
  }

  try {
    const response = await api.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    if (response.status !== 200) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error("Error in EmailSendReply:", error);
    throw error;
  }
}
