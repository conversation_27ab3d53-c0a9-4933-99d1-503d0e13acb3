import { Box, Center, Loader, Text, Stack } from "@mantine/core";

import classes from "../NoShow.module.css";
import { type PageName } from "./Common";
import { type PageProps } from "../../Common/Header/WizardHeader";

interface WizardCardProps extends PageProps<PageName> {
  isLoading?: boolean;
  pageName: PageName;
  headerText: string;
  descriptionText: string;
  footerText: string;
  headerIcon: JSX.Element;
}

export default function WizardCard({
  isLoading,
  pages,
  setPages,
  setTotalPages,
  pageName,
  headerText,
  descriptionText,
  footerText,
  headerIcon,
}: WizardCardProps) {
  return (
    <Box
      className={classes.wizardButton}
      onClick={() => {
        setPages([...pages, pageName]);
        if (pageName == "SPACETOUR") {
          setTotalPages!(pages.length);
        }
      }}
    >
      {isLoading ? (
        <Stack align="stretch" justify="center" gap="md" h={"100%"}>
          <Center h={"100%"} w={"100%"}>
            <Loader />
          </Center>
        </Stack>
      ) : (
        <Stack align="stretch" justify="center" gap="md">
          <Center>{headerIcon}</Center>
          <Text fz={24} ta={"center"} fw={600} c={"#282828"}>
            {headerText}
          </Text>
          <Text fz={12} fw={400} c={"#282828"}>
            {descriptionText}
          </Text>
          <Text fz={10} fw={600} c={"#ADADAD"}>
            {footerText}
          </Text>
        </Stack>
      )}
    </Box>
  );
}
