import { useDisclosure } from "@mantine/hooks";
import { Mo<PERSON>, Button, LoadingOverlay } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconLockOpen } from "@tabler/icons-react";
import { type Schemas } from "@/types";
import { useParams } from "react-router-dom";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import { useEntityPostMutation } from "@/features/entity/mutations";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { useQueryClient } from "react-query";

export function ReOpenButton() {
  const [opened, { open, close }] = useDisclosure(false);
  const { t } = useTranslation("features");
  const queryCache = useQueryClient();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);

  const { mutate: reopenLead } = useEntityPostMutation<Schemas["Lead"], string>(
    {
      resourcePath: `/api/Leads/reopenLead`,
      queryKey: "lead_" + id!,
    },
  );

  const handleOpen = () => {
    open();
  };

  const handleReOpen = () => {
    setLoading(true);
    reopenLead(id!, {
      onSuccess: () => {
        notifications.show({
          color: "green",
          title: t("leads.reOpenSuccessTitle"),
          message: t("leads.reOpenSuccessMessage"),
        });
      },
      onError: () => {
        notifications.show({
          color: "red",
          title: t("leads.reOpenErrorTitle"),
          message: t("leads.reOpenErrorMessage"),
        });
      },
      onSettled: () => {
        void queryCache.invalidateQueries("lead_" + id);
        setLoading(false);
      },
    });
    close();
  };

  return (
    <>
      <ButtonMain
        label={t("leads.reOpen")}
        icon={<IconLockOpen size={18} />}
        onClick={handleOpen}
      />
      <Modal
        opened={opened}
        onClose={close}
        size={"lg"}
        centered
        title={t("leads.confirmReOpen")}
      >
        <p>{t("leads.reOpenMessage")}</p>
        <LoadingOverlay visible={loading} />
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <Button onClick={handleReOpen} w={"48%"} mt={"xs"}>
            {t("leads.reOpen")}
          </Button>
          <Button onClick={close} variant="light" w={"48%"} mt={"xs"}>
            {t("leads.cancel")}
          </Button>
        </div>
      </Modal>
    </>
  );
}
