import {
  LeadFormProvider,
  leadFormSchema,
  useLeadForm,
} from "@/features/leads/providers/form";
import { config } from "@/config";
import { getDirtyFormFields } from "@/features/entity/utils";
import { zodResolver } from "mantine-form-zod-resolver";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { StorageTypeLookup } from "@/components/Lookup/Features/StorageTypes/StorageTypeLookup";
import {
  StorageUnitReason,
  StorageDuration,
  RentAsBusiness,
  Transportation,
  Discount,
  ProductType,
  SizeOfUnit,
} from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import {
  Text,
  Select,
  TextInput,
  NumberInput,
  Textarea,
  Box,
  Center,
  Grid,
  Button,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { useTranslation } from "react-i18next";
import { IconCalendarMonth } from "@tabler/icons-react";
import { type Schemas } from "@/types";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { filterFalsyValues } from "@/utils/filters";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { useNavigate } from "react-router-dom";
import { type PageName } from "../Common/Common";
import { type PageProps } from "../../Common/Header/WizardHeader";

interface PageSpaceTourProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
  appointment?: Schemas["AppointmentRetrieveDto"];
}
export default function PageSpaceTour({
  lead,
  appointment,
  setPages,
  pages,
}: PageSpaceTourProps) {
  const { t } = useTranslation("features");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { dateFormat } = useSettingsContext();
  const navigate = useNavigate();
  const form = useLeadForm({
    initialValues: {
      firstName: lead?.firstName ?? "",
      lastName: lead?.lastName ?? "",
      email: lead?.email ?? "",
      salutation: lead?.salutation ?? "MrMrs",
      companyName: lead?.companyName ?? "",
      webformTitle: lead?.webformTitle ?? "",
      webformDetails: lead?.webformDetails ?? "",
      mobile: lead?.mobile ?? "",
      phone: lead?.phone ?? "",
      discountText: lead?.discountText ?? "",
      storageValue: lead?.storageValue ?? null,
      spaceTourRemarks: lead?.spaceTourRemarks ?? "",
      callCount: lead?.callCount ?? 0,
      callCountReached: lead?.callCountReached ?? 0,
      callCountNotReached: lead?.callCountNotReached ?? 0,
      quotationEmailSent: lead?.quotationEmailSent ?? false,
      moveInDate: new Date(lead?.moveInDate ?? new Date()),
      nextCallback: null,
      reservationStart: null,
      reservedUntil: null,
      rentAsBusiness: lead?.rentAsBusiness ?? null,
      transportation: lead?.transportation ?? null,
      discount: lead?.discount ?? null,
      storageDuration: lead?.storageDuration ?? null,
      storageUnitReason: lead?.storageUnitReason ?? null,
      processStage: lead?.processStage ?? "New",
      optOutPhone: lead?.optOutPhone ?? false,
      description: lead?.description ?? "",
      type: lead?.type ?? "Private",
      startWithin: lead?.startWithin ?? "Later",
      productType: lead?.productType ?? "StorageSpace",
      leadSource: lead?.leadSource ?? "",
      sizeOfUnit: lead?.sizeOfUnit ?? null,
      businessUnitId: lead?.businessUnitId ?? "",
      businessUnit: lead?.businessUnit ?? null,
      existingContactId: lead?.existingContactId ?? "",
      existingContact: lead?.existingContact ?? null,
      existingCustomerId: lead?.existingCustomerId ?? "",
      existingCustomer: lead?.existingCustomer ?? null,
      contractId: lead?.contractId ?? "",
      contract: lead?.contract ?? null,
      recordState: lead?.recordState ?? "",
      comments: lead?.comments ?? "",
      promotionCode: lead?.promotionCode ?? "",
      fax: lead?.fax ?? "",
      city: lead?.city ?? "",
      zip: lead?.zip ?? "",
      street: lead?.street ?? "",
      countryId: lead?.countryId ?? "",
      country: lead?.country ?? null,
      approvalForAddressUsage: lead?.approvalForAddressUsage ?? false,
      storageTypeId: lead?.storageTypeId ?? "",
      storageType: lead?.storageType ?? null,
      preferredLanguage: lead?.preferredLanguage ?? "Dutch",
      optInType: lead?.optInType ?? "OptIn",
      step: lead?.step ?? "",
      price: lead?.price ?? "",
      unitSize: lead?.unitSize ?? "",
      volume: lead?.volume ?? null,
      amount: lead?.amount ?? null,
      lossReasonId: lead?.lossReasonId ?? "",
      lossReason: lead?.lossReason ?? null,
      makeAnAppointment: null,
    },
    validate: zodResolver(leadFormSchema),
  });
  const queryCache = useQueryClient();
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Lead"],
    Schemas["LeadPatchDto"]
  >({
    resourcePath: "/api/Leads/{id}",
    resourceId: lead?.id ?? "",
    queryKey: "lead",
  });

  const { mutate: updateAppointment } = useEntityUpdateMutation<
    Schemas["Appointment"],
    Schemas["AppointmentPatchDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: appointment?.id ?? "",
    queryKey: "appointment",
  });

  return (
    <LeadFormProvider form={form}>
      <form
        onSubmit={form.onSubmit((fields) => {
          setIsLoading(true);
          if (lead?.processStage !== "FollowUp") {
            fields.processStage = "SpaceTour";
          }
          const filteredFields = getDirtyFormFields(
            fields,
            false,
            form.isDirty,
          );

          const filteredValues = filterFalsyValues(filteredFields);
          if (Object.keys(filteredValues).length != 0) {
            update(filteredValues, {
              onSuccess: () => {
                updateAppointment(
                  {
                    appointmentStatus: "Completed",
                  },
                  {
                    onSuccess: () => {
                      void queryCache.invalidateQueries(
                        "lead_" + appointment?.leadId,
                      );
                      setPages([...pages, "COMPLETED"]);
                      setIsLoading(false);
                    },
                    onError: (error) => {
                      setIsLoading(false);
                      console.error(error);
                    },
                  },
                );
              },
              onError: (error) => {
                setIsLoading(false);
                console.error(error);
              },
              onSettled: () => {
                navigate(`/app/leads/${lead?.id}`);
              },
            });
          } else {
            setIsLoading(false);
          }
        })}
      >
        <Center>
          <Text fz={16} fw={600} c={"#282828"}>
            {t("wizards.SpaceTour.Title")}
          </Text>
        </Center>
        <Center>
          <Text fz={10} fw={300} c={"#ADADAD"}>
            {t("wizards.SpaceTour.Label")}
          </Text>
        </Center>
        <Box p={16} mr={"14%"} ml={"14%"}>
          <Grid mt="lg">
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Select
                searchable
                label={t("leads.storageUnitReason")}
                data={StorageUnitReason.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("storageUnitReason")}
                {...{
                  labelProps: {
                    style: {
                      flex: "3",
                      fontSize: "0.7rem",
                      fontWeight: "500",
                    },
                  },
                }}
              />
              <FieldValidation isDirty={form.isDirty("storageTypeId")}>
                <StorageTypeLookup
                  required
                  initial={form.getValues().storageType}
                  initialId={form.getValues().storageTypeId}
                  identifier="storageTypeIdLead"
                  label={t("leads.storageType")}
                  {...form.getInputProps("storageTypeId")}
                  {...{
                    labelProps: {
                      style: {
                        flex: "3",
                        fontSize: "0.7rem",
                        fontWeight: "500",
                      },
                    },
                  }}
                />
              </FieldValidation>
              <DatePickerInput
                valueFormat={dateFormat.toUpperCase()}
                clearable
                label={t("leads.moveInDate")}
                {...form.getInputProps("moveInDate")}
                {...{
                  labelProps: {
                    style: {
                      flex: "3",
                      fontSize: "0.7rem",
                      fontWeight: "500",
                    },
                  },
                }}
              />
              <Select
                searchable
                label={t("leads.storageDuration")}
                data={StorageDuration.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("storageDuration")}
                {...{
                  labelProps: {
                    style: {
                      flex: "3",
                      fontSize: "0.7rem",
                      fontWeight: "500",
                    },
                  },
                }}
              />
              <Select
                searchable
                label={t("leads.rentAsBusiness")}
                data={RentAsBusiness}
                clearable
                {...form.getInputProps("rentAsBusiness")}
                {...{
                  labelProps: {
                    style: {
                      flex: "3",
                      fontSize: "0.7rem",
                      fontWeight: "500",
                    },
                  },
                }}
              />
              <Select
                searchable
                label={t("leads.transportation")}
                data={Transportation.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("transportation")}
                {...{
                  labelProps: {
                    style: {
                      flex: "3",
                      fontSize: "0.7rem",
                      fontWeight: "500",
                    },
                  },
                }}
              />
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <TextInput
                label={t("leads.discountText")}
                {...form.getInputProps("discountText")}
                {...{
                  labelProps: {
                    style: {
                      flex: "2.4",
                      fontSize: "0.7rem",
                      fontWeight: "500",
                    },
                  },
                }}
              />
              <FieldValidation isDirty={form.isDirty("productType")}>
                <Select
                  searchable
                  label={t("leads.productType")}
                  data={ProductType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  {...form.getInputProps("productType")}
                  {...{
                    labelProps: {
                      style: {
                        flex: "2.4",
                        fontSize: "0.7rem",
                        fontWeight: "500",
                      },
                    },
                  }}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("sizeOfUnit")}>
                <Select
                  searchable
                  label={t("leads.sizeOfUnit")}
                  data={SizeOfUnit.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  {...form.getInputProps("sizeOfUnit")}
                  {...{
                    labelProps: {
                      style: {
                        flex: "2.4",
                        fontSize: "0.7rem",
                        fontWeight: "500",
                      },
                    },
                  }}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("amount")}>
                <NumberInput
                  label={t("leads.amount")}
                  leftSection={config.CURRENCY.symbol}
                  {...form.getInputProps("amount")}
                  {...{
                    labelProps: {
                      style: {
                        flex: "2.4",
                        fontSize: "0.7rem",
                        fontWeight: "500",
                      },
                    },
                  }}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("volume")}>
                <NumberInput
                  label={t("leads.volume")}
                  leftSection={config.CUBICMETERS.symbol}
                  {...form.getInputProps("volume")}
                  {...{
                    labelProps: {
                      style: {
                        flex: "2.4",
                        fontSize: "0.7rem",
                        fontWeight: "500",
                      },
                    },
                  }}
                />
              </FieldValidation>
              <Select
                searchable
                label={t("leads.discount")}
                data={Discount.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("discount")}
                {...{
                  labelProps: {
                    style: {
                      flex: "2.4",
                      fontSize: "0.7rem",
                      fontWeight: "500",
                    },
                  },
                }}
              />
            </Grid.Col>
            <Textarea
              ml={4}
              mr={8}
              label={t("leads.spaceTourRemarks")}
              {...form.getInputProps("spaceTourRemarks")}
              w={"100%"}
              minRows={4}
              autosize
              {...{
                labelProps: {
                  style: {
                    flex: "1.2",
                    fontSize: "0.7rem",
                    fontWeight: "500",
                  },
                },
              }}
            />
          </Grid>
        </Box>
        <Center mt={24}>
          <Button
            disabled={!form.isDirty()}
            type="submit"
            loading={isLoading}
            leftSection={<IconCalendarMonth width={20} height={20} />}
            fz={14}
            fw={400}
          >
            {t("wizards.SpaceTour.ConfirmButton")}
          </Button>
        </Center>
      </form>
    </LeadFormProvider>
  );
}
