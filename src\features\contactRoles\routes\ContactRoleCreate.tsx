import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { type Schemas } from "@/types";
import { ContactRoleForm } from "../components/ContactRoleForm";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { useState } from "react";
import { notifications } from "@mantine/notifications";

export function ContactRoleCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["ContactRole"],
    Schemas["ContactRoleCreateDto"]
  >({ resourcePath: "/api/ContactRoles", queryKey: "contactRole" });
  const { t } = useTranslation("features");

  return (
    <ContactRoleForm
      isCreate={true}
      title={t("contactRoles.createTitle")}
      onSubmit={(values) => {
        mutate(values, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/contactRoles");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/contactRoles/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
