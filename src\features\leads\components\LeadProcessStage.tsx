import { Group, Paper, Stepper, em, Text } from "@mantine/core";
import { ProcessStage } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "@mantine/hooks";
import { modals } from "@mantine/modals";
import { IconBrowserX, IconCheck } from "@tabler/icons-react";
import classes from "./ProcessStage.module.css";

interface LeadProcessStageProps {
  onStepClick: (index: number) => void;
  activeStageIndex: number;
  lossReason?: string | null;
}

export function LeadProcessStage({
  onStepClick,
  activeStageIndex,
  lossReason,
}: LeadProcessStageProps) {
  const { t } = useTranslation("features");
  const isMobile = useMediaQuery(`(max-width: ${em(750)})`);
  const maxStageIndex = ProcessStage.length - 2;
  const isLeadLost = ProcessStage[activeStageIndex] === "Lost";
  const isLeadWon = ProcessStage[activeStageIndex] === "Won";

  const openConfirmModal = (stepIndex: number) => {
    modals.openConfirmModal({
      title: (
        <Text fw={600} size="lg">
          {t("leads.moveStepperTitle")}
        </Text>
      ),
      centered: true,
      children: <Text size="sm">{t("leads.moveStepperText")}</Text>,
      labels: {
        confirm: t("leads.moveStepperConfirm"),
        cancel: t("leads.moveStepperCancel"),
      },
      confirmProps: { color: "primary" },
      onConfirm: () => {
        onStepClick(stepIndex);
      },
    });
  };

  if (isLeadLost || isLeadWon) {
    return (
      <Group justify="center" gap="xl">
        <Stepper active={activeStageIndex + 1}>
          <Stepper.Step
            key={ProcessStage[activeStageIndex]}
            completedIcon={isLeadLost ? <IconBrowserX /> : undefined}
            allowStepSelect={false}
            label={
              isLeadLost ? (
                <>
                  <strong>{t("leads.lost")}</strong> -
                  {t("lossReasons." + lossReason) ?? t("lossReasons.other")}
                </>
              ) : (
                <>
                  <strong>{t("leads.won")}</strong>
                </>
              )
            }
          />
        </Stepper>
      </Group>
    );
  }
  return (
    <>
      <Paper shadow="xs" p="lg" mb="xs" visibleFrom="md">
        <Stepper
          active={
            activeStageIndex == maxStageIndex
              ? maxStageIndex + 1
              : activeStageIndex
          }
          color="green.3"
          completedIcon={<IconCheck size={24} />}
          size={isMobile ? "xl" : "xs"}
          onStepClick={(stepIndex: number) => {
            if (
              activeStageIndex < stepIndex &&
              (stepIndex == 5 || (activeStageIndex == 6 && stepIndex == 7))
            )
              openConfirmModal(stepIndex);
            else return;
          }}
        >
          {ProcessStage.map(
            (stage, index) =>
              stage != "Lost" && (
                <Stepper.Step
                  key={stage}
                  label={t(getEnumTransKey("leads", stage))}
                  classNames={{
                    stepLabel:
                      index === activeStageIndex
                        ? classes.stepLabelActive
                        : classes.stepLabel,
                    stepIcon:
                      index === activeStageIndex
                        ? classes.stepIconActive
                        : index < activeStageIndex
                          ? classes.stepCompletedIcon
                          : classes.stepIcon,
                  }}
                />
              ),
          )}
        </Stepper>
      </Paper>
      <Paper shadow="xs" p="lg" mb="xs" hiddenFrom="md">
        <Group justify="center" gap="xl">
          <Stepper active={activeStageIndex + 1} onStepClick={onStepClick}>
            {ProcessStage.map(
              (stage, index) =>
                index === activeStageIndex &&
                stage != "Lost" && (
                  <Stepper.Step
                    key={stage}
                    allowStepSelect={false}
                    label={t(getEnumTransKey("leads", stage))}
                  />
                ),
            )}
          </Stepper>
        </Group>
      </Paper>
    </>
  );
}
