import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useSearchParams } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { ContactMomentForm } from "../components/ContactMomentForm";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";
import { type Dispatch, type SetStateAction } from "react";
import { useQueryClient } from "react-query";

interface ContactMomentCreateProps {
  refreshForm?: () => void;
  closeModal?: () => void;
  leadId?: string;
  isModal?: boolean;
  setPhantomEvent?: Dispatch<SetStateAction<Schemas["ContactMoment"] | null>>;
}

export function ContactMomentCreate({
  refreshForm: refreshAllContactMoments,
  closeModal,
  leadId: leadIdProp,
  isModal,
  setPhantomEvent,
}: ContactMomentCreateProps) {
  const [searchParams] = useSearchParams();
  const queryCache = useQueryClient();
  const { mutate } = useEntityCreateMutation<
    Schemas["ContactMoment"],
    Schemas["ContactMomentCreateDto"]
  >({ resourcePath: "/api/ContactMoments", queryKey: "contactMoment" });
  const { t } = useTranslation("features");
  const leadId = searchParams.get("leadId");
  const leadIdToUse = leadId ?? leadIdProp;
  const {
    data: lead = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/Leads/{id}",
    resourceId: leadIdToUse!,
    queryKey: "leadContactMoment",
  });
  if (isLoading || isFetching) {
    return <></>;
  }
  const initialValues = {
    id: "",
    comment: "",
    leadId: leadIdToUse ?? "",
    lead: lead,
    recordState: "Active",
  };
  return (
    <ContactMomentForm
      isCreate={true}
      isModal={isModal}
      showBackButton={false}
      initialValues={initialValues}
      closeModal={closeModal}
      setPhantomEvent={setPhantomEvent}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: () => {
            void queryCache.invalidateQueries("lead_omnichannel_list");
            void queryCache.invalidateQueries("lead_" + leadIdToUse);
            notifications.show({
              color: "green",
              title: t("notifications.createSuccessTitle"),
              message: t("notifications.createSuccessMessage"),
            });
            if (refreshAllContactMoments) {
              refreshAllContactMoments();
            }
            if (closeModal) {
              closeModal();
            }
            if (setPhantomEvent) {
              setPhantomEvent(null);
            }
          },
        });
      }}
    />
  );
}
