import { PageLoader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  AppointmentTypeTemplateMappingForm,
  type FormSchema,
} from "../components/AppointmentTypeTemplateMappingForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function AppointmentTypeTemplateMappingShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["AppointmentTypeTemplateMapping"],
    Schemas["AppointmentTypeTemplateMappingCreateDto"]
  >({
    resourcePath: "/api/AppointmentTypeTemplateMappings/{id}",
    resourceId: id!,
    queryKey: "appointmentTypeTemplateMapping",
  });
  const [searchParams] = useSearchParams();

  const redirectTo =
    searchParams.get("redirectTo") ?? "/app/appointmentTypeTemplateMappings";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("appointmentTypeTemplateMapping_" + id);
  };

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["AppointmentTypeTemplateMapping"]>({
    resourcePath: "/api/AppointmentTypeTemplateMappings/{id}",
    resourceId: id!,
    queryKey: "appointmentTypeTemplateMapping",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <AppointmentTypeTemplateMappingForm
      isCreate={false}
      title={t("appointmentTypeTemplateMappings.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["AppointmentTypeTemplateMappingCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t(
                  "appointmentTypnotificationseTemplateMappings.updateSuccessTitle",
                ),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
