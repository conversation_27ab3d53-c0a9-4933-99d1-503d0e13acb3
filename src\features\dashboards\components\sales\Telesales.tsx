import type React from "react";
import { Flex, Grid } from "@mantine/core";
import CustomBarChartVertical from "../common/CustomBarChartVertical";
import CardChart from "../common/CardChart";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import { type DashboardContentProps } from "../index";
import CustomBarChartHorizontal from "../common/CustomBarChartHorizontal";
import DonutChart from "../common/DonutChart";
import CustomBarChartStackedVertical from "../common/CustomBarChartStackedVertical";

const priorityPredicate = (value: number) => {
  if (value < 25) {
    return "#8bc34a";
  }

  if (value < 100) {
    return "#ffc107";
  }

  if (value >= 100) {
    return "#ef5350";
  }

  return "transparent";
};

const metrics: MetricProps[] = [
  { metric: "LeadsCalledToday", period: "today" },
  { metric: "AppointmentsMadeToday", period: "today" },
  { metric: "CustomLeadPriorityChangeTelesales", sales: true },
  { metric: "CustomLeadPriorityChangeTelesales", sales: true },
  { metric: "CustomLeadPriorityChangeTelesales", sales: true },
  { metric: "CustomLeadPriorityChangeTelesales", sales: true },
  { metric: "LeadsCreated30Days", period: "custom" },
  { metric: "LeadsConverted30Days", period: "custom" },
  { metric: "LeadsCalled30Days", period: "custom" },
  { metric: "MostAppointmentsBooked", period: "this_month" },
  { metric: "LeadsCalledTodayTelesales", period: "today" },
  { metric: "InvertedLeadsCalledTodayTelesales", period: "today" },
];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <>
    <Grid gutter={"xs"}>
      <Grid.Col span={12}>
        <Grid gutter={"xs"}>
          <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
            <CustomBarChartStackedVertical
              height={250}
              marginTop={10}
              data={data[10]!}
              title={"Leads Called Today"}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
            <CustomBarChartVertical
              height={250}
              data={data[1]}
              title={"Appointments Made Today"}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 6, lg: 6 }}>
            <Grid gutter={"xs"}>
              <Grid.Col span={12}>
                <Grid gutter={"xs"}>
                  <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
                    <CardChart
                      marginTop={10}
                      value={data[2]![0]?.value.toString() ?? "0"}
                      predicate={priorityPredicate}
                      title={"Prio 1 Leads"}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
                    <CardChart
                      marginTop={10}
                      value={data[2]![1]?.value.toString() ?? "0"}
                      predicate={priorityPredicate}
                      title={"Prio 2 Leads"}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
                    <CardChart
                      marginTop={10}
                      value={data[2]![2]?.value.toString() ?? "0"}
                      predicate={priorityPredicate}
                      title={"Prio 3 Leads"}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6, lg: 3 }}>
                    <CardChart
                      marginTop={10}
                      value={data[2]![3]?.value.toString() ?? "0"}
                      predicate={priorityPredicate}
                      title={"Prio 4 Leads"}
                    />
                  </Grid.Col>
                </Grid>
              </Grid.Col>
              <Grid.Col span={12}>
                <Grid gutter={"xs"}>
                  <Grid.Col span={{ base: 12, md: 6, lg: 4 }}>
                    <CardChart
                      marginTop={0}
                      value={data[0]![0]?.total.toString() ?? "0"}
                      title={"Called"}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6, lg: 4 }}>
                    <CardChart
                      marginTop={0}
                      value={data[1]![0]?.total.toString() ?? "0"}
                      title={"Appointments"}
                    />
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6, lg: 4 }}>
                    <CardChart
                      value={
                        data[6]![
                          (data[6]?.length ?? 1) - 1
                        ]?.value.toString() ?? "0"
                      }
                      title={"New Leads Today"}
                    />
                  </Grid.Col>
                </Grid>
              </Grid.Col>
            </Grid>
          </Grid.Col>
        </Grid>
      </Grid.Col>
    </Grid>
    <Flex dir="row" mt={4}>
      <Grid gutter="xs" flex={2.5} mr={5}>
        <Grid.Col span={12}>
          <CustomBarChartHorizontal
            marginTop={0}
            height={150}
            data={data[6]}
            title={"New Leads Per Day Last 30 Days"}
          />
        </Grid.Col>
        <Grid.Col span={12}>
          <CustomBarChartHorizontal
            height={150}
            marginTop={0}
            data={data[7]}
            title={"Leads Converted Per Day Last 30 Days"}
          />
        </Grid.Col>
        <Grid.Col span={12}>
          <CustomBarChartHorizontal
            height={150}
            marginTop={0}
            data={data[8]}
            title={"Leads Called Per Day Last 30 Days"}
          />
        </Grid.Col>
      </Grid>
      <Flex direction={"column"} flex={1}>
        <DonutChart
          height={250}
          marginTop={0}
          data={data[9]}
          title="Most Appointments Booked"
        />
        <CustomBarChartStackedVertical
          height={270}
          marginTop={10}
          data={data[11]!}
          title={"Leads Called Today"}
        />
      </Flex>
    </Flex>
  </>
);

const Telesales: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default Telesales;
