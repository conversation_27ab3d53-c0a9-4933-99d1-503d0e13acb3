import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { CustomerForm } from "../components/CustomerForm";
import { EntityLayout } from "@/features/entity";
import { useState } from "react";
import { Group } from "@mantine/core";
import { notifications } from "@mantine/notifications";

export function CustomerCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Customer"],
    Schemas["CustomerCreateDto"]
  >({ resourcePath: "/api/Customers", queryKey: "customer" });
  const { t } = useTranslation("features");

  return (
    <CustomerForm
      title={t("customers.createTitle")}
      isCreate={true}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/customers");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/customers/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
