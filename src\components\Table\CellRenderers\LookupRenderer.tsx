import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { Anchor } from "@mantine/core";
import { format, isValid, parseISO } from "date-fns";
import {
  type MRT_Column,
  type MRT_Row,
  type MRT_TableInstance,
  type MRT_Cell,
  type MRT_RowData,
  type MRT_Header,
} from "mantine-react-table";
import { type ReactNode, type RefObject } from "react";

function LookupCellRenderer(
  props: {
    cell: MRT_Cell<MRT_RowData, unknown>;
    column: MRT_Column<MRT_RowData, unknown>;
    renderedCellValue: ReactNode | number | string;
    renderedColumnIndex?: number;
    renderedRowIndex?: number;
    row: MRT_Row<MRT_RowData>;
    rowRef?: RefObject<HTMLTableRowElement>;
    table: MRT_TableInstance<MRT_RowData>;
  },
  entityPath: string,
  nameFields: string[],
) {
  const { dateFormat } = useSettingsContext();
  if (!props.cell.getValue<object>()) {
    return <></>;
  }

  const entity = props.cell.getValue<object>();
  let name = "";
  nameFields.forEach((nameField) => {
    let value = getPropertyAsStringFromObject(nameField, entity);
    if (isValidDateString(value)) {
      value = format(parseISO(value ?? ""), dateFormat);
    }
    name += value != null ? value : " ";
  });
  const url = window.location.pathname
    ? `/app/${entityPath}/${getPropertyAsStringFromObject("id", entity)}?redirectTo=${window.location.pathname}`
    : `/app/${entityPath}/${getPropertyAsStringFromObject("id", entity)}`;
  return (
    <Anchor fz={12} href={url} underline="hover" style={{ color: "primary" }}>
      {name.trim() !== "" ? name.trim() : " - "}
    </Anchor>
  );
}

const getPropertyAsStringFromObject = (field: string, obj: object): string => {
  type prop = keyof typeof obj;
  const selectedProp = field as prop;

  return obj[selectedProp];
};

const getPropertyFromObject = (field: string, obj: object): object => {
  if (field.includes(".")) {
    // Nested property
    const firstLevelField = field.split(".")[0];
    const secondLevelField = field.split(".")[1]!;
    type firstLevelProp = keyof typeof obj;
    const firstLevelValue = firstLevelField as firstLevelProp;
    const firstLevelObject = obj[firstLevelValue];
    return getPropertyFromObject(secondLevelField, firstLevelObject);
  } else {
    type prop = keyof typeof obj;
    const selectedProp = field as prop;

    return obj[selectedProp];
  }
};

interface LookupInterface {
  column: MRT_Column<MRT_RowData, unknown>;
  header: MRT_Header<MRT_RowData, unknown>;
  rangeFilterIndex?: number;
  table: MRT_TableInstance<MRT_RowData>;
}

function LookupFilter(
  props: LookupInterface,
  children: (props: {
    initial?: MRT_RowData | null;
    initialId?: string | null;
    identifier: string;
    onChange: (value: string) => void;
  }) => JSX.Element,
) {
  let existingLookup = undefined;
  if (
    props.column.getFilterValue() !== null &&
    props.column.getFilterValue() !== undefined
  ) {
    if (props.table !== null && props.table !== undefined) {
      if (
        props.table.getRowModel() !== null &&
        props.table.getRowModel() !== undefined
      ) {
        if (props.table.getRowModel().rows) {
          const rows = props.table.getRowModel().rows;
          if (rows.length > 0) {
            existingLookup = getPropertyFromObject(
              props.column.columnDef.accessorKey ?? "",
              rows[0]?.original ?? {},
            ) as MRT_RowData;
            if (existingLookup) {
              if (props.column.getFilterValue() !== existingLookup.id) {
                existingLookup = undefined;
              }
            }
          }
        }
      }
    }
  }
  const childProps = {
    initial: existingLookup,
    identifier: props.column.id + "Lookup",
    initialId:
      existingLookup !== undefined
        ? (props.column.getFilterValue() as string)
        : undefined,
    onChange: (value: string) => {
      props.column.setFilterValue(value);
    },
  };

  return children(childProps);
}

export function LookupRenderer(
  Lookup: (props: {
    initial?: MRT_RowData | null;
    initialId?: string | null;
    identifier: string;
    onChange: (value: string) => void;
  }) => JSX.Element,
  entityPath: string,
  nameFields: string[],
) {
  const filterComponent = (props: LookupInterface) =>
    LookupFilter(props, Lookup);
  const cellComponent = (props: {
    cell: MRT_Cell<MRT_RowData, unknown>;
    column: MRT_Column<MRT_RowData, unknown>;
    renderedCellValue: ReactNode | number | string;
    renderedColumnIndex?: number;
    renderedRowIndex?: number;
    row: MRT_Row<MRT_RowData>;
    rowRef?: RefObject<HTMLTableRowElement>;
    table: MRT_TableInstance<MRT_RowData>;
  }) => LookupCellRenderer(props, entityPath, nameFields);

  return {
    Filter: filterComponent,
    Cell: (props: {
      cell: MRT_Cell<MRT_RowData, unknown>;
      column: MRT_Column<MRT_RowData, unknown>;
      renderedCellValue: ReactNode | number | string;
      renderedColumnIndex?: number;
      renderedRowIndex?: number;
      row: MRT_Row<MRT_RowData>;
      rowRef?: RefObject<HTMLTableRowElement>;
      table: MRT_TableInstance<MRT_RowData>;
    }) => ({
      ...cellComponent(props),
    }),
  };
}

function isValidDateString(value: string): boolean {
  if (typeof value !== "string") {
    return false;
  }
  const date = parseISO(value ?? "");
  return isValid(date) && value.includes("-");
}
