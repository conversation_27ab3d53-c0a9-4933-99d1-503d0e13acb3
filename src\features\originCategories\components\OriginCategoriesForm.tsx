import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";

const formSchema = z.object({
  name: z.string().nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface OriginCategoriesFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function OriginCategoriesForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: OriginCategoriesFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      name: initialValues?.name ?? "",
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <FieldValidation isDirty={form.isDirty("name")}>
                <TextInput
                  label={t("originCategories.name")}
                  {...form.getInputProps("name")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
