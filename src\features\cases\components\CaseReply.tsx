import {
  type Dispatch,
  type SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  Button,
  Modal,
  Group,
  Grid,
  Loader,
  TextInput,
  Tooltip,
  ActionIcon,
  Menu,
  Box,
  Paper,
} from "@mantine/core";
import {
  IconMessage,
  IconPaperclip,
  IconPlus,
  IconTrash,
} from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import {
  EmailSend,
  EmailForward,
  type CustomErrorData,
} from "@/components/EmailSending/ProcessEmail";
import HtmlEditor, {
  type HtmlEditorRef,
} from "@/components/HtmlEditor/HtmlEditor";
import { useEntityListQuery, useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { notifications } from "@mantine/notifications";
import { type QueryClient, useQueryClient } from "react-query";
import { type ApiError } from "@/lib/api";
import EmailTemplates from "@/components/EmailSending/EmailTemplates";
import ReplyMailboxes from "@/components/EmailSending/ReplyMailboxes";

interface CaseReplyProps {
  caseId: string;
  businessUnitId: string;
  preffiledTo?: string | null;
  prefilledSubject?: string | null;
  caseStatus?: string | null;
  useModal?: boolean; // New prop to toggle modal
}

export default function CaseReply({
  caseId,
  businessUnitId,
  preffiledTo,
  prefilledSubject,
  caseStatus,
  useModal = true, // Default to using modal
}: CaseReplyProps) {
  const { t } = useTranslation("features");
  const [opened, setOpened] = useState(false);
  const [emailBody, setEmailBody] = useState("");
  const queryCache = useQueryClient();
  const [attachment, setAttachment] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      setAttachment((prevFiles) => [...prevFiles, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setAttachment((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const removeAllFiles = () => {
    setAttachment([]);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };
  const htmlEditorRef = useRef<HtmlEditorRef>(null);
  const [to, setTo] = useState(preffiledTo ?? "");
  const [cc, setCc] = useState("");
  const [bcc, setBcc] = useState("");
  const [subject, setSubject] = useState(prefilledSubject ?? "");
  const [selectedMailbox, setSelectedMailbox] = useState<string | null>("");

  const { data: mailboxes, isLoading: isLoadingMailboxes } = useEntityListQuery<
    Schemas["MailboxRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Mailboxes",
    queryKey: `mailboxes`,
    params: {
      filter: `BusinessUnit.Id == ${businessUnitId}`,
    },
    enabled: !!businessUnitId,
  });

  const { data: latestEmail } = useEntityQuery<Schemas["EmailRetrieveDto"]>({
    resourcePath: "/api/Cases/{id}/latestCaseEmail",
    resourceId: caseId,
    queryKey: [`latestCaseEmail`, caseId],
  });

  useEffect(() => {
    if (mailboxes?.data && latestEmail) {
      const matchedMailbox = mailboxes.data.find(
        (mailbox) => mailbox.email === latestEmail.to,
      )?.id;
      setSelectedMailbox(matchedMailbox ?? "");
      setCc(latestEmail.cc ?? "");
    }
  }, [mailboxes, latestEmail]);

  const prelimanaryMailbox =
    mailboxes?.totalCount === 1 ? mailboxes?.data?.at(0)?.id : null;

  const validateReply = () => {
    if (!to) {
      notifications.show({
        title: "Error",
        message: t("features:emails.toRequired"),
        color: "red",
      });
      return;
    }

    if (!subject) {
      notifications.show({
        title: "Error",
        message: t("features:emails.subjectRequired"),
        color: "red",
      });
      return;
    }

    if (!prelimanaryMailbox && !selectedMailbox) {
      notifications.show({
        title: "Error",
        message: t("features:emails.pleaseSelectMailbox"),
        color: "red",
      });
      return;
    }
  };

  const handleSendReply = async () => {
    validateReply();
    if (htmlEditorRef.current) {
      const fullHtml = htmlEditorRef.current.getFullHtml();
      await EmailSend(
        caseId,
        "Cases",
        fullHtml,
        attachment,
        to,
        "",
        "",
        subject,
        prelimanaryMailbox ?? selectedMailbox,
      )
        .then(handleReplyResponse(to, setOpened, queryCache))
        .catch(handleReplyError);
    }
  };

  const handleSendReplyAll = async () => {
    validateReply();
    if (htmlEditorRef.current) {
      const fullHtml = htmlEditorRef.current.getFullHtml();
      await EmailSend(
        caseId,
        "Cases",
        fullHtml,
        attachment,
        to,
        cc,
        bcc,
        subject,
        prelimanaryMailbox ?? selectedMailbox,
      )
        .then(handleReplyResponse(to, setOpened, queryCache))
        .catch(handleReplyError);
    }
  };

  const handleSendForward = async () => {
    validateReply();
    if (htmlEditorRef.current) {
      const fullHtml = htmlEditorRef.current.getFullHtml();
      await EmailForward(
        caseId,
        "Cases",
        fullHtml,
        attachment,
        to,
        subject,
        prelimanaryMailbox ?? selectedMailbox,
      )
        .then(handleReplyResponse(to, setOpened, queryCache))
        .catch(handleReplyError);
    }
  };

  const handleReplyResponse =
    (
      to: string,
      setOpened: Dispatch<SetStateAction<boolean>>,
      queryCache: QueryClient,
    ) =>
    () => {
      notifications.show({
        title: "Success",
        message: `Reply was sent to ${to}`,
        color: "green",
      });
      void queryCache.invalidateQueries("caseActivity_list");
      setOpened(false);
    };
  const handleReplyError = (error: ApiError<CustomErrorData>) => {
    console.error("Error sending reply:", error);
    notifications.show({
      title: "Error",
      message: error.response?.data.fallbackMessage ?? "",
      color: "red",
    });
  };

  if (isLoadingMailboxes) {
    return <Loader />;
  }

  const formContent = (
    <>
      <Paper shadow="xs" p="xs" mt="md" pt="">
        <Grid>
          <Group w="100%" mt="md" ml="md">
            <Menu trigger="click-hover">
              <Menu.Target>
                <ActionIcon
                  variant="outline"
                  size="sm"
                  color="gray"
                  w={30}
                  h={30}
                >
                  <IconPaperclip
                    style={{ width: "70%", height: "70%" }}
                    stroke={1.5}
                  />
                </ActionIcon>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  rightSection={<IconPlus size={14} />}
                  onClick={triggerFileInput}
                >
                  {t("features:downloads.addFiles")}
                </Menu.Item>
                {attachment.length > 0 &&
                  attachment.map((file, index) => (
                    <Menu.Item
                      key={index}
                      rightSection={<IconTrash size={14} />}
                      onClick={() => removeFile(index)}
                    >
                      {file.name}
                    </Menu.Item>
                  ))}
                <Menu.Item
                  rightSection={<IconTrash size={14} />}
                  onClick={removeAllFiles}
                  disabled={attachment.length === 0}
                >
                  {t("features:downloads.removeAllFiles")}
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
            <Tooltip label={t("cases.sendReplyTooltip")}>
              <Button size="xs" onClick={handleSendReply}>
                {t("cases.sendReply")}
              </Button>
            </Tooltip>
            <Button size="xs" onClick={handleSendReplyAll}>
              {t("cases.sendReplyAll")}
            </Button>
            {caseStatus != "Escalated" && (
              <Button size="xs" onClick={handleSendForward}>
                {t("cases.forward")}
              </Button>
            )}
          </Group>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <ReplyMailboxes
              businessUnitId={businessUnitId}
              prelimanaryMailbox={prelimanaryMailbox}
              onChange={setSelectedMailbox}
              value={selectedMailbox}
            />
            <TextInput
              flex={1}
              label={t("features:cases.to")}
              placeholder="To"
              value={to}
              onChange={(event) => setTo(event.currentTarget.value)}
            />

            <TextInput
              flex={6}
              label={t("features:cases.subject")}
              placeholder="Subject"
              value={subject}
              onChange={(event) => setSubject(event.currentTarget.value)}
            />
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              style={{ display: "none" }}
              multiple
              accept="all"
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <TextInput
              flex={1}
              label="CC"
              placeholder="CC"
              value={cc}
              onChange={(event) => setCc(event.currentTarget.value)}
            />
            <TextInput
              flex={2}
              label="BCC"
              placeholder="BCC"
              value={bcc}
              onChange={(event) => setBcc(event.currentTarget.value)}
            />
            <EmailTemplates
              setEmailBody={setEmailBody}
              setSubject={setSubject}
              subject={prefilledSubject}
              templateType="Cases"
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <HtmlEditor html={emailBody} ref={htmlEditorRef} />
          </Grid.Col>
        </Grid>
      </Paper>
    </>
  );

  return useModal ? (
    <>
      <Tooltip label={t("cases.reply")}>
        <Button
          onClick={() => setOpened(true)}
          variant="subtle"
          size="compact-md"
        >
          <IconMessage size={18} />
        </Button>
      </Tooltip>

      <Modal
        opened={opened}
        onClose={() => setOpened(false)}
        withCloseButton={false}
        size="74%"
        centered
        overlayProps={{
          backgroundOpacity: 0.55,
          blur: 3,
        }}
      >
        {formContent}
      </Modal>
    </>
  ) : (
    <Box>{formContent}</Box>
  );
}
