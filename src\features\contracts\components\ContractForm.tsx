import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Tabs } from "@mantine/core";
import { zodResolver } from "mantine-form-zod-resolver";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { useNavigate, useParams } from "react-router-dom";
import { GeneralTab } from "./Tabs/GeneralTab";
import { InvoicingTab } from "./Tabs/InvoicingTab";
import {
  ContractFormProvider,
  contractFormSchema,
  useContractForm,
  type ContractFormSchema,
} from "../providers/form";
import { getDirtyFormFields } from "@/features/entity/utils";

interface ContractFormProps {
  includeContractProcess?: boolean;
  onSubmit: (values: Partial<ContractFormSchema>) => void;
  actionSection?: ReactNode;
  headerSection?: ReactNode;
  initialValues?: ContractFormSchema;
  title: string;
  contractId?: string;
  isCreate: boolean;
}

export function ContractForm({
  onSubmit,
  initialValues,
  actionSection = null,
  headerSection = null,
  title,
  contractId,
  isCreate,
}: ContractFormProps) {
  const { id, tabValue } = useParams();
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const navigate = useNavigate();

  const form = useContractForm({
    initialValues: {
      contractNumber: initialValues?.contractNumber ?? "",
      invoiceReference: initialValues?.invoiceReference ?? "",
      remarks: initialValues?.remarks ?? "",
      invoicingInterval: initialValues?.invoicingInterval ?? 0,
      from: initialValues?.from ?? null,
      to: initialValues?.to ?? null,
      signedOn: initialValues?.signedOn ?? null,
      firstCancelDate: initialValues?.firstCancelDate ?? null,
      cancelledOn: initialValues?.cancelledOn ?? null,
      moveOutDate: initialValues?.moveOutDate ?? null,
      invoicePeriodFrom: initialValues?.invoicePeriodFrom ?? null,
      invoicedUntil: initialValues?.invoicedUntil ?? null,
      contactId: initialValues?.contactId ?? "",
      contact: initialValues?.contact ?? null,
      customerId: initialValues?.customerId ?? "",
      customer: initialValues?.customer ?? null,
      contractStatus: initialValues?.contractStatus ?? "Active",
      cancelReason: initialValues?.cancelReason ?? null,
      invoicingType: initialValues?.invoicingType ?? null,
      paymentMethod: initialValues?.paymentMethod ?? null,
    },
    validate: zodResolver(contractFormSchema),
  });

  return (
    <ContractFormProvider form={form}>
      <form
        onSubmit={form.onSubmit((fields) => {
          const filteredFields = getDirtyFormFields(
            fields,
            isCreate,
            form.isDirty,
          );
          debouncedOnSubmit(filteredFields);
          form.resetDirty();
        })}
      >
        <EntityLayout
          disabled={form.getInputProps("recordState").value === "Inactive"}
          title={title}
          actionSection={actionSection}
          headerSection={headerSection}
          recordState={t(
            (form.getInputProps("recordState").value as string) ?? "",
          )}
        >
          <Tabs
            defaultValue="general"
            value={tabValue ?? "general"}
            onChange={(value) => navigate(`/app/contracts/${id}/${value}`)}
          >
            <Tabs.List>
              <Tabs.Tab value="general">{t("contracts.general")}</Tabs.Tab>
              <Tabs.Tab value="invoicing">{t("contracts.invoicing")}</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="general">
              <GeneralTab contractId={contractId} />
            </Tabs.Panel>
            <Tabs.Panel value="invoicing">
              <InvoicingTab />
            </Tabs.Panel>
          </Tabs>
        </EntityLayout>
      </form>
    </ContractFormProvider>
  );
}
