import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import {
  AppointmentForm,
  type FormSchema,
} from "../components/AppointmentForm";
import { filterFalsyValues } from "@/utils/filters";
import { notifications } from "@mantine/notifications";
import { Box, Center, Group, Loader } from "@mantine/core";
import { useParams } from "react-router-dom";
import { api } from "@/lib/api";
import { EntityLayout } from "@/features/entity";
import { useQueryClient } from "react-query";
import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";

interface AppointmentShowProps {
  refreshForm?: () => void;
  closeModal?: () => void;
  isModal?: boolean;
  fromCalendar?: boolean;
  appointmentId?: string | null;
}

export function AppointmentShow({
  refreshForm,
  closeModal,
  isModal,
  fromCalendar,
  appointmentId,
}: AppointmentShowProps) {
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const idToUse = appointmentId ?? id;
  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Appointment"]>({
    resourcePath: "/api/Appointments/{id}",
    resourceId: idToUse!,
    queryKey: "appointment",
  });
  const { roles } = useUserContext();
  const isAdmin = roles.includes("Admin");
  const queryCache = useQueryClient();
  const refreshAppointmentForm = async () => {
    refreshForm ?? (await queryCache.invalidateQueries("appointment_" + id));
  };
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Appointment"],
    Schemas["AppointmentCreateDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: idToUse!,
    queryKey: "appointment",
  });

  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/Appointments/{id}",
    resourceId: idToUse!,
    queryKey: "appointmentDelete",
  });

  if (isLoading || isFetching) {
    return (
      <Center h={"100%"}>
        <Loader />
      </Center>
    );
  }

  async function AppointmentAddAttachments(
    appointmentId: string,
    attachment: File[] | null,
  ) {
    const url = `/api/appointments/${appointmentId}/attachments`;
    const formData = new FormData();
    if (attachment && attachment.length > 0) {
      attachment.forEach((file) => {
        formData.append(`Files`, file);
      });
    }
    try {
      const response = await api.post(url, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response;
    } catch (error) {
      console.error("Error in CaseSendReply:", error);
      throw error;
    }
  }

  return (
    <Box
      m={8}
      style={{
        borderRadius: "8px",
        backgroundColor: "#FDFDFD",
        border: "2px solid #f5f4f3",
        padding: "8px",
      }}
      h="100%"
    >
      <AppointmentForm
        isCreate={false}
        isModal={isModal}
        fromCalendar={fromCalendar}
        closeModal={closeModal}
        isAdmin={isAdmin}
        actionSection={
          <Group>
            {!fromCalendar && !isModal && (
              <EntityLayout.RefreshButton clicked={refreshAppointmentForm} />
            )}
            {isAdmin && (
              <EntityLayout.DeleteButton
                modalTitle={t("appointments.delete", { id })}
                modalContent={t("appointments.deleteConfirmation", { id })}
                confirmLabel={t("appointments.delete", { id })}
                onClick={async () => {
                  await mutateAsync();
                  if (!isDeleteError) {
                    notifications.show({
                      color: "green",
                      title: t("notifications.deleteSuccessTitle"),
                      message: t("notifications.deleteSuccessMessage"),
                    });
                    if (closeModal) {
                      closeModal();
                    }
                    if (refreshForm) {
                      refreshForm();
                    }
                  }
                }}
              />
            )}
          </Group>
        }
        initialValues={
          filterFalsyValues({
            ...data,
            startDate: data.startDate ? new Date(data.startDate) : null,
            endDate: data.endDate ? new Date(data.endDate) : null,
            createPhoneCallFlag: false,
          }) as FormSchema
        }
        onSubmit={(values, attachment, id) => {
          if (Object.keys(values).length === 0) {
            return;
          }

          if (attachment && attachment.length > 0) {
            void AppointmentAddAttachments(id!, attachment);
          }
          update(values as Schemas["AppointmentCreateDto"], {
            onSuccess: () => {
              if (refreshForm) {
                refreshForm();
              }
              void queryCache.invalidateQueries(`lead_${data?.leadId}`);
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            },
          });
        }}
      />
    </Box>
  );
}
