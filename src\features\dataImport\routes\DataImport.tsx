import { useState } from "react";
import {
  Button,
  Group,
  Text,
  Box,
  Stack,
  Center,
  ScrollArea,
} from "@mantine/core";
import { api } from "@/lib/api";
import { Dropzone } from "@mantine/dropzone";
import {
  IconFileTypeCsv,
  IconTableImport,
  IconUpload,
  IconX,
} from "@tabler/icons-react";
import classes from "./DataImport.module.css";
import { useTranslation } from "react-i18next";

interface ResponseMessage {
  timestamp: string;
  message: string;
}

function isAxiosError(
  error: unknown,
): error is { response?: { data?: { detail?: string } } } {
  return (
    typeof error === "object" &&
    error !== null &&
    "response" in error &&
    typeof (error as { response?: unknown }).response === "object" &&
    "data" in (error as { response: { data?: unknown } }).response &&
    typeof (error as { response: { data: unknown } }).response.data === "object"
  );
}

export function DataImport() {
  const [responseMessages, setResponseMessages] = useState<ResponseMessage[]>(
    [],
  );
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [importStatus, setImportStatus] = useState<string>("");
  const [statusColor, setStatusColor] = useState<string>(
    "var(--mantine-color-neutral-6)",
  );

  const { t } = useTranslation("features");

  const addResponseMessage = (message: string) => {
    const timestamp = new Date().toISOString();

    setResponseMessages((prevMessages) => [
      { timestamp, message },
      ...prevMessages,
    ]);
  };

  const handleUpload = async () => {
    if (!file) {
      addResponseMessage(t("dataImport.messageMissingCSV"));
      setImportStatus(t("dataImport.messageMissingCSV"));
      setStatusColor("var(--mantine-color-neutral-6)");
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const url = "/api/importData/leads";
      const response = await api.post(url, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const count = Array.isArray(response.data) ? response.data.length : 0;

      const responseMessage = count
        ? `${t("dataImport.messageSuccess")} ${count}`
        : `${t("dataImport.messageNoItems")}`;
      addResponseMessage(responseMessage);
      setImportStatus(t("dataImport.statusSuccess"));
      setStatusColor("var(--mantine-color-green-4)");

      setFile(null);
    } catch (err: unknown) {
      setImportStatus(t("dataImport.statusFailed"));
      setStatusColor("var(--mantine-color-red-6)");
      if (isAxiosError(err) && err.response?.data?.detail) {
        const errorMessage = err.response.data.detail;
        addResponseMessage(errorMessage || t("dataImport.messageUploadFailed"));
      } else {
        addResponseMessage(t("dataImport.messageUploadFailed"));
      }
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (files: File[]) => {
    if (files.length > 0 && files[0]) {
      setFile(files[0]);
    }
  };

  return (
    <>
      <Box p={64} maw={1200} mx="auto" mt={32}>
        <Text my={16} className={classes.displayText}>
          {t("dataImport.title")}
        </Text>
        <Stack gap={2}>
          <Text className={classes.footerText}>
            {t("dataImport.description")}
            <Text span className={classes.footerBoldText}>
              {t("dataImport.columns")}
            </Text>
          </Text>
        </Stack>
        <Dropzone
          onDrop={handleDrop}
          onReject={(files) => console.log("rejected files", files)}
          accept={["text/csv"]}
          maxFiles={1}
          loading={uploading}
          acceptColor="var(--mantine-color-green-4)"
          rejectColor="var(--mantine-color-red-6)"
          my={32}
        >
          <Group
            justify="center"
            gap={16}
            mih={220}
            style={{ pointerEvents: "none" }}
          >
            <Dropzone.Accept>
              <IconUpload size={42} stroke={1.5} />
            </Dropzone.Accept>
            <Dropzone.Reject>
              <IconX size={42} stroke={1.5} />
            </Dropzone.Reject>
            <Dropzone.Idle>
              <IconFileTypeCsv
                size={42}
                color="var(--mantine-color-dimmed)"
                stroke={1.5}
              />
            </Dropzone.Idle>

            <Stack gap={2}>
              <Text className={classes.boldText}>
                {t("dataImport.dropzoneTitle")}
              </Text>
              <Text className={classes.footerText}>
                {t("dataImport.dropzoneSelected")}
                <Text span className={classes.footerBoldText}>
                  {file?.name || "..."}
                </Text>
              </Text>
            </Stack>
          </Group>
        </Dropzone>
        <Center m={32}>
          <Button
            onClick={handleUpload}
            disabled={!file || uploading}
            loading={uploading}
            leftSection={<IconTableImport size={16} />}
          >
            {t("dataImport.importButton")}
          </Button>
        </Center>
        <Box mt={16}>
          <Text my={16} className={classes.boldText}>
            {t("dataImport.importResults")}
            <Text
              span
              my={16}
              className={classes.boldText}
              style={{ color: statusColor }}
            >
              {importStatus}
            </Text>
          </Text>

          <ScrollArea
            h={320}
            type="never"
            scrollbars="y"
            className={classes.scrollArea}
          >
            {responseMessages.map((item, index) => (
              <Stack
                key={index}
                justify="flex-start"
                gap={2}
                mb={16}
                align="flex-start"
              >
                <Text
                  key={index}
                  className={classes.footerBoldText}
                  style={{ whiteSpace: "pre-line" }}
                >
                  {item.timestamp}
                </Text>
                <Text
                  key={index}
                  className={classes.footerText}
                  style={{ whiteSpace: "pre-line" }}
                >
                  {item.message}
                </Text>
              </Stack>
            ))}
          </ScrollArea>
        </Box>
      </Box>
    </>
  );
}
