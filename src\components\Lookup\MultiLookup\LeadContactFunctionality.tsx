/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
export function HasLeadBeenChosen(
  options: (JSX.Element | null)[] | undefined,
  mainValue: string,
) {
  if (options) {
    try {
      const match = options.find((x) => x?.key == mainValue);
      if (match) {
        const isLead =
          match?.props.children.props.children.find(
            (x: JSX.Element) => x.key == "lead_" + mainValue,
          ).props.children.props.children.props.checked == true;
        return isLead;
      }
    } catch {
      return undefined;
    }
  }
  return undefined;
}
