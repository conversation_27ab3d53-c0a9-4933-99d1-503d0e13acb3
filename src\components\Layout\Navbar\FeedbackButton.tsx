import { But<PERSON> } from "@mantine/core";
import {
  type BrowserClient,
  type feedbackIntegration,
  getClient,
} from "@sentry/react";
import { IconSpeakerphone } from "@tabler/icons-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { CustomFeedbackModal } from "./CustomFeedbackModal";

type FeedbackIntegration = ReturnType<typeof feedbackIntegration>;

export function FeedbackButton() {
  const [modalOpened, setModalOpened] = useState(false);
  const client = getClient<BrowserClient>();
  const { t } = useTranslation("common");
  const feedback =
    client?.getIntegrationByName<FeedbackIntegration>("Feedback");

  if (!feedback) {
    return null;
  }

  return (
    <>
      <Button
        variant="light"
        color="red"
        size="xs"
        justify="space-between"
        radius="xl"
        rightSection={<IconSpeakerphone size={16} />}
        onClick={() => setModalOpened(true)}
      >
        {t("navbar.reportBug")}
      </Button>
      <CustomFeedbackModal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
      />
    </>
  );
}
