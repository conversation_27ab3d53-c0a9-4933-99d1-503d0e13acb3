import { Route, Routes } from "react-router-dom";
import { RentableItemList } from "./RentableItemList";
import { RentableItemShow } from "./RentableItemShow";
import { RentableItemCreate } from "./RentableItemCreate";

export default function RentableItemsRoutes() {
  return (
    <Routes>
      <Route index element={<RentableItemList />} />
      <Route path=":id" element={<RentableItemShow />} />
      <Route path="create" element={<RentableItemCreate />} />
    </Routes>
  );
}
