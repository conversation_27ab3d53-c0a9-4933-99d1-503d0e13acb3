import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function CaseCommentsColumnsCase() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "text",
      header: t("caseComments.text"),
      filterVariant: "text",
    },
    {
      accessorKey: "owner",
      header: t("caseComments.owner"),
      filterVariant: "text",
      ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
    },
  ];
  return columns;
}
