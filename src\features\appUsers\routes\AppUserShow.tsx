import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { PageLoader } from "@/components/PageLoader";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";
import { type FormSchema, AppUserForm } from "../components/AppUserForm";

export function AppUserShow() {
  const queryCache = useQueryClient();
  const navigate = useNavigate();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["AppUserPatchDto"]
  >({
    resourcePath: "/api/AppUsers/{id}",
    resourceId: id!,
    queryKey: "appUser",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["AppUserRetrieveDto"]>({
    resourcePath: "/api/AppUsers/{id}",
    resourceId: id!,
    queryKey: "appUser",
  });
  const refreshForm = async () => {
    await queryCache.invalidateQueries("appUser_" + id);
  };
  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <AppUserForm
      isCreate={false}
      title={t("appUsers.showTitle", { id })}
      appUserId={data.id}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/appUsers");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(
          { ...filteredValues },
          {
            onSuccess: () => {
              if (close) {
                navigate("/app/appUsers");
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.updateSuccessTitle"),
                  message: t("notifications.updateSuccessMessage"),
                });
              }
            },
          },
        );
      }}
      initialValues={
        filterFalsyValues({
          ...data,
        }) as FormSchema
      }
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
      headerSection={<Group></Group>}
    />
  );
}
