import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import LeadContractButton from "@/features/leads/components/LeadContractButton";
import { useDisclosure } from "@mantine/hooks";
import { useState } from "react";

const PATH = "Contracts";

interface ContractListInnerProps extends InnerListProps {
  lead: Schemas["LeadRetrieveDto"];
  leadId: string;
}

export function ContractListInner({
  resourcePath,
  lead,
  leadId,
}: ContractListInnerProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  const [opened, { open, close }] = useDisclosure(false);
  const [activeWizard, setActiveWizard] = useState<string | null>(null);

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["ContractRetrieveDto"],
        Schemas["ContractRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="Contract"
        disableNavigation
        customNavigation
        customNavigate={(wizardId: string) => {
          setActiveWizard(wizardId);
          open();
        }}
        entityPath="contracts"
        title={t("contracts.title")}
        toolbar={
          <Group>
            <LeadContractButton
              lead={lead}
              leadId={leadId}
              activeWizard={activeWizard}
              setActiveWizard={setActiveWizard}
              opened={opened}
              close={close}
              open={open}
            />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "contractNumber",
            header: t("contracts.contractNumber"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "contracts"),
          },
          {
            accessorKey: "status",
            header: t("contracts.contractStatus"),
            filterVariant: "text",
          },
          {
            accessorKey: "reservationDate",
            header: t("contracts.reservationDate"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
          {
            accessorKey: "modifiedOn",
            header: t("contracts.modifiedOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "contractNumber",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

interface ContractListProps extends OutterListProps {
  lead: Schemas["LeadRetrieveDto"];
  leadId: string;
}

export function ContractList({
  parentEntityName,
  lead,
  leadId,
  parentEntityId,
  parentEntityIdParam,
}: ContractListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <ContractListInner
        resourcePath={resourcePath}
        createPath={createPath}
        lead={lead}
        leadId={leadId}
      />
    </ListCommandsProvider>
  );
}
