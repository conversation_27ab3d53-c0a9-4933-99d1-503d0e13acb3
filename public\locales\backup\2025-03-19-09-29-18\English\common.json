{"dashboards": {"selectDashboard": "Choose Dashboard"}, "entity": {"save": "Save"}, "commonActions": {"newAppointment": "New Appointment", "newCase": "New Case", "newLead": "New Lead", "feedback": "Forum"}, "navbar": {"advisedProducts": "Advised Products", "advisedUnits": "Advised Units", "appSettings": "App Settings", "appUsers": "App Users", "businessUnits": "Business Units", "calendar": "Calendar", "caseComments": "Case Comments", "caseReasons": "Case Reasons", "cases": "Cases", "complaintReasons": "Complaint Reasons", "connections": "Connections", "contactRoles": "Contact Roles", "contacts": "Contacts", "contractLines": "Contract Lines", "contractManagement": "Contract Management", "contracts": "Contracts", "countries": "Countries", "customers": "Customers", "dashboards": "Dashboards", "home": "Home", "htmlTemplates": "Html Templates", "leads": "Leads", "lossReasons": "Loss Reasons", "locations": "Locations", "mailboxes": "Mailboxes", "omnichannel": "Omnichannel", "originCategories": "Origin Categories", "origins": "Origins", "products": "Products", "quotes": "Quotes", "refunds": "Refunds", "reservations": "Reservations", "roles": "Roles", "security": "Security", "reportBug": "Report a Bug", "service": "Service", "settings": "Settings", "storageTypes": "Storage Types", "tags": "Tags", "unitTypes": "Unit Types", "units": "Units", "userGroups": "User Groups", "work": "Work"}, "notFound": {"description": "Unfortunately, this is only a 404 page. You may have mistyped the address, or the page has been moved to another URL.", "goBack": "Take me back to home page", "logout": "Logout", "title": "You have found a secret place."}, "forbidden": {"description": "Unfortunately, this is only a 403 page. Which means you do not have the right permissions to view this page/resource.", "goBack": "Take me back to home page", "logout": "Logout", "title": "You do not have the right permissions."}, "userMenu": {"application": "Application", "feedback": "Feedback forum", "language": "Language", "logout": "Logout"}, "validation": {"invalidEmail": "Invalid email address", "invalidPhone": "Invalid phone number"}}