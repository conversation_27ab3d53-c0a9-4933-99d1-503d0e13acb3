import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { DateRenderer } from "@/components/Table/CellRenderers";
import { ListCommandsProvider } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";

const PATH = "Tags";
export function TagListInner({ resourcePath, createPath }: InnerListProps) {
  const { t } = useTranslation("features");

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["TagRetrieveDto"],
        Schemas["TagRetrieveDtoPagedList"]
      >
        resourcePath={resourcePath as PathKeys}
        queryKey="tag"
        entityPath="tags"
        title={t("tags.title")}
        toolbar={
          <>
            <EntityLayout.CreateButton to={createPath} />
          </>
        }
        columns={[
          {
            accessorKey: "name",
            header: t("tags.name"),
            filterVariant: "text",
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}
export function TagList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <TagListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
