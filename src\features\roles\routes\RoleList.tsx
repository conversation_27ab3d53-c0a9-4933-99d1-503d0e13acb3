import { EntityLayout } from "@/features/entity";
import { EntityLinkRenderer } from "@/components/Table/CellRenderers/EntityLinkRenderer";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";

export function RoleList() {
  const { t } = useTranslation("features");

  return (
    <>
      <EntityLayout stickyHeader={false}>
        <EntityLayout.TableMantine<
          Schemas["RoleDto"],
          Schemas["RoleDtoPagedList"]
        >
          resourcePath={`/auth/roles` as PathKeys}
          queryKey="rolesMenu"
          entityPath="roles"
          title={t("roles.title")}
          redirectTo={window.location.pathname}
          columns={[
            {
              accessorKey: "name",
              header: t("roles.name"),
              filterVariant: "text",
              Cell: (props) => EntityLinkRenderer(props, "roles"),
            },
            {
              accessorKey: "description",
              header: t("roles.description"),
              filterVariant: "text",
              Cell: (props) => EntityLinkRenderer(props, "roles"),
            },
            {
              accessorKey: "id",
              header: t("roles.id"),
              filterVariant: "text",
            },
          ]}
          initialSorting={[
            {
              id: "createdOn",
              desc: true,
            },
          ]}
        />
      </EntityLayout>
    </>
  );
}
