import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { t } from "i18next";

const ENTITY = "emails";
type ENTITY_TYPE = Schemas["EmailRetrieveDto"];

type EmailLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function EmailLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  ...props
}: EmailLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.subject,
    initialId,
    initial,
    true,
  );

  const queryParams: QueryParams = {
    pageSize: 50,
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["EmailRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/Emails",
      queryKey: "email",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.subject")}</Box>
      <Box>{t("common:lookup.from")}</Box>
      <Box>{t("common:lookup.to")}</Box>
    </React.Fragment>
  );
  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.subject, searchTerm, `subject_${entity.id}`),
        OptionRender(entity.from, searchTerm, `from_${entity.id}`),
        OptionRender(entity.to, searchTerm, `to_${entity.id}`),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.subject!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
