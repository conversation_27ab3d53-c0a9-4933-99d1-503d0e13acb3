import type React from "react";
import { Grid } from "@mantine/core";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import Card<PERSON>hart from "../common/CardChart";
import CustomBarChartWithLine from "../common/CustomBarChartWithLine";

const metrics: MetricProps[] = [
  { metric: "AllAppointments" },
  { metric: "FutureAppointments" },
  { metric: "ConversionSiteVisits" },
  { metric: "PastAppointments" },
  { metric: "TotalSiteVisits" },
  { metric: "ConversionReserverUnit" },
  { metric: "ReservedUnitInAFO" },
  { metric: "TotalOpenOpportunities" },
  { metric: "OpenCalls" },
  { metric: "OpenNoShows" },
  { metric: "ConversionLost" },
  { metric: "LostWithoutSpaceTourNoShow" },
  { metric: "LostPercentage" },
  { metric: "Lost" },
  { metric: "OverviewSiteVisits" },
];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <>
    <Grid gutter={"xs"}>
      <Grid.Col span={12}>
        <Grid gutter={"xs"}>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[0]![0]?.total.toString() ?? "0"}
              title={"All Appointments"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[1]![0]?.total.toString() ?? "0"}
              title={"Future Appointments"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[2]![0]?.total.toString() ?? "0"}
              title={"Conversion Site Visits"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[3]![0]?.total.toString() ?? "0"}
              title={"Past Appointments"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[4]![0]?.total.toString() ?? "0"}
              title={"Total Site Visits"}
            />
          </Grid.Col>
        </Grid>
      </Grid.Col>
      <Grid.Col span={12}>
        <Grid gutter={"xs"}>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[5]![0]?.total.toString() ?? "0"}
              title={"Conversion Reserver Unit"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[6]![0]?.total.toString() ?? "0"}
              title={"Reserver Unit In AFO"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[7]![0]?.total.toString() ?? "0"}
              title={"Total Open Opportunities"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[8]![0]?.total.toString() ?? "0"}
              title={"Open Calls"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[9]![0]?.total.toString() ?? "0"}
              title={"Open No Shows"}
            />
          </Grid.Col>
        </Grid>
      </Grid.Col>
      <Grid.Col span={12}>
        <Grid>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[10]![0]?.total.toString() ?? "0"}
              title={"Conversions Lost"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              value={data[11]![0]?.total.toString() ?? "0"}
              title={"Lost Without SpaceTour"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              marginTop={4}
              percentage
              value={data[12]![0]?.total.toString() ?? "0"}
              title={"Lost Percentage"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}>
            <CardChart
              value={data[13]![0]?.total.toString() ?? "0"}
              marginTop={4}
              title={"Lost"}
            />
          </Grid.Col>
          <Grid.Col span={2.4}></Grid.Col>
        </Grid>
      </Grid.Col>
    </Grid>
    <CustomBarChartWithLine
      data={data[14]}
      lineLabel="No Shows"
      barLabel="Site Visits"
      marginTop={4}
      height={400}
      title="Overview of Site Visits"
    />
  </>
);

const Bezoekafspraken: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default Bezoekafspraken;
