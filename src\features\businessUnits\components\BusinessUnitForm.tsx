import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import i18next from "i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  ColorInput,
  Grid,
  NumberInput,
  Paper,
  Select,
  TextInput,
  Textarea,
  Text,
  Stack,
  Box,
  Checkbox,
} from "@mantine/core";
import { type ReactNode } from "react";
import validator from "validator";
import "@mantine/tiptap/styles.css";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { UnitList } from "@/features/units/routes/UnitList";
import { MailboxesList } from "@/features/mailboxes/routes/MailboxesList";
import { getEnumTransKey } from "@/utils/trans";
import classes from "./businessUnitForm.module.css";
import {
  AccessControlType,
  ParkingSpotsType,
  PromotionalAction,
  EntityState,
  BusinessUnitType,
} from "@/types/enums";
import { DatePickerInput } from "@mantine/dates";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { CountryLookup } from "@/components/Lookup/Features/Countries/CountryLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { config } from "@/config";

const formSchema = z.object({
  id: z.string(),
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string(),
  division: z.string(),
  website: z.string(),
  email: z.string().refine(
    (value) => {
      if (value) {
        return validator.isEmail(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidEmail") },
  ),
  mainPhone: z.string().refine(
    (value) => {
      if (value) {
        return validator.isMobilePhone(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidPhone") },
  ),
  street: z.string(),
  zip: z.string(),
  city: z.string(),
  parentBusinessId: z.string().nullable(),
  parentBusiness: z.object({}).nullable(),
  managerId: z.string().nullable(),
  manager: z.object({}).nullable(),
  countryId: z.string().nullable(),
  country: z.object({}).nullable(),
  recognitionPoints: z.string(),
  locationName: z.string(),
  roadwayHeight: z.coerce.number().nullable(),
  driveInHeightUnit: z.coerce.number().nullable(),
  parkingLength: z.coerce.number().nullable(),
  parkingLotOutside: z.boolean().default(true),
  region: z.string(),
  costCentreCode: z.string(),
  afoMigrationId: z.coerce.number().nullable(),
  administrationCode: z.string(),
  iban: z.string(),
  objectID: z.coerce.number().nullable(),
  usesTKB: z.boolean().default(false),
  isMrBox: z.boolean().default(false),
  isHidden: z.boolean().default(false),
  isSelfStorage: z.boolean().default(true),
  hasEngineRoom: z.boolean().default(false),
  hasRoboticStorage: z.boolean().default(false),
  hasVault: z.boolean().default(false),
  usesDynamicPricing: z.boolean().default(false),
  dateOfOpening: z.date().nullable(),
  recordState: z.enum(EntityState as [string]).nullable(),
  type: z.enum(BusinessUnitType as [string]).nullable(),
  accessControlType: z.enum(AccessControlType as [string]).nullable(),
  parkingSpotsType: z.enum(ParkingSpotsType as [string]).nullable(),
  promotionalAction: z.enum(PromotionalAction as [string]).nullable(),
  districtManagerId: z.string().nullable(),
  districtManager: z.object({}).nullable(),
  seniorManagerId: z.string().nullable(),
  seniorManager: z.object({}).nullable(),
  color: z.string().nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface BusinessUnitFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  isCreate: boolean;
}

export function BusinessUnitForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: BusinessUnitFormProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      code: initialValues?.code ?? "",
      name: initialValues?.name ?? "",
      description: initialValues?.description ?? "",
      division: initialValues?.division ?? "",
      website: initialValues?.website ?? "",
      mainPhone: initialValues?.mainPhone ?? "",
      email: initialValues?.email ?? "",
      street: initialValues?.street ?? "",
      zip: initialValues?.zip ?? "",
      city: initialValues?.city ?? "",
      parentBusinessId: initialValues?.parentBusinessId ?? "",
      parentBusiness: initialValues?.parentBusiness ?? null,
      managerId: initialValues?.managerId ?? "",
      manager: initialValues?.manager ?? null,
      countryId: initialValues?.countryId ?? "",
      country: initialValues?.country ?? null,
      recognitionPoints: initialValues?.recognitionPoints ?? "",
      locationName: initialValues?.locationName ?? "",
      roadwayHeight: initialValues?.roadwayHeight ?? null,
      driveInHeightUnit: initialValues?.driveInHeightUnit ?? null,
      parkingLength: initialValues?.parkingLength ?? null,
      parkingLotOutside: initialValues?.parkingLotOutside ?? true,
      region: initialValues?.region ?? "",
      costCentreCode: initialValues?.costCentreCode ?? "",
      afoMigrationId: initialValues?.afoMigrationId ?? null,
      administrationCode: initialValues?.administrationCode ?? "",
      iban: initialValues?.iban ?? "",
      objectID: initialValues?.objectID ?? null,
      usesTKB: initialValues?.usesTKB ?? false,
      isMrBox: initialValues?.isMrBox ?? false,
      isHidden: initialValues?.isHidden ?? false,
      isSelfStorage: initialValues?.isSelfStorage ?? true,
      hasEngineRoom: initialValues?.hasEngineRoom ?? false,
      hasRoboticStorage: initialValues?.hasRoboticStorage ?? false,
      hasVault: initialValues?.hasVault ?? false,
      usesDynamicPricing: initialValues?.usesDynamicPricing ?? false,
      dateOfOpening: initialValues?.dateOfOpening ?? null,
      recordState: initialValues?.recordState ?? null,
      type: initialValues?.type ?? null,
      accessControlType: initialValues?.accessControlType ?? null,
      parkingSpotsType: initialValues?.parkingSpotsType ?? null,
      promotionalAction: initialValues?.promotionalAction ?? null,
      districtManagerId: initialValues?.districtManagerId ?? "",
      districtManager: initialValues?.districtManager ?? null,
      seniorManagerId: initialValues?.seniorManagerId ?? "",
      seniorManager: initialValues?.seniorManager ?? null,
      color: initialValues?.color ?? "#6082B6",
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid m={16}>
          <Grid.Col span={{ base: 12, md: 10 }} pb={24}>
            <Grid mb={16}>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Stack gap="lg" h={"100%"}>
                  <Paper shadow="xs" p="lg" h={"100%"}>
                    <Text size="sm" fw={700} mb={8}>
                      {t("businessUnits.sectionDetails")}
                    </Text>
                    <TextInput
                      required
                      label={t("businessUnits.code")}
                      {...form.getInputProps("code")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      required
                      label={t("businessUnits.name")}
                      {...form.getInputProps("name")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />

                    <TextInput
                      label={t("businessUnits.iban")}
                      {...form.getInputProps("iban")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      label={t("businessUnits.costCentreCode")}
                      {...form.getInputProps("costCentreCode")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      disabled
                      label={t("businessUnits.afoMigrationId")}
                      {...form.getInputProps("afoMigrationId")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <NumberInput
                      label={t("businessUnits.objectID")}
                      {...form.getInputProps("objectID")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />

                    <DatePickerInput
                      valueFormat={dateFormat.toUpperCase()}
                      clearable
                      label={t("businessUnits.dateOfOpening")}
                      {...form.getInputProps("dateOfOpening")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <Select
                      searchable
                      label={t("businessUnits.accessControlType")}
                      data={AccessControlType.map((value) => ({
                        value,
                        label: t(getEnumTransKey("businessUnits", value)),
                      }))}
                      clearable
                      {...form.getInputProps("accessControlType")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />

                    <Select
                      searchable
                      label={t("businessUnits.recordState")}
                      data={EntityState.map((value: string) => ({
                        value,
                        label: t(getEnumTransKey("businessUnits", value)),
                      }))}
                      {...form.getInputProps("recordState")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <Select
                      searchable
                      label={t("businessUnits.promotionalAction")}
                      data={PromotionalAction.map((value) => ({
                        value,
                        label: t(getEnumTransKey("businessUnits", value)),
                      }))}
                      clearable
                      {...form.getInputProps("promotionalAction")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <Textarea
                      label={t("businessUnits.description")}
                      {...form.getInputProps("description")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                      minRows={4}
                      autosize
                    />
                  </Paper>
                </Stack>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Stack gap="lg" h={"100%"}>
                  <Paper shadow="xs" p="lg">
                    <Text size="sm" fw={700} mb={8}>
                      {t("businessUnits.sectionContacts")}
                    </Text>
                    <TextInput
                      label={t("businessUnits.mainPhone")}
                      {...form.getInputProps("mainPhone")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      label={t("businessUnits.email")}
                      {...form.getInputProps("email")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      label={t("businessUnits.website")}
                      {...form.getInputProps("website")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                  </Paper>
                  <Paper shadow="xs" p="lg" h={"100%"}>
                    <Text size="sm" fw={700} mb={8}>
                      {t("businessUnits.sectionManagement")}
                    </Text>
                    <BusinessUnitLookup
                      label={t("businessUnits.parentBusiness")}
                      initial={form.getValues().parentBusiness}
                      initialId={form.getValues().parentBusinessId}
                      identifier="parentBusinessIdBusinessUnit"
                      {...form.getInputProps("parentBusinessId")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <AppUserLookup
                      required
                      showFullName
                      label={t("businessUnits.manager")}
                      initial={form.getValues().manager}
                      initialId={form.getValues().managerId}
                      identifier="managerIdBusinessUnit"
                      {...form.getInputProps("managerId")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <AppUserLookup
                      label={t("businessUnits.districtManager")}
                      initial={form.getValues().districtManager}
                      initialId={form.getValues().districtManagerId}
                      identifier="districtManagerIdBusinessUnit"
                      {...form.getInputProps("districtManagerId")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <AppUserLookup
                      label={t("businessUnits.seniorManager")}
                      initial={form.getValues().seniorManager}
                      initialId={form.getValues().seniorManagerId}
                      identifier="seniorManagerIdBusinessUnit"
                      {...form.getInputProps("seniorManagerId")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                  </Paper>
                  <Paper shadow="xs" p="lg" h={"100%"}>
                    <ColorInput
                      classNames={{
                        label: classes.colorInputLabel,
                      }}
                      swatches={[
                        "#42a5f5",
                        "#26c6da",
                        "#26a69a",
                        "#66bb6a",
                        "#9ccc65",
                        "#d4e157",
                        "#ffee58",
                        "#ffca28",
                        "#ffa726",
                        "#ff7043",
                        "#f44336",
                        "#ec407a",
                        "#ba68c8",
                        "#9575cd",
                      ]}
                      label={t("businessUnits.businessUnitColor")}
                      {...form.getInputProps("color")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                  </Paper>
                </Stack>
              </Grid.Col>
            </Grid>
            <Paper shadow="xs">
              <Grid>
                <Grid.Col span={{ base: 12, md: 6 }}>
                  <Box p="lg" h={"100%"}>
                    <Text size="sm" fw={700} mb={8}>
                      {t("businessUnits.sectionLocation")}
                    </Text>
                    <TextInput
                      label={t("businessUnits.street")}
                      {...form.getInputProps("street")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      label={t("businessUnits.zip")}
                      {...form.getInputProps("zip")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      label={t("businessUnits.city")}
                      {...form.getInputProps("city")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <CountryLookup
                      label={t("businessUnits.country")}
                      initial={form.getValues().country}
                      initialId={form.getValues().countryId}
                      identifier="countryIdBusinessUnit"
                      {...form.getInputProps("countryId")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                      shiftLeft="20vw"
                    />
                    <TextInput
                      label={t("businessUnits.division")}
                      {...form.getInputProps("division")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <Textarea
                      label={t("businessUnits.recognitionPoints")}
                      {...form.getInputProps("recognitionPoints")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                      minRows={4}
                      autosize
                    />
                  </Box>
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 6 }}>
                  <Box p="lg" h={"100%"}>
                    <Text
                      size="sm"
                      fw={700}
                      mb={8}
                      style={{ visibility: "hidden" }}
                    >
                      {t("businessUnits.sectionParking")}
                    </Text>
                    <TextInput
                      label={t("businessUnits.locationName")}
                      {...form.getInputProps("locationName")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <TextInput
                      label={t("businessUnits.region")}
                      {...form.getInputProps("region")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <NumberInput
                      label={t("businessUnits.roadwayHeight")}
                      leftSection={config.METERS.symbol}
                      {...form.getInputProps("roadwayHeight")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <NumberInput
                      label={t("businessUnits.driveInHeightUnit")}
                      leftSection={config.METERS.symbol}
                      {...form.getInputProps("driveInHeightUnit")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <NumberInput
                      label={t("businessUnits.parkingLength")}
                      leftSection={config.METERS.symbol}
                      {...form.getInputProps("parkingLength")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <Select
                      searchable
                      label={t("businessUnits.parkingSpotsType")}
                      data={ParkingSpotsType.map((value) => ({
                        value,
                        label: t(getEnumTransKey("businessUnits", value)),
                      }))}
                      clearable
                      {...form.getInputProps("parkingSpotsType")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                    <Checkbox
                      classNames={{ icon: classes.icon }}
                      mt="sm"
                      label={t("businessUnits.parkingLotOutside")}
                      checked={form.getValues().parkingLotOutside}
                      {...form.getInputProps("parkingLotOutside")}
                      {...{ labelProps: { style: { flex: 1.9 } } }}
                    />
                  </Box>
                </Grid.Col>
              </Grid>
            </Paper>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 2 }}>
            <Stack gap="lg" h={"100%"} pb={8}>
              <Paper shadow="xs" p="lg">
                <Text size="sm" fw={700} mb={8}>
                  {t("businessUnits.sectionFeatures")}
                </Text>
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.usesTKB")}
                  description={t("businessUnits.usesTKBDescription")}
                  checked={form.getValues().usesTKB}
                  {...form.getInputProps("usesTKB")}
                />
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.isMrBox")}
                  description={t("businessUnits.isMrBoxDescription")}
                  checked={form.getValues().isMrBox}
                  {...form.getInputProps("isMrBox")}
                />
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.isHidden")}
                  description={t("businessUnits.isHiddenDescription")}
                  checked={form.getValues().isHidden}
                  {...form.getInputProps("isHidden")}
                />
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.usesDynamicPricing")}
                  description={t("businessUnits.usesDynamicPricingDescription")}
                  checked={form.getValues().usesDynamicPricing}
                  {...form.getInputProps("usesDynamicPricing")}
                />
              </Paper>
              <Paper shadow="xs" p="lg" h={"100%"}>
                <Text size="sm" fw={700}>
                  {t("businessUnits.sectionTypes")}
                </Text>
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.isSelfStorage")}
                  description={t("businessUnits.isSelfStorageDescription")}
                  checked={form.getValues().isSelfStorage}
                  {...form.getInputProps("isSelfStorage")}
                  {...{ labelProps: { style: { flex: 1.9 } } }}
                />
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.hasRoboticStorage")}
                  description={t("businessUnits.hasRoboticStorageDescription")}
                  checked={form.getValues().hasRoboticStorage}
                  {...form.getInputProps("hasRoboticStorage")}
                  {...{ labelProps: { style: { flex: 1.9 } } }}
                />
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.hasEngineRoom")}
                  description={t("businessUnits.hasEngineRoomDescription")}
                  checked={form.getValues().hasEngineRoom}
                  {...form.getInputProps("hasEngineRoom")}
                  {...{ labelProps: { style: { flex: 1.9 } } }}
                />
                <Checkbox
                  classNames={{ icon: classes.icon }}
                  mt="sm"
                  label={t("businessUnits.hasVault")}
                  description={t("businessUnits.hasVaultDescription")}
                  checked={form.getValues().hasVault}
                  {...form.getInputProps("hasVault")}
                  {...{ labelProps: { style: { flex: 1.9 } } }}
                />
              </Paper>
            </Stack>
          </Grid.Col>

          {initialValues?.id ? (
            <>
              <Grid.Col span={{ base: 12, md: 12 }}>
                <UnitList
                  parentEntityId={initialValues?.id ?? ""}
                  parentEntityName="BusinessUnits"
                  parentEntityIdParam="businessUnitId"
                  visibleColumns={["unitType", "code"]}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 12 }}>
                <MailboxesList
                  parentEntityId={initialValues?.id ?? ""}
                  parentEntityName="BusinessUnits"
                  parentEntityIdParam="businessUnitId"
                />
              </Grid.Col>
            </>
          ) : null}
        </Grid>
      </EntityLayout>
    </form>
  );
}
