import { Button, Combobox, useCombobox } from "@mantine/core";
import { useTranslation } from "react-i18next";

interface WizardSelectProps {
  optionValues: string[];
  selectedItem: string | null;
  setSelectedItem: (value: string | null) => void;
  leftSectionIcon?: JSX.Element;
  buttonLabel: string;
}

export default function WizardSelect({
  optionValues,
  selectedItem,
  setSelectedItem,
  leftSectionIcon,
  buttonLabel,
}: WizardSelectProps) {
  const { t } = useTranslation("features");
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });
  const options = optionValues.map((item: string) => (
    <Combobox.Option value={item} key={item}>
      {t(`wizards.${item}`)}
    </Combobox.Option>
  ));
  return (
    <Combobox
      store={combobox}
      position="bottom-start"
      withArrow
      onOptionSubmit={(val, options) => {
        options.value;
        setSelectedItem(val);
        combobox.closeDropdown();
      }}
    >
      <Combobox.Target>
        <Button
          fullWidth
          variant="light"
          leftSection={leftSectionIcon}
          style={{
            fontSize: 12,
            fontWeight: 500,
            borderRadius: 32,
          }}
          onClick={() => combobox.toggleDropdown()}
        >
          {`${selectedItem ? t(`wizards.${selectedItem}`) : buttonLabel}`}
        </Button>
      </Combobox.Target>

      <Combobox.Dropdown w={"100%"}>
        <Combobox.Options>{options}</Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}
