import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { ContractForm } from "../components/ContractForm";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { notifications } from "@mantine/notifications";
import { Group } from "@mantine/core";
import { useState } from "react";

export function ContractCreate() {
  const { t } = useTranslation("features");
  const [close, setClose] = useState(false);
  const navigate = useNavigate();
  const { mutate } = useEntityCreateMutation<
    Schemas["Contract"],
    Schemas["ContractCreateDto"]
  >({ resourcePath: "/api/Contracts", queryKey: "contract" });

  return (
    <ContractForm
      isCreate={true}
      title={t("contracts.createTitle")}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/contracts");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/contracts/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
