import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { CaseForm } from "../components/CaseForm";
import { EntityLayout } from "@/features/entity";
import { Group } from "@mantine/core";
import { useState } from "react";
import { notifications } from "@mantine/notifications";
import UseUser from "@/hooks/useUser";

export function CaseCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { finalUser } = UseUser();
  const { mutate } = useEntityCreateMutation<
    Schemas["Case"],
    Schemas["CaseCreateDto"]
  >({ resourcePath: "/api/Cases", queryKey: "case" });
  const { t } = useTranslation("features");
  return (
    <CaseForm
      isCreate={true}
      title={t("cases.createTitle")}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/cases");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/cases");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/cases/" + data.data.id);
            }
          },
        });
      }}
      initialValues={{
        businessUnitId: finalUser?.businessUnitId ?? "",
        businessUnit: finalUser?.businessUnit ?? null,
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
