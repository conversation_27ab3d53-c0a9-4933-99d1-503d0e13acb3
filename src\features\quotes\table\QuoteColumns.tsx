import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Date<PERSON><PERSON>er,
} from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { lowerCaseNthLetter } from "@/utils/filters";
import { QuoteStatus } from "@/types/enums";
import { type ComboboxData } from "@mantine/core";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function QuotesColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "number",
      header: t("quotes.number"),
      filterVariant: "text",
    },
    {
      accessorKey: "totalOneTimeFee",
      header: t("quotes.totalOneTimeFee"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "totalMonthlyPrice",
      header: t("quotes.totalMonthlyPrice"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "expirationDate",
      header: t("quotes.expirationDate"),
      filterVariant: "range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "status",
      header: t("quotes.status"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("quotes." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: QuoteStatus as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "lead",
      header: t("quotes.lead"),
      ...TableRenderer(LeadLookup, "leads", ["fullName"]),
    },
    {
      accessorKey: "createdOn",
      header: t("quotes.createdOn"),
      filterVariant: "range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "owner",
      header: t("quotes.owner"),
      ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
    },
  ];
  return columns;
}
