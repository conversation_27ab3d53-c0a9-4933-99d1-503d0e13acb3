import { Route, Routes } from "react-router-dom";
import { PriceList } from "./PriceList";
import { PriceShow } from "./PriceShow";
import { PriceCreate } from "./PriceCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<PriceList />} />
      <Route path=":id" element={<PriceShow />} />
      <Route path="create" element={<PriceCreate />} />
    </Routes>
  );
}
