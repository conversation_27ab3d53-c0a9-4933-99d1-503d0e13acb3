import type React from "react";
import { Flex, Grid } from "@mantine/core";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import Card<PERSON>hart from "../common/CardChart";
import CustomBarChartVertical from "../common/CustomBarChartVertical";
import { formatCardValue } from "../../utils/logic";

const metrics: MetricProps[] = [
  { metric: "AllAppointments" },
  { metric: "PastAppointments" },
  { metric: "TotalSiteVisits" },
  { metric: "ConversionSiteVisits" },
  { metric: "AllNoShows" },
  { metric: "NoShowPercentage" },
  { metric: "OpenNoShows" },
  { metric: "NewAppointmentAfterNoShow" },
  { metric: "LostNoShow" },
  { metric: "LostWithoutSpaceTourNoShow" },
  { metric: "RealtimePercentage" },
];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid gutter={"xs"}>
    <Grid.Col span={12}>
      <Flex>
        <Grid flex={5.15}>
          <Grid.Col span={12}>
            <Grid mt={4} gutter={"xs"}>
              <Grid.Col span={2.4}>
                <CardChart
                  value={data[0]![0]?.total.toString() ?? "0"}
                  title={"All Appointments"}
                />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart
                  value={data[2]![0]?.total.toString() ?? "0"}
                  title={"Total Space Tour"}
                />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart
                  value={formatCardValue(data[2])}
                  title={"All No Shows"}
                />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart
                  value={data[6]![0]?.total.toString() ?? "0"}
                  title={"Open No Shows"}
                />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart
                  value={data[8]![0]?.total.toString() ?? "0"}
                  title={"Lost No Shows"}
                />
              </Grid.Col>
            </Grid>
          </Grid.Col>
          <Grid.Col span={12}>
            <Grid gutter={"xs"}>
              <Grid.Col span={2.4}>
                <CardChart value="0" title={"Not Yet Processed"} />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart value="0" title={"Conversion Appointments"} />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart value="0" title={"No Show Percentage"} />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart value="0" title={"New Appointments Made"} />
              </Grid.Col>
              <Grid.Col span={2.4}>
                <CardChart value="0" title={"Completed Without No Shows"} />
              </Grid.Col>
            </Grid>
          </Grid.Col>
        </Grid>
        <Grid flex={1} ml={4}>
          <Grid.Col span={12}>
            <CardChart
              height="255"
              marginTop={4}
              percentage
              value={data[10]![0]?.total.toString() ?? "0"}
              title={"Current No Show Percentage"}
            />
          </Grid.Col>
        </Grid>
      </Flex>
    </Grid.Col>
    <Grid.Col span={12}>
      <Grid gutter={"xs"}>
        <Grid.Col span={2}>
          <CustomBarChartVertical
            data={data[0]}
            marginTop={0}
            height={520}
            title="Appointments Created"
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <CustomBarChartVertical
            data={data[2]}
            marginTop={0}
            height={520}
            title="Space Tours"
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <CustomBarChartVertical
            data={data[0]}
            marginTop={0}
            height={520}
            title="No Show Before"
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <CustomBarChartVertical
            data={data[4]}
            marginTop={0}
            height={520}
            title="No Show"
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <CustomBarChartVertical
            data={data[7]}
            marginTop={0}
            height={520}
            title="New Appointment(No Show Before)"
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <CustomBarChartVertical
            data={data[5]}
            marginTop={0}
            height={520}
            title="Percentage No Show Per Location"
          />
        </Grid.Col>
      </Grid>
    </Grid.Col>
  </Grid>
);

const NoShow: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default NoShow;
