import { Route, Routes } from "react-router-dom";
import { CaseList } from "./CaseList";
import { CaseShow } from "./CaseShow";
import { CaseCreate } from "./CaseCreate";

export default function CustomersRoutes() {
  return (
    <Routes>
      <Route index element={<CaseList />} />
      <Route path=":id" element={<CaseShow />}>
        <Route path=":tabValue" element={<CaseShow />} />
      </Route>
      <Route path="create" element={<CaseCreate />} />
    </Routes>
  );
}
