import { Route, Routes } from "react-router-dom";
import { LeadList } from "./LeadList";
import { LeadShow } from "./LeadShow";
import { LeadCreate } from "./LeadCreate";

export default function ContractsRoutes() {
  return (
    <Routes>
      <Route index element={<LeadList />} />
      <Route path=":id" element={<LeadShow />}>
        <Route path=":tabValue" element={<LeadShow />} />
      </Route>
      <Route path="create" element={<LeadCreate />} />
    </Routes>
  );
}
