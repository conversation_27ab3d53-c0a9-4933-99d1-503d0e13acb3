import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { EntityLinkRenderer } from "@/components/Table/CellRenderers/EntityLinkRenderer";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { useEffect } from "react";

const PATH = "AppUsers";
export function AppUserListInner({
  resourcePath,
  toolbarExtension,
  toolbarOverride,
  setTableRef,
  queryKey = "appUser",
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  useEffect(() => {
    if (setTableRef) {
      setTableRef(tableRef);
    }
  }, [tableRef, setTableRef]);

  return (
    <>
      <EntityLayout stickyHeader={false}>
        <EntityLayout.TableMantine<
          Schemas["AppUserRetrieveDto"],
          Schemas["AppUserRetrieveDtoPagedList"]
        >
          tableRef={tableRef}
          resourcePath={resourcePath as PathKeys}
          queryKey={queryKey}
          entityPath="appUsers"
          toolbar={
            <>
              {toolbarExtension || null}
              {toolbarOverride || (
                <EntityLayout.AssignRolesButton table={tableRef} />
              )}
            </>
          }
          title={t("appUsers.title")}
          redirectTo={window.location.pathname}
          columns={[
            {
              accessorKey: "name",
              header: t("appUsers.name"),
              filterVariant: "text",
              Cell: (props) => EntityLinkRenderer(props, "appUsers"),
            },
            {
              accessorKey: "email",
              header: t("appUsers.email"),
              filterVariant: "text",
            },
          ]}
          initialSorting={[
            {
              id: "createdOn",
              desc: true,
            },
          ]}
        />
      </EntityLayout>
    </>
  );
}

export function AppUserList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
  toolbarExtension,
  toolbarOverride,
  queryKey,
  setTableRef,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <AppUserListInner
        toolbarExtension={toolbarExtension}
        toolbarOverride={toolbarOverride}
        resourcePath={resourcePath}
        createPath={createPath}
        queryKey={queryKey}
        setTableRef={setTableRef}
      />
    </ListCommandsProvider>
  );
}
