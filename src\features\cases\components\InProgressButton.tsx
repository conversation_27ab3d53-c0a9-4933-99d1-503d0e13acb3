import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { notifications } from "@mantine/notifications";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useQueryClient } from "react-query";
import { type Schemas } from "@/types";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { IconPlayerPlay } from "@tabler/icons-react";

export default function InProgressButton() {
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const queryCache = useQueryClient();

  const { mutate: updateCase } = useEntityUpdateMutation<
    Schemas["Case"],
    Schemas["CasePatchDto"]
  >({
    resourcePath: "/api/Cases/{id}",
    resourceId: id ?? "",
    queryKey: "case",
  });

  const handleEscalationReturn = () => {
    updateCase(
      {
        status: "InProgress",
      },
      {
        onSuccess: () => {
          notifications.show({
            color: "green",
            title: t("cases.caseStartProgressSuccessTitle"),
            message: t("cases.caseStartProgressSuccessMessage"),
          });
          void queryCache.invalidateQueries("case_" + id);
          close();
        },
        onError: () => {
          notifications.show({
            color: "red",
            title: t("cases.caseStartProgressErrorTitle"),
            message: t("cases.caseStartProgressErrorMessage"),
          });
        },
      },
    );
  };

  return (
    <ButtonMain
      label={t("cases.caseStartProgress")}
      icon={<IconPlayerPlay size={18} />}
      onClick={handleEscalationReturn}
    />
  );
}
