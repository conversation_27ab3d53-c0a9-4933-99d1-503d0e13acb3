import { Box, Text, Center, Grid, Loader, Stack } from "@mantine/core";
import { IconU<PERSON><PERSON><PERSON><PERSON>, IconUserCheck } from "@tabler/icons-react";

import WizardCard from "../Common/WizardCard";
import { type Schemas } from "@/types";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useState } from "react";

import classes from "../NoShow.module.css";
import { useTranslation } from "react-i18next";
import { type PageName } from "../Common/Common";
import { type PageProps } from "../../Common/Header/WizardHeader";

interface PageStartProps extends PageProps<PageName> {
  appointment?: Schemas["AppointmentRetrieveDto"];
}

export default function PageStart({
  appointment,
  setPages,
  pages,
  setTotalPages,
}: PageStartProps) {
  const { t } = useTranslation("features");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { mutate: updateAppointment } = useEntityUpdateMutation<
    Schemas["Appointment"],
    <PERSON><PERSON><PERSON>["AppointmentPatchDto"]
  >({
    resourcePath: "/api/Appointments/{id}",
    resourceId: appointment?.id ?? "",
    queryKey: "appointment",
  });

  function SpaceTourCard() {
    if (
      appointment?.appointmentType === "SpaceTour" ||
      appointment?.appointmentType === "BankSafe" ||
      appointment?.appointmentType === "EngineRoomTour" ||
      appointment?.appointmentType === "RoboticStorageTour"
    ) {
      return (
        <WizardCard
          pages={pages}
          setPages={setPages}
          setTotalPages={setTotalPages}
          pageName={"SPACETOUR"}
          headerText={t("wizards.UpdateAppointment.LeadShowTitle")}
          headerIcon={<IconUserCheck size={40} />}
          descriptionText={t("wizards.UpdateAppointment.LeadShowDescription")}
          footerText={t("wizards.UpdateAppointment.LeadShowFooter")}
        />
      );
    } else {
      return (
        <Box
          className={classes.wizardButton}
          onClick={() => {
            updateAppointment(
              {
                appointmentStatus: "Completed",
              },
              {
                onSuccess: () => {
                  setPages([...pages, "COMPLETED"]);
                  setIsLoading(false);
                },
                onError: (error) => {
                  setIsLoading(false);
                  console.error(error);
                },
              },
            );
          }}
        >
          {isLoading ? (
            <Stack align="stretch" justify="center" gap="md" h={"100%"}>
              <Center h={"100%"} w={"100%"}>
                <Loader />
              </Center>
            </Stack>
          ) : (
            <Stack align="stretch" justify="center" gap="md">
              <Center>
                <IconUserCheck size={40} />
              </Center>
              <Text fz={24} ta={"center"} fw={600} c={"#282828"}>
                {t("wizards.UpdateAppointment.LeadShowTitle")}
              </Text>
              <Text fz={12} fw={400} c={"#282828"}>
                {t("wizards.UpdateAppointment.LeadShowDescription")}
              </Text>
              <Text fz={10} fw={600} c={"#ADADAD"}>
                {t("wizards.UpdateAppointment.LeadShowFooter")}
              </Text>
            </Stack>
          )}
        </Box>
      );
    }
  }
  return (
    <Box>
      <Center>
        <Text fz={16} fw={600} c={"#282828"}>
          {t("wizards.UpdateAppointment.Title")}
        </Text>
      </Center>
      <Center>
        <Text fz={10} fw={300} c={"#ADADAD"}>
          {t("wizards.UpdateAppointment.Label")}
        </Text>
      </Center>
      <Center mt={40}>
        <Grid
          justify="center"
          grow
          w={{
            base: "100%",
            xs: "100%",
            sm: "100%",
            md: "70%",
            lg: "60%",
            xl: "50%",
          }}
          p={{ base: 0, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}
          ml={{ base: 0, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}
          mr={{ base: 0, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}
          gutter={24}
        >
          <Grid.Col span={{ base: 12, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}>
            <WizardCard
              isLoading={isLoading}
              pages={pages}
              setPages={setPages}
              pageName={"SHOW_UP"}
              headerText={t("wizards.UpdateAppointment.LeadNoShowTitle")}
              headerIcon={<IconUserCancel size={40} />}
              descriptionText={t(
                "wizards.UpdateAppointment.LeadNoShowDescription",
              )}
              footerText={t("wizards.UpdateAppointment.LeadNoShowFooter")}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, xs: 12, sm: 6, md: 6, lg: 3, xl: 3 }}>
            {SpaceTourCard()}
          </Grid.Col>
        </Grid>
      </Center>
    </Box>
  );
}
