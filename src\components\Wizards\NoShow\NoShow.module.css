.wizardButton {
  border-radius: 8px;
  padding: 32px;
  background-color: var(--mantine-color-primary-0);
  border-style: solid;
  border-width: 1px;
  border-color: var(--mantine-color-primary-1);
  height: 100%;
}

.wizardButtonConfirm {
  border-radius: 8px;
  padding: 32px;
  border-style: solid;
  border-width: 2px;
  border-color: var(--mantine-color-primary-1);
  height: 100%;
}

.wizardButton:hover {
  background-color: var(--mantine-color-primary-1);
  cursor: pointer;
  border-style: solid;
  border-width: 1px;
  border-color: var(--mantine-color-primary-6);
}

.textareaWizardInput {
  border-radius: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: var(--mantine-color-neutral-2);
}

.listItem {
  padding: 16px;
  margin: 8px;
  background-color: var(--mantine-color-neutral-0);
  border-radius: 8px;
}

.listItem:hover {
  cursor: pointer;
  background-color: var(--mantine-color-primary-1);
}

.listItemTitle {
  color: var(--mantine-color-neutral-9);
  font-size: 1rem;
  font-weight: 700;
}

.listItemLabel {
  color: var(--mantine-color-neutral-6);
  font-size: 0.75rem;
  font-weight: 400;
}

.listItemText {
  color: var(--mantine-color-neutral-9);
  font-size: 0.875rem;
  font-weight: 400;
}
.listNoItemsText {
  color: var(--mantine-color-neutral-6);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  font-style: italic;
}
