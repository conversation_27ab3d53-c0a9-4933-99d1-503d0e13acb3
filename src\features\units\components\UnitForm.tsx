import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  Grid,
  NumberInput,
  Paper,
  Select,
  Switch,
  TextInput,
} from "@mantine/core";
import { type ReactNode } from "react";
import { config } from "@/config";
import { UnitStatus, Floor } from "@/types/enums";
import "@mantine/tiptap/styles.css";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { getEnumTransKey } from "@/utils/trans";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { UnitTags } from "./UnitTags";
import { PriceList } from "@/features/prices/routes/PriceList";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { UnitTypeLookup } from "@/components/Lookup/Features/UnitTypes/UnitTypeLookup";

const formSchema = z.object({
  id: z.string(),
  unitId: z.string().nullable(),
  unitCode: z.string().nullable(),
  unitTypeId: z.string().nullable(),
  unitType: z.object({}).nullable(),
  distanceToElevator: z.coerce.number().nullable(),
  length: z.coerce.number().nullable(),
  width: z.coerce.number().nullable(),
  height: z.coerce.number().nullable(),
  volume: z.coerce.number().nullable(),
  area: z.coerce.number().nullable(),
  pricePerMonth: z.coerce.number().nullable(),
  minPrice: z.coerce.number().nullable(),
  maxPrice: z.coerce.number().nullable(),
  available24Hours: z.boolean().default(false),
  businessUnitId: z.string().nullable(),
  businessUnit: z.object({}).nullable(),
  status: z.enum(UnitStatus as [string]).nullable(),
  floor: z.enum(Floor as [string]).nullable(),
});

type FormSchema = z.infer<typeof formSchema>;

interface UnitFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  contextRecordId?: string;
  isCreate: boolean;
}

export function UnitForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: UnitFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      unitCode: initialValues?.unitCode ?? "",
      unitId: initialValues?.unitId ?? "",
      unitTypeId: initialValues?.unitTypeId ?? "",
      unitType: initialValues?.unitType ?? null,
      distanceToElevator: initialValues?.distanceToElevator ?? null,
      length: initialValues?.length ?? null,
      width: initialValues?.width ?? null,
      height: initialValues?.height ?? null,
      volume: initialValues?.volume ?? null,
      area: initialValues?.area ?? null,
      pricePerMonth: initialValues?.pricePerMonth ?? null,
      minPrice: initialValues?.minPrice ?? null,
      maxPrice: initialValues?.maxPrice ?? null,
      available24Hours: initialValues?.available24Hours ?? false,
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      status: initialValues?.status ?? null,
      floor: initialValues?.floor ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="xs" pt="">
              <TextInput
                disabled
                label={t("units.unitId")}
                {...form.getInputProps("unitId")}
              />
              <FieldValidation isDirty={form.isDirty("unitCode")}>
                <TextInput
                  disabled
                  label={t("units.unitCode")}
                  {...form.getInputProps("unitCode")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("unitTypeId")}>
                <UnitTypeLookup
                  required
                  disabled
                  label={t("units.unitType")}
                  initial={form.getValues().unitType}
                  initialId={form.getValues().unitTypeId}
                  identifier="unitTypeIdUnit"
                  {...form.getInputProps("unitTypeId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("distanceToElevator")}>
                <NumberInput
                  disabled
                  label={t("units.distanceToElevator")}
                  leftSection={config.METERS.symbol}
                  {...form.getInputProps("distanceToElevator")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("length")}>
                <NumberInput
                  disabled
                  label={t("units.length")}
                  leftSection={config.METERS.symbol}
                  {...form.getInputProps("length")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("width")}>
                <NumberInput
                  disabled
                  label={t("units.width")}
                  leftSection={config.METERS.symbol}
                  {...form.getInputProps("width")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("height")}>
                <NumberInput
                  disabled
                  label={t("units.height")}
                  leftSection={config.METERS.symbol}
                  {...form.getInputProps("height")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="xs" pt="">
              <NumberInput
                label={t("units.pricePerMonth")}
                disabled
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("pricePerMonth")}
              />
              <NumberInput
                label={t("units.minPrice")}
                disabled
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("minPrice")}
              />
              <NumberInput
                label={t("units.maxPrice")}
                disabled
                leftSection={config.CURRENCY.symbol}
                {...form.getInputProps("maxPrice")}
              />
              <FieldValidation isDirty={form.isDirty("area")}>
                <NumberInput
                  disabled
                  label={t("units.area")}
                  leftSection={config.SQUAREMETERS.symbol}
                  {...form.getInputProps("area")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("volume")}>
                <NumberInput
                  disabled
                  label={t("units.volume")}
                  leftSection={config.CUBICMETERS.symbol}
                  {...form.getInputProps("volume")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("floor")}>
                <Select
                  searchable
                  disabled
                  label={t("units.floor")}
                  data={Floor.map((value) => ({
                    value,
                    label: t(getEnumTransKey("units", value)),
                  }))}
                  clearable
                  {...form.getInputProps("floor")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("businessUnitId")}>
                <BusinessUnitLookup
                  required
                  disabled
                  label={t("units.businessUnit")}
                  initial={form.getValues().businessUnit}
                  initialId={form.getValues().businessUnitId}
                  identifier="businessUnitIdUnit"
                  {...form.getInputProps("businessUnitId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("status")}>
                <Select
                  searchable
                  disabled
                  label={t("units.status")}
                  data={UnitStatus.map((value) => ({
                    value,
                    label: t(getEnumTransKey("units", value)),
                  }))}
                  clearable
                  {...form.getInputProps("status")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("available24Hours")}>
                <Switch
                  mt="sm"
                  disabled
                  label={t("units.available24Hours")}
                  checked={form.getValues().available24Hours}
                  {...form.getInputProps("available24Hours")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          {initialValues?.id ? (
            <Grid.Col span={{ base: 12, md: 6 }}>
              <PriceList
                parentEntityId={initialValues?.id ?? ""}
                parentEntityName="Units"
                parentEntityIdParam="unitId"
              />
            </Grid.Col>
          ) : null}
          {initialValues?.id ? (
            <Grid.Col span={{ base: 12, md: 6 }}>
              <UnitTags unitId={initialValues?.id ?? ""} />
            </Grid.Col>
          ) : null}
        </Grid>
      </EntityLayout>
    </form>
  );
}
