import { EntityLayout } from "@/features/entity";
import { type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
export function SettingList() {
  const { t } = useTranslation("features");

  return (
    <EntityLayout title={t("settings.title")} showBackButton={false}>
      <EntityLayout.TableMantine<
        Schemas["SettingRetrieveDto"],
        Schemas["SettingRetrieveDtoPagedList"]
      >
        resourcePath="/api/Settings"
        queryKey="setting"
        entityPath="settings"
        columns={[
          {
            accessorKey: "name",
            header: t("settings.name"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "settings"),
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: Date<PERSON><PERSON><PERSON>,
          },
        ]}
        initialSorting={[
          {
            id: "name",
            desc: false,
          },
        ]}
      />
    </EntityLayout>
  );
}
