import { Route, Routes } from "react-router-dom";
import { CaseCommentsList } from "./CaseCommentsList";
import { CaseCommentsShow } from "./CaseCommentsShow";
import { CaseCommentsCreate } from "./CaseCommentsCreate";

export default function CaseCommentsRoutes() {
  return (
    <Routes>
      <Route index element={<CaseCommentsList />} />
      <Route path=":id" element={<CaseCommentsShow />} />
      <Route path="create" element={<CaseCommentsCreate />} />
    </Routes>
  );
}
