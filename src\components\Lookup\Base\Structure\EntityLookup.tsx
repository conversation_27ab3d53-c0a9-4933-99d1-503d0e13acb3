import { useLookup } from "@/components/Lookup/Context/useLookup";
import { type QueryParams } from "@/features/entity/api";
import { useInfiniteEntityListQuery } from "@/features/entity/queries";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";
import { type Paths } from "@/types";
import { useCombobox } from "@mantine/core";
import { useDebouncedValue } from "@mantine/hooks";
import { useCallback, useState } from "react";

interface EntityProps {
  queryParams: QueryParams;
  resourcePath: keyof Paths;
  queryKey: string;
  entity: string;
}

interface BasicResponse<T> {
  hasNext?: boolean;
  currentPage?: number;
  data?: T[] | null | undefined;
  pageSize?: number;
}

export function EntityLookup<P, T extends BasicResponse<P>>({
  queryParams,
  resourcePath,
  queryKey,
  entity,
}: EntityProps) {
  const { searchTerm, setSearchTerm } = useLookup(entity);
  const [debouncedSearchTerm] = useDebouncedValue(searchTerm, 300);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  if (debouncedSearchTerm) {
    queryParams.searchBy = debouncedSearchTerm;
  }

  const { data, isFetching, fetchNextPage } = useInfiniteEntityListQuery<T>({
    resourcePath: resourcePath,
    queryKey: queryKey,
    params: queryParams,
    enabled: isDropdownOpen,
  });
  const loadMore = useCallback(() => {
    void fetchNextPage();
  }, [fetchNextPage]);

  const infiniteScrollRef = useInfiniteScroll(
    loadMore,
    !isFetching,
    queryParams.pageSize,
  );
  const combobox = useCombobox({
    onDropdownClose: () => {
      combobox.focusTarget();
      setSearchTerm("");
      setIsDropdownOpen(false);
    },
    onDropdownOpen: () => {
      combobox.focusTarget();
      setIsDropdownOpen(true);
    },
  });
  const entityListRaw = data?.pages.map((page) => page?.data).flat() ?? [];
  const entityList = entityListRaw.filter(
    (entity): entity is P => entity != null,
  );

  const pageSize = data?.pages[0]?.pageSize ?? 0;
  const currentPage = data?.pages.length ?? 0;
  const currentFocus = currentPage === 1 ? 0 : pageSize * (currentPage - 1);
  return {
    infiniteScrollRef,
    entityList,
    currentFocus,
    combobox,
    isFetching,
    queryParams,
  };
}
