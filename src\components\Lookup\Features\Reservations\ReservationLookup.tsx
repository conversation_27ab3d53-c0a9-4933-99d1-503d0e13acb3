import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { useTranslation } from "react-i18next";

const ENTITY = "reservations";
type ENTITY_TYPE = Schemas["ReservationRetrieveDto"];

type ReservationLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function ReservationLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  ...props
}: ReservationLookupProps) {
  const { t } = useTranslation("features");
  const { searchTerm } = useLookup(
    identifier,
    initial?.start,
    initialId,
    initial,
    true,
  );

  const queryParams: QueryParams = {
    pageSize: 50,
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["ReservationRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/Reservations",
      queryKey: "reservation",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("reservations.start")}</Box>
      <Box>{t("reservations.reservedUntil")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.start, searchTerm, `start_${entity.id}`),
        OptionRender(
          entity.reservedUntil,
          searchTerm,
          `reservedUntil_${entity.id}`,
        ),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.start!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
