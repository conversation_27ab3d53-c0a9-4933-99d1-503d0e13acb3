import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { lowerCaseNthLetter } from "@/utils/filters";
import { DateRenderer } from "@/components/Table/CellRenderers";
import { ReservationStatus } from "@/types/enums";
import { Group, type ComboboxData } from "@mantine/core";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

const PATH = "Reservations";

export function ReservationListInner({ resourcePath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["ReservationRetrieveDto"],
        Schemas["ReservationRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="reservation"
        entityPath="reservations"
        title={t("reservations.title")}
        toolbar={<Group></Group>}
        pageSize={10}
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "start",
            header: t("reservations.start"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
          {
            accessorKey: "reservedUntil",
            header: t("reservations.until"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
          {
            accessorKey: "reservationStatus",
            header: t("reservations.status"),
            filterVariant: "multi-select",
            Cell: ({ cell }) =>
              t("leads." + lowerCaseNthLetter(cell.getValue<string>())),
            mantineFilterSelectProps: {
              data: ReservationStatus as ComboboxData | undefined,
            },
          },
          {
            accessorKey: "lead",
            header: t("reservations.lead"),
            ...TableRenderer(LeadLookup, "leads", ["lastName", "firstName"]),
          },
          {
            accessorKey: "lead.businessUnit",
            header: t("leads.businessUnit"),
            ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
          },
          {
            accessorKey: "createdByUser",
            header: t("common.createdBy"),
            ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
          },
          {
            accessorKey: "modifiedByUser",
            header: t("common.modifiedBy"),
            ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function ReservationList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <ReservationListInner
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
