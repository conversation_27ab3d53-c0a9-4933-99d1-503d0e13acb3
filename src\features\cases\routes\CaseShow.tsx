import { <PERSON>Loader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { CaseForm } from "../components/CaseForm";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";
import CaseCloseButton from "../components/CaseCloseButton";
import EscalationButton from "../components/EscalationButton";
import { type CaseFormSchema } from "../providers/form";
import InProgressButton from "../components/InProgressButton";

export function CaseShow() {
  const queryCache = useQueryClient();
  const navigate = useNavigate();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Case"],
    Schemas["CaseCreateDto"]
  >({
    resourcePath: "/api/Cases/{id}",
    resourceId: id!,
    queryKey: "case",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Case"]>({
    resourcePath: "/api/Cases/{id}",
    resourceId: id!,
    queryKey: "case",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("case_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <CaseForm
      isCreate={false}
      disabled={data.recordState === "Inactive"}
      title={`${t("cases.showTitle", { id })} - ${data.subject}`}
      recordState={data.status}
      caseId={id}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/cases");
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/cases");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      initialValues={filterFalsyValues(data) as CaseFormSchema}
      disabledActionSection={
        <Group>
          <CaseCloseButton
            caseId={id!}
            close={data.recordState !== "Inactive"}
          />
        </Group>
      }
      actionSection={
        <Group>
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          {data.status !== "Escalated" && (
            <CaseCloseButton
              caseId={id!}
              close={data.recordState !== "Inactive"}
            />
          )}
          <EscalationButton />
          {(data.status == "New" || data.status == "Reopened") && (
            <InProgressButton />
          )}
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
      headerSection={
        <Group>
          <EntityLayout.OwnerHeader
            setOwner={(userId: string) => {
              update({ ownerId: userId });
            }}
            ownerId={data.ownerId}
          />
        </Group>
      }
    />
  );
}
