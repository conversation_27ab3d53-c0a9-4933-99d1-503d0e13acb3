import { useDisclosure } from "@mantine/hooks";
import { useState } from "react";
import { Modal, Button, Select } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { IconLockOpenOff } from "@tabler/icons-react";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { useLayoutVisibility } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import { LossReasonLookup } from "@/components/Lookup/Features/LossReasons/LossReasonLookup";
import { useEntityPostMutation } from "@/features/entity/mutations";
import { type Schemas } from "@/types";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";
import LeadListeners from "./LeadListeners";

interface LeadLostButtonProps {
  leadId?: string;
}

export function LeadLostButton({ leadId }: LeadLostButtonProps) {
  const { mutate: loseLead } = useEntityPostMutation<
    Schemas["Lead"],
    Schemas["LeadLoseDto"]
  >({
    resourcePath: "/api/Leads/loseLead",
    queryKey: "lead_" + leadId!,
  });
  const [opened, { open, close }] = useDisclosure(false);
  const queryCache = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [lossReason, setLossReason] = useState<string | null>(null);
  const { activeNavbar } = useLayoutVisibility();
  const isSalesAndServiceApp = activeNavbar === "Sales & Service";
  const [phoneCallStatus, setPhoneCallStatus] = useState<
    "Reached" | "NotReached" | undefined
  >(undefined);
  const { t } = useTranslation("features");

  const onConfirm = () => {
    if (isSalesAndServiceApp) {
      if (!phoneCallStatus) {
        notifications.show({
          color: "red",
          title: t("leads.phoneCallStatusRequiredTitle"),
          message: t("leads.phoneCallStatusRequiredMessage"),
        });
        setIsLoading(false);
        return;
      }
    }
    loseLead(
      {
        leadId: leadId!,
        lossReasonId: lossReason!,
        phoneCallStatus: phoneCallStatus,
      },
      {
        onSuccess: async () => {
          notifications.show({
            color: "green",
            title: t("leads.leadLossSuccessTitle"),
            message: t("leads.leadLossSuccessMessage"),
          });
          setIsLoading(false);
          await queryCache.invalidateQueries("lead_" + leadId);
          close();
        },
        onError: () => {
          notifications.show({
            color: "green",
            title: t("leads.leadLossErrorTitle"),
            message: t("leads.leadLossErrorMessage"),
          });
          setIsLoading(false);
          close();
        },
      },
    );
  };

  return (
    <>
      <LeadListeners
        leadId={leadId}
        lossReason={lossReason}
        phoneCallStatus={phoneCallStatus}
      />
      <ButtonMain
        label={t("leads.lost")}
        icon={<IconLockOpenOff size={18} />}
        onClick={open}
      />
      <Modal
        opened={opened}
        onClose={close}
        centered
        title={t("leads.leadLostTitle")}
      >
        <LossReasonLookup
          identifier="lossReasonsLeadLost"
          label={t("leads.lossReason")}
          groupingEnabled
          onChange={(value: string | null) => {
            if (value) {
              setLossReason(value);
            }
          }}
        />
        {isSalesAndServiceApp && (
          <Select
            required
            label={t("leads.phoneCallStatus")}
            data={[
              { value: "Reached", label: t("leads.reached") },
              { value: "NotReached", label: t("leads.notReached") },
            ]}
            clearable
            value={phoneCallStatus}
            onChange={(value) =>
              setPhoneCallStatus(value as "Reached" | "NotReached" | undefined)
            }
            mt="md"
          />
        )}
        <Button
          loading={isLoading}
          onClick={() => {
            if (lossReason) {
              setIsLoading(true);
              onConfirm();
            }
          }}
          w={"100%"}
          mt={"xs"}
        >
          {t("leads.leadLostConfirm")}
        </Button>
      </Modal>
    </>
  );
}
