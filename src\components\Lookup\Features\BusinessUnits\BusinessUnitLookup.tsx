import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { GetBusinessUnitTypeFilter } from "./utils";
import { t } from "i18next";

const ENTITY = "businessUnits";
type ENTITY_TYPE = Schemas["BusinessUnitRetrieveDto"];

type BusinessUnitLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
    filterBUbyAppointmentType?: string | null;
  };

export function BusinessUnitLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  filterBUbyAppointmentType,
  ...props
}: BusinessUnitLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.code,
    initialId,
    initial,
    true,
  );
  const queryParams: QueryParams = {
    pageSize: 50,
    filter: GetBusinessUnitTypeFilter(filterBUbyAppointmentType),
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["BusinessUnitRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/BusinessUnits",
      queryKey: "BusinessUnit",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.code")}</Box>
      <Box>{t("common:lookup.name")}</Box>
    </React.Fragment>
  );

  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.code, searchTerm, `code_${entity.id}`, 1),
        OptionRender(entity.name, searchTerm, `name_${entity.id}`, 3),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.code!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
