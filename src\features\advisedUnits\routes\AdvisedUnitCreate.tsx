import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { AdvisedUnitForm } from "../components/AdvisedUnitForm";
import { EntityLayout } from "@/features/entity";
import { Group, Loader } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";
import { useQueryClient } from "react-query";

interface AdvisedUnitCreateProps {
  parentEntityId?: string;
  redirectTo?: string;
  usingModal?: boolean;
  closeModal?: () => void;
}

export function AdvisedUnitCreate({
  parentEntityId: propParentEntityId,
  redirectTo: propRedirectTo,
  usingModal: propUsingModal,
  closeModal, // Add closeModal to props
}: AdvisedUnitCreateProps) {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["AdvisedUnit"],
    Schemas["AdvisedUnitCreateDto"]
  >({ resourcePath: "/api/AdvisedUnits", queryKey: "advisedUnit" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const leadId = propParentEntityId ?? searchParams.get("leadId");
  const queryCache = useQueryClient();
  const redirectTo =
    propRedirectTo ?? searchParams.get("redirectTo") ?? "/app/advisedUnits";
  const usingModal = propUsingModal ?? false;
  const {
    data: lead = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/Leads/{id}",
    resourceId: leadId!,
    queryKey: ["lead", leadId],
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <AdvisedUnitForm
      initialValues={{
        leadId: leadId ?? "",
        lead: lead,
        businessUnitId: lead?.businessUnit?.id ?? "",
        businessUnit: lead?.businessUnit ?? null,
      }}
      isCreate={true}
      title={t("advisedUnits.createTitle")}
      actionSection={
        <Group>
          {!usingModal && <EntityLayout.SaveButton setClose={setClose} />}
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);
        mutate(filteredValues, {
          onSuccess: (data) => {
            void queryCache.invalidateQueries("advisedUnits_list");
            if (usingModal) {
              if (close) {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("advisedUnits.createSuccessMessage"),
                });
                closeModal?.();
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("advisedUnits.createSuccessMessage"),
                });
              }
            } else {
              if (close) {
                navigate(redirectTo);
              } else {
                let navigateTo = `/app/advisedUnits/${data.data.id}`;
                navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
                navigate(navigateTo);
              }
            }
          },
        });
      }}
    />
  );
}
