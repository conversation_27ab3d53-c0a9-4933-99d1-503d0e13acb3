import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { type FormSchema, RoleForm } from "../components/RoleForm";
import { useEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { PageLoader } from "@/components/PageLoader";

export function RoleShow() {
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityListQuery<Schemas["RoleDtoPagedList"]>({
    resourcePath: "/auth/roles",
    queryKey: "rolesFormRoles",
  });
  if (isLoading || isFetching) {
    return <PageLoader />;
  }
  const filteredData = data?.data
    ? data.data.find((item) => item.id === id)
    : null;
  return (
    <RoleForm
      roleId={id}
      initialValues={
        filterFalsyValues({
          ...filteredData,
        }) as unknown as FormSchema
      }
      title={t("roles.showTitle")}
      headerSection={<Group></Group>}
    />
  );
}
