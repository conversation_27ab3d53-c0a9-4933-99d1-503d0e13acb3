import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  CaseCommentsForm,
  type FormSchema,
} from "../components/CaseCommentsForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function CaseCommentsShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["CaseComment"],
    Schemas["CaseCommentCreateDto"]
  >({
    resourcePath: "/api/CaseComments/{id}",
    resourceId: id!,
    queryKey: "caseComment",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/CaseComments";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("caseComment_" + id);
  };

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["CaseComment"]>({
    resourcePath: "/api/CaseComments/{id}",
    resourceId: id!,
    queryKey: "caseComment",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <CaseCommentsForm
      isCreate={false}
      title={t("caseComments.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["CaseCommentCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
