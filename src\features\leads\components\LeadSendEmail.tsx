import { useRef, useState } from "react";
import {
  Button,
  Group,
  Grid,
  Loader,
  TextInput,
  ActionIcon,
  Menu,
  Box,
  Paper,
} from "@mantine/core";
import { IconPaperclip, IconPlus, IconTrash } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import {
  EmailSend,
  type CustomErrorData,
} from "@/components/EmailSending/ProcessEmail";
import HtmlEditor, {
  type HtmlEditorRef,
} from "@/components/HtmlEditor/HtmlEditor";
import { useEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { notifications } from "@mantine/notifications";
import { type QueryClient, useQueryClient } from "react-query";
import { type ApiError } from "@/lib/api";
import EmailTemplates from "@/components/EmailSending/EmailTemplates";
import ReplyMailboxes from "@/components/EmailSending/ReplyMailboxes";

interface LeadSendEmailProps {
  leadId: string;
  businessUnitId: string;
  preffiledTo?: string | null;
  prefilledSubject?: string | null;
  leadStatus?: string | null;
}

export default function LeadSendEmail({
  leadId,
  businessUnitId,
  preffiledTo,
  prefilledSubject,
}: LeadSendEmailProps) {
  const { t } = useTranslation("features");
  const [emailBody, setEmailBody] = useState("");
  const queryCache = useQueryClient();
  const [attachment, setAttachment] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      setAttachment((prevFiles) => [...prevFiles, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setAttachment((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const removeAllFiles = () => {
    setAttachment([]);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };
  const htmlEditorRef = useRef<HtmlEditorRef>(null);
  const [to, setTo] = useState(preffiledTo ?? "");
  const [subject, setSubject] = useState(prefilledSubject ?? "");
  const [selectedMailbox, setSelectedMailbox] = useState<string | null>("");

  const { data: mailboxes, isLoading: isLoadingMailboxes } = useEntityListQuery<
    Schemas["MailboxRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Mailboxes",
    queryKey: `mailboxes`,
    params: {
      filter: `BusinessUnit.Id == ${businessUnitId}`,
    },
    enabled: !!businessUnitId,
  });

  const prelimanaryMailbox =
    mailboxes?.totalCount === 1 ? mailboxes?.data?.at(0)?.id : null;

  const validateReply = () => {
    if (!to) {
      notifications.show({
        title: "Error",
        message: t("features:emails.toRequired"),
        color: "red",
      });
      return;
    }

    if (!subject) {
      notifications.show({
        title: "Error",
        message: t("features:emails.subjectRequired"),
        color: "red",
      });
      return;
    }

    if (!prelimanaryMailbox && !selectedMailbox) {
      notifications.show({
        title: "Error",
        message: t("features:emails.pleaseSelectMailbox"),
        color: "red",
      });
      return;
    }
  };

  const handleSendReply = async () => {
    validateReply();
    if (htmlEditorRef.current) {
      const fullHtml = htmlEditorRef.current.getFullHtml();
      await EmailSend(
        leadId,
        "Leads",
        fullHtml,
        attachment,
        to,
        "",
        "",
        subject,
        prelimanaryMailbox ?? selectedMailbox,
      )
        .then(handleReplyResponse(to, queryCache))
        .catch(handleReplyError);
    }
  };

  const handleReplyResponse = (to: string, queryCache: QueryClient) => () => {
    notifications.show({
      title: "Success",
      message: `Reply was sent to ${to}`,
      color: "green",
    });
    void queryCache.invalidateQueries("leadActivity_list");
  };
  const handleReplyError = (error: ApiError<CustomErrorData>) => {
    console.error("Error sending reply:", error);
    notifications.show({
      title: "Error",
      message: error.response?.data.fallbackMessage ?? "",
      color: "red",
    });
  };

  if (isLoadingMailboxes) {
    return <Loader />;
  }

  const formContent = (
    <>
      <Paper shadow="xs" p="xs" mt="md" pt="">
        <Grid>
          <Group w="100%" mt="md" ml="md">
            <Menu trigger="click-hover">
              <Menu.Target>
                <ActionIcon
                  variant="outline"
                  size="sm"
                  color="gray"
                  w={30}
                  h={30}
                >
                  <IconPaperclip
                    style={{ width: "70%", height: "70%" }}
                    stroke={1.5}
                  />
                </ActionIcon>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  rightSection={<IconPlus size={14} />}
                  onClick={triggerFileInput}
                >
                  {t("features:downloads.addFiles")}
                </Menu.Item>
                {attachment.length > 0 &&
                  attachment.map((file, index) => (
                    <Menu.Item
                      key={index}
                      rightSection={<IconTrash size={14} />}
                      onClick={() => removeFile(index)}
                    >
                      {file.name}
                    </Menu.Item>
                  ))}
                <Menu.Item
                  rightSection={<IconTrash size={14} />}
                  onClick={removeAllFiles}
                  disabled={attachment.length === 0}
                >
                  {t("features:downloads.removeAllFiles")}
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
            <Button size="xs" onClick={handleSendReply}>
              {t("leads.sendEmail")}
            </Button>
          </Group>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <ReplyMailboxes
              businessUnitId={businessUnitId}
              prelimanaryMailbox={prelimanaryMailbox}
              onChange={setSelectedMailbox}
              value={selectedMailbox}
            />
            <TextInput
              flex={1}
              label={t("features:downloads.to")}
              placeholder="To"
              value={to}
              onChange={(event) => setTo(event.currentTarget.value)}
            />

            <TextInput
              flex={6}
              label={t("features:downloads.subject")}
              placeholder="Subject"
              value={subject}
              onChange={(event) => setSubject(event.currentTarget.value)}
            />
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              style={{ display: "none" }}
              multiple
              accept="all"
            />
            <EmailTemplates
              setEmailBody={setEmailBody}
              setSubject={setSubject}
              subject={prefilledSubject}
              templateType="Leads"
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 12 }}>
            {emailBody && <HtmlEditor html={emailBody} ref={htmlEditorRef} />}
          </Grid.Col>
        </Grid>
      </Paper>
    </>
  );

  return <Box>{formContent}</Box>;
}
