import { type Schemas } from "@/types";
import { Box, Flex, Text, SimpleGrid, Stack } from "@mantine/core";
import { IconDots } from "@tabler/icons-react";
import classes from "../..//Home.module.css";
import { useTranslation } from "react-i18next";

export default function OmnichannelItem({
  user,
}: {
  user: Schemas["AppUserRetrieveDto"];
}) {
  const { t } = useTranslation("features");

  return (
    <Box className={classes.listItem}>
      <Flex justify="space-between" align="center" direction="row">
        <Flex justify="flex-start" align="center" direction="row" mb={8}>
          <Text className={classes.listItemTitle}>{user.name}</Text>
        </Flex>
        <IconDots width={20} height={20} style={{ display: "none" }} />
      </Flex>
      <SimpleGrid cols={{ xs: 4, sm: 4, md: 6, lg: 6, xl: 10 }} spacing={0}>
        <Stack gap={0}>
          <Text className={classes.listItemLabel}>{t(`leads.email`)}</Text>
          <Text className={classes.listItemText}>{user?.email ?? ""}</Text>
        </Stack>
      </SimpleGrid>
    </Box>
  );
}
