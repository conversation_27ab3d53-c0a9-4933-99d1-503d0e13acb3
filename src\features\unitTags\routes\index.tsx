import { Route, Routes } from "react-router-dom";
import { UnitTagList } from "./UnitTagList";
import { UnitTagShow } from "./UnitTagShow";
import { UnitTagCreate } from "./UnitTagCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<UnitTagList />} />
      <Route path=":id" element={<UnitTagShow />} />
      <Route path="create" element={<UnitTagCreate />} />
    </Routes>
  );
}
