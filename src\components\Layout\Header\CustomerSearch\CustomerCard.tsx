import { Box, Group, Badge, Text, Anchor } from "@mantine/core";
import classes from "./CustomerSearch.module.css";
import { type Schemas } from "@/types";
import { useNavigate } from "react-router-dom";

interface CustomerCardProps {
  customer: Schemas["CustomerSearchResultDto"];
}

export default function CustomerCard({ customer }: CustomerCardProps) {
  const navigate = useNavigate();
  return (
    <Box className={classes.customerCard} p={8}>
      <Anchor
        href={`/app/${customer.entityName + "s"}/${customer.id}`}
        target="_self"
        underline="never"
        c={"#282828"}
        onClick={(e) => {
          navigate(`/app/${customer.entityName + "s"}/${customer.id}`);
          e.preventDefault();
        }}
      >
        <Group gap="sm">
          <Text fw={600} truncate="end">
            {customer.name!.length > 20
              ? customer.name!.substring(0, 43) + "..."
              : customer.name!}
          </Text>
          <BadgeController entity={customer.entityName!} />
          {customer.hasRelatedCases && (
            <Badge color={"violet"} size="xs" radius="md" variant="light">
              {`Contains Cases`}
            </Badge>
          )}
        </Group>
        <Group gap="sm">
          {customer.mobile && (
            <Text size="sm" fw={400}>
              {customer.mobile}
            </Text>
          )}
          {customer.email && (
            <Text size="sm" fw={400}>
              {customer.email}
            </Text>
          )}
          <Group gap={4}>
            <Text size="md" fw={600}>
              {customer.hasRelatedCases!}
            </Text>
          </Group>
        </Group>
      </Anchor>
    </Box>
  );
}

function BadgeController({ entity }: { entity: string }) {
  let color = "primary";
  switch (entity) {
    case "Lead":
      color = "primary";
      break;
    case "Contact":
      color = "orange";
      break;
    case "Customer":
      color = "green";
      break;
    case "Case":
      color = "violet";
      break;
  }
  return (
    <Badge color={color} size="xs" radius="md" variant="light">
      {entity}
    </Badge>
  );
}
