import { Route, Routes } from "react-router-dom";
import { AppointmentList } from "./AppointmentList";
import { AppointmentShow } from "./AppointmentShow";
import { AppointmentCreate } from "./AppointmentCreate";

export default function AppointmentsRoutes() {
  return (
    <Routes>
      <Route index element={<AppointmentList />} />
      <Route path=":id" element={<AppointmentShow />} />
      <Route path="create" element={<AppointmentCreate />} />
    </Routes>
  );
}
