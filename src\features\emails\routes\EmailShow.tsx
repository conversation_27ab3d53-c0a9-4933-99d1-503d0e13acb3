import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { type FormSchema, EmailForm } from "../components/EmailForm";
import { PageLoader } from "@/components/PageLoader";
interface EmailShowProps {
  emailId?: string | null;
}
export function EmailShow({ emailId }: EmailShowProps) {
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Email"]>({
    resourcePath: "/api/Emails/{id}",
    resourceId: emailId ?? id!,
    queryKey: "email",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <EmailForm
      isCreate={false}
      title={t("emails.showTitle") + " " + (data.subject ?? "")}
      emailId={emailId ?? data.id!}
      onSubmit={() => {
        console.log("Submit not implemented: ");
      }}
      initialValues={
        filterFalsyValues({
          ...data,
        }) as FormSchema
      }
    />
  );
}
