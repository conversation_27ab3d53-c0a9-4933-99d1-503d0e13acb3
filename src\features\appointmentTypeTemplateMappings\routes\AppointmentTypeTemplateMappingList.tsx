import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { AppointmentTypeTemplateMappingColumns } from "../table/AppointmentTypeTemplateMappingColumns";

const PATH = "AppointmentTypeTemplateMappings";

export function AppointmentTypeTemplateMappingListInner({
  resourcePath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["AppointmentTypeTemplateMappingRetrieveDto"],
        Schemas["AppointmentTypeTemplateMappingRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="appointmentTypeTemplateMapping"
        entityPath="appointmentTypeTemplateMappings"
        title={t("appointmentTypeTemplateMappings.title")}
        redirectTo={window.location.pathname}
        visibleColumns={["appointmentType", "htmlTemplate"]}
        columns={AppointmentTypeTemplateMappingColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function AppointmentTypeTemplateMappingList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <AppointmentTypeTemplateMappingListInner
        resourcePath={resourcePath}
        createPath={createPath}
      />
    </ListCommandsProvider>
  );
}
