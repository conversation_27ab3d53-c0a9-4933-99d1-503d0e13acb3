import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import {
  Button,
  Paper,
  Stack,
  Text,
  Group,
  Grid,
  Card,
  Divider,
  Box,
  ScrollArea,
} from "@mantine/core";
import { IconPencil, IconCheck } from "@tabler/icons-react";
import { useState, useRef } from "react"; // Added useRef
import SignatureCanvas from "react-signature-canvas"; // Added import for signature canvas
interface PageSignContractProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

interface UnitSummary {
  unitNumber: string;
  accessories: number;
  deposit: number;
}

export default function PageSignContract({
  lead: _lead,
  setPages,
  pages,
}: PageSignContractProps) {
  const [isSigned, setIsSigned] = useState(false);
  const sigCanvasRef = useRef<SignatureCanvas | null>(null); // Ref for the signature canvas
  // Mock data for summary
  const unitSummaries: UnitSummary[] = [
    { unitNumber: "Unit#0123", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#4444", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#7777", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#7777", accessories: 67, deposit: 80 },
    { unitNumber: "Unit#7777", accessories: 67, deposit: 80 },
  ];
  const handleSignContract = () => {
    // Check if the signature canvas ref is available and if it's empty
    if (!sigCanvasRef.current || sigCanvasRef.current.isEmpty()) {
      // You might want to use a more user-friendly notification here
      // (e.g., Mantine's Notification component)
      alert("Please provide your signature in the box before confirming.");
      return;
    }

    // Optional: Get the signature as a data URL (e.g., for saving or displaying)
    // const signatureDataUrl = sigCanvasRef.current.toDataURL();
    // console.log("Signature captured (base64 data):", signatureDataUrl);
    // You would typically send this data to your backend or store it as needed.

    setIsSigned(true);
    // Simulate API call latency for signing process
    setTimeout(() => {
      setPages([...pages, "COMPLETED"]);
    }, 2000);
  };

  const handleClearSignature = () => {
    sigCanvasRef.current?.clear();
  };
  // Calculations
  const depositTotal = 160; // One-time
  const serviceCosts = 20; // One-time
  const subtotal = 80; // Recurring
  const taxes = 79.8;
  const total = 380;

  return (
    <Paper radius="md" h={"60vh"}>
      <Stack gap="lg">
        <Grid>
          <Grid.Col span={8}>
            <Card withBorder p="md">
              <Stack gap="md">
                <Text fw={500} size="lg">
                  Contract
                </Text>

                <Grid>
                  <Grid.Col span={6}>
                    <Stack gap="xs">
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Contract ID
                        </Text>
                        <Text size="sm" fw={500}>
                          0000001
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          First name
                        </Text>
                        <Text size="sm" fw={500}>
                          John
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Last name
                        </Text>
                        <Text size="sm" fw={500}>
                          Moose
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Birth date
                        </Text>
                        <Text size="sm" fw={500}>
                          22-11-1999
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Identification number
                        </Text>
                        <Text size="sm" fw={500}>
                          100123456789
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Email address
                        </Text>
                        <Text size="sm" fw={500}>
                          <EMAIL>
                        </Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">
                          Phone number
                        </Text>
                        <Text size="sm" fw={500}>
                          +31612345678
                        </Text>
                      </Group>
                    </Stack>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Stack gap="md">
                      <Stack gap="xs">
                        <Text fw={500}>Company</Text>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Company Name
                          </Text>
                          <Text size="sm" fw={500}>
                            Moose Corporation
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            KVK
                          </Text>
                          <Text size="sm" fw={500}>
                            *********
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            VAT
                          </Text>
                          <Text size="sm" fw={500}>
                            *********
                          </Text>
                        </Group>
                      </Stack>

                      <Stack gap="xs">
                        <Text fw={500}>Address</Text>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Street
                          </Text>
                          <Text size="sm" fw={500}>
                            Emmastraat 1
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Zipcode
                          </Text>
                          <Text size="sm" fw={500}>
                            1071A
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            City
                          </Text>
                          <Text size="sm" fw={500}>
                            Amsterdam
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Country
                          </Text>
                          <Text size="sm" fw={500}>
                            Netherlands
                          </Text>
                        </Group>
                      </Stack>
                    </Stack>
                  </Grid.Col>
                </Grid>
                <Box
                  style={{
                    border: `2px solid var(--mantine-color-gray-3)`,
                    borderRadius: `var(--mantine-radius-md)`,
                    padding: `var(--mantine-spacing-md)`,
                    minHeight: 180, // Adjusted for canvas and button
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {isSigned ? (
                    <Group gap="md">
                      <IconCheck
                        size={24}
                        color="var(--mantine-color-green-6)"
                      />
                      <Text c="green.6" fw={500}>
                        Contract Signed
                      </Text>
                    </Group>
                  ) : (
                    <Stack align="stretch" gap="sm" w="100%">
                      <Text size="sm" c="dimmed" ta="center">
                        Please sign below:
                      </Text>
                      <Box
                        style={{
                          border: `1px dashed var(--mantine-color-gray-5)`,
                          borderRadius: `var(--mantine-radius-sm)`,
                          overflow: "hidden", // To clip canvas corners with border-radius
                          backgroundColor: `var(--mantine-color-gray-0)`, // Light background for canvas
                          width: "100%", // Ensure the container takes full width
                          height: 120, // Explicit height for the canvas container
                        }}
                      >
                        <SignatureCanvas
                          ref={sigCanvasRef}
                          penColor="black"
                          canvasProps={{
                            // The canvas element will scale to fill its parent (Box above)
                            style: { width: "100%", height: "100%" },
                          }}
                        />
                      </Box>
                      <Button
                        variant="light"
                        color="gray"
                        size="xs"
                        onClick={handleClearSignature}
                        leftSection={<IconPencil size={14} />}
                      >
                        Clear Signature
                      </Button>
                    </Stack>
                  )}
                </Box>

                <Button
                  fullWidth
                  size="sm"
                  leftSection={<IconCheck size={20} />}
                  onClick={handleSignContract}
                  disabled={isSigned}
                  loading={isSigned} // Show loading spinner when isSigned is true (during timeout)
                >
                  {isSigned
                    ? "Processing Signature..."
                    : "Confirm & Sign Contract"}
                </Button>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={4}>
            <Card withBorder p="md">
              <Stack gap="md">
                <ScrollArea.Autosize mah={"36vh"} type="always" scrollbars="y">
                  <Box mr={"1vw"}>
                    {unitSummaries.map((unit) => (
                      <Stack key={unit.unitNumber} gap="xs">
                        <Text fw={500} size="sm">
                          {unit.unitNumber}
                        </Text>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Accessories
                          </Text>
                          <Text size="sm">€{unit.accessories}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">
                            Deposit
                          </Text>
                          <Text size="sm">€{unit.deposit}</Text>
                        </Group>
                      </Stack>
                    ))}
                  </Box>
                </ScrollArea.Autosize>
                <Divider />

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Deposit (one-time)
                  </Text>
                  <Text size="sm">€{depositTotal}</Text>
                </Group>

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Service costs (one-time)
                  </Text>
                  <Text size="sm">€{serviceCosts}</Text>
                </Group>

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Subtotal (recurring)
                  </Text>
                  <Text size="sm">€{subtotal}</Text>
                </Group>

                <Divider />

                <Group justify="space-between">
                  <Text size="sm" c="dimmed">
                    Taxes (btw)
                  </Text>
                  <Text size="sm">€{taxes.toFixed(2)}</Text>
                </Group>

                <Group justify="space-between">
                  <Text fw={600} size="lg">
                    Total
                  </Text>
                  <Text fw={600} size="lg">
                    €{total}
                  </Text>
                </Group>
              </Stack>
            </Card>
          </Grid.Col>
        </Grid>
      </Stack>
    </Paper>
  );
}
