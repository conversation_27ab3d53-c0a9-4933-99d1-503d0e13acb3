import { Title, Text, But<PERSON>, Container, Group } from "@mantine/core";
import classes from "./Forbidden.module.css";
import { useTranslation } from "react-i18next";
import { useAuth0 } from "@auth0/auth0-react";

export function Forbidden() {
  const { t } = useTranslation("common");
  const { logout } = useAuth0();

  return (
    <Container className={classes.root}>
      <div
        className={classes.label}
        style={{ fontFamily: "var(--mantine-font-family-headings" }}
      >
        403
      </div>
      <Title
        style={{ fontFamily: "var(--mantine-font-family-headings" }}
        className={classes.title}
      >
        {t("forbidden.title")}
      </Title>
      <Text c="dimmed" size="lg" ta="center" className={classes.description}>
        {t("forbidden.description")}
      </Text>
      <Group justify="center">
        <Button
          variant="subtle"
          size="md"
          onClick={() => {
            window.open("/app/home", "_self");
          }}
        >
          {t("forbidden.goBack")}
        </Button>
      </Group>
      <Group justify="center">
        <Button
          variant="subtle"
          size="md"
          onClick={() => {
            void logout();
          }}
        >
          {t("forbidden.logout")}
        </Button>
      </Group>
    </Container>
  );
}
