import { type Query<PERSON><PERSON> } from "react-query";
import { type PathKeys } from "@/types";
import { type MutableRefObject, type ReactNode } from "react";
import {
  type MRT_ColumnFiltersState,
  type MRT_ColumnDef,
  type MRT_RowData,
  type MRT_SortingState,
  type MRT_TableInstance,
} from "mantine-react-table";

export interface BasicResponse<TData> {
  data?: TData[] | null;
  totalPages?: number;
  totalCount?: number;
}
export interface BasicEntity {
  id?: string | null | undefined;
}

export interface ViewOption {
  value: string;
  label: string;
  resourcePath?: PathKeys;
  default?: boolean;
  visibleColumns?: string[];
  filter: MRT_ColumnFiltersState;
  sorting?: MRT_SortingState | undefined;
}

// Extend MRT_TableInstance if you need custom properties like activeView on it
export interface CustomTableInstance extends MRT_TableInstance<MRT_RowData> {
  activeView?: ViewOption;
}

export interface TableMantineProps {
  columns: MRT_ColumnDef<MRT_RowData>[];
  resourcePath: PathKeys;
  queryKey: QueryKey;
  entityPath: string;
  redirectTo?: string;
  rawData?: MRT_RowData[]; // Optionally pass raw data to be used instead of fetched data
  disableNavigation?: boolean;
  customNavigation?: boolean;
  customNavigate?: (id: string) => void;
  enableGrouping?: boolean;
  grouping?: string[];
  pageSize?: number;
  selectionEnabled?: boolean;
  toolbarEnabled?: boolean;
  tableRef?: MutableRefObject<CustomTableInstance | null>;
  visibleColumns?: string[]; // Initial visible columns if no views/default view specifies them
  toolbar?: ReactNode; // Custom content for toolbar
  initialSorting?: MRT_SortingState | undefined;
  viewOptions?: ViewOption[];
  title?: string; // Used if viewOptions are not provided
  loading?: boolean; // External loading state (optional)
  searchDisabled?: boolean;
}

export interface TableToolbarProps {
  viewOptions?: ViewOption[];
  activeView?: ViewOption;
  onViewChange: (value: string | null) => void;
  title?: string;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  searchDisabled?: boolean;
  table: MRT_TableInstance<MRT_RowData>;
  customToolbarContent?: ReactNode; // Renamed from 'toolbar' to avoid clash
}
