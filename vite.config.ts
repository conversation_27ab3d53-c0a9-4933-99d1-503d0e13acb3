import path from "path";
import { type UserConfig, defineConfig } from "vite";
import { sentryVitePlugin } from "@sentry/vite-plugin";
import react from "@vitejs/plugin-react-swc";
import mkcert from "vite-plugin-mkcert";

import "dotenv/config";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()].concat(process.env.DEV_SSL ? [mkcert()] : []).concat(
    process.env.NODE_ENV === "development"
      ? []
      : [
          sentryVitePlugin({
            authToken: process.env.SENTRY_AUTH_TOKEN,
            org: "unitlogic",
            project: "frontend",
          }),
        ],
  ),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    https: process.env.DEV_SSL,
    port: 8080,
  },
  build: { sourcemap: true },
  test: {
    environment: "jsdom",
    setupFiles: "./src/tests/setup.ts",
    globals: true,
  },
  css: {
    modules: {
      localsConvention: "camelCaseOnly",
    },
  },
} as UserConfig);
