import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionRender,
  OptionGroupRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { SingleLookup } from "@/components/Lookup/SingleLookup/SingleLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { useLookup } from "@/components/Lookup/Context/useLookup";
import { useTranslation } from "react-i18next";
import { ComboboxOptionGroupRender } from "../../Base/Structure/LookupRenderers";

const ENTITY = "lossReasons";
type ENTITY_TYPE = Schemas["LossReasonRetrieveDto"];

type LossReasonLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
    groupingEnabled?: boolean;
  };

export function LossReasonLookup({
  required = false,
  disabled = false,
  initial,
  initialId,
  identifier,
  groupingEnabled = false,
  ...props
}: LossReasonLookupProps) {
  const { searchTerm } = useLookup(
    identifier,
    initial?.name,
    initialId,
    initial,
    true,
  );

  const { t } = useTranslation("features");
  const queryParams: QueryParams = {
    pageSize: 50,
    filter: "RecordState==0",
  };

  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["LossReasonRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/LossReasons",
      queryKey: "lossReason",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.name")}</Box>
    </React.Fragment>
  );

  let options: JSX.Element[];

  if (groupingEnabled) {
    const groupedEntities = entityList.reduce<Record<string, JSX.Element[]>>(
      (acc, entity, index) => {
        if (!entity) return acc;

        const categoryLabel =
          t("lossReasons." + entity.lossReasonCategory?.name) ??
          t("lossReasons.other");

        const renderedOptions = [
          OptionRender(
            t("lossReasons." + entity.name),
            searchTerm,
            `name_${entity.id}`,
          ),
        ];

        const optionGroupElement = OptionGroupRender(
          entity.id!,
          index,
          t("lossReasons." + entity.name),
          entityList.length,
          infiniteScrollRef,
          renderedOptions,
        );

        if (!acc[categoryLabel]) {
          acc[categoryLabel] = [];
        }

        acc[categoryLabel]?.push(optionGroupElement);
        return acc;
      },
      {},
    );

    const orderedCategoryNames = [
      ...new Set(
        entityList
          .map((entity) => {
            if (!entity) return null;
            return (
              t("lossReasons." + entity.lossReasonCategory?.name) ??
              t("lossReasons.other")
            );
          })
          .filter(Boolean),
      ),
    ];

    options = orderedCategoryNames.map((categoryLabel) => {
      const optionsForCategory = groupedEntities[categoryLabel] || [];
      return ComboboxOptionGroupRender(categoryLabel, optionsForCategory);
    });
  } else {
    options = entityList
      .map((entity) => {
        if (!entity) return null;
        return OptionRender(entity.name, searchTerm, `name_${entity.id}`);
      })
      .filter(Boolean) as JSX.Element[];
  }

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      width={"30vw"}
      left={"35vw"}
      {...props}
    />
  );
}
