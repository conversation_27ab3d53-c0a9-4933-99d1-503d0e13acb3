import { Currency<PERSON>enderer } from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { RefundLookup } from "@/components/Lookup/Features/Refunds/RefundLookupField";
import { ProductLookup } from "@/components/Lookup/Features/Products/ProductLookupField";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function RefundProductsColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "refund",
      header: t("refundProducts.refund"),
      ...TableRenderer(RefundLookup, "refunds", ["number"]),
    },
    {
      accessorKey: "product",
      header: t("refundProducts.product"),
      ...TableRenderer(ProductLookup, "products", ["name"]),
    },
    {
      accessorKey: "quantityToReturn",
      header: t("refundProducts.quantityToReturn"),
      filterVariant: "range",
    },
    {
      accessorKey: "stillUsableQuantity",
      header: t("refundProducts.stillUsableQuantity"),
      filterVariant: "range",
    },
    {
      accessorKey: "price",
      header: t("refundProducts.price"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "totalPrice",
      header: t("refundProducts.totalPrice"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
  ];
  return columns;
}
