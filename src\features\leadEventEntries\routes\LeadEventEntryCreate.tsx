import { useEntityCreateMutation } from "@/features/entity/mutations";
import { type Schemas } from "@/types";
import { Button, Group } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";

interface LeadEventEntryCreateProps {
  close: () => void;
  refreshForm: () => void;
  leadId: string;
  eventType: "WalkIn";
  triggerSource: "User" | "System";
}

const LeadEventEntryCreate: React.FC<LeadEventEntryCreateProps> = ({
  close,
  leadId,
  eventType,
  triggerSource,
}) => {
  const { t } = useTranslation("features");
  const { mutate: create } = useEntityCreateMutation<
    Schemas["LeadEventEntry"],
    Schemas["LeadEventEntryCreateDto"]
  >({ resourcePath: "/api/LeadEventEntries", queryKey: "leadEventEntry" });
  const queryCache = useQueryClient();
  const createLeadEvent = () => {
    create(
      {
        leadId: leadId,
        leadEventType: eventType,
        leadEventTriggerSource: triggerSource,
      },
      {
        onSuccess: () => {
          notifications.show({
            color: "green",
            title: t("leads.walkInSuccesstitle"),
            message: t("leads.walkInSucessMessage"),
          });
        },
        onError: () => {
          notifications.show({
            color: "red",
            title: t("leads.walkInErrorTitle"),
            message: t("leads.walkInErrorMessage"),
          });
        },
        onSettled: () => {
          void queryCache.invalidateQueries(`lead_` + leadId);
          close();
        },
      },
    );
  };

  return (
    <Group justify="center" mt="xs">
      <Button onClick={createLeadEvent} w={"20%"}>
        {t("common.create")}
      </Button>
      <Button onClick={close} w={"20%"} color="gray">
        {t("common.cancel")}
      </Button>
    </Group>
  );
};

export default LeadEventEntryCreate;
