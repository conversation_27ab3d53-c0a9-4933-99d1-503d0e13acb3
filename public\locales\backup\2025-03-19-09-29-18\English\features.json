{"common": {"yes": "Yes", "no": "No", "confirmNavigation": "Unsaved changes will be lost. Continue?", "save": "Save", "leads": "Leads", "cancel": "Cancel", "create": "Create", "all": "All", "phoneCalls": "Phone Calls", "appointments": "Appointments", "callbacks": "Callbacks", "contactMoments": "Contact Moments", "leadEvents": "Lead Events", "webForms": "Web Forms", "emails": "Emails", "smsMessages": "SMS Messages", "filterActivities": "Filter Activities", "createNewActivity": "Create New Activity", "active": "Active", "inactive": "Inactive"}, "advisedProducts": {"createTitle": "Create Advised Product", "delete": "Delete Advised Product", "deleteConfirmation": "Are you sure you want to delete this Advised Product? This action is destructive.", "editTitle": "Edit Advised Product", "lead": "Lead", "price": "Price", "product": "Product", "quantity": "Quantity", "showTitle": "Advised Product", "submit": "Submit", "title": "Advised Products", "totalPrice": "Total Price", "quote": "Quote"}, "advisedUnits": {"businessUnit": "Business Unit", "createTitle": "Create Advised Unit", "delete": "Delete Advised Unit", "deleteConfirmation": "Are you sure you want to delete this Advised Unit? This action is destructive.", "editTitle": "Edit Advised Unit", "electricity": "Electricity", "insurance": "Insurance", "lead": "Lead", "lighting": "Lighting", "pricePerMonth": "Price Per Month", "pricePerWeek": "Price Per Week", "minPrice": "<PERSON><PERSON>", "maxPrice": "<PERSON><PERSON>", "reservation": "Reservation", "showTitle": "Advised Unit", "submit": "Submit", "title": "Advised Units", "type": "Type", "unit": "Unit", "unitType": "Unit Type", "quote": "Quote", "priceWarning": "Price Per Month must be between Minimum and Maximum Price."}, "appUsers": {"allRoles": "All Roles", "appUser": "App User", "assignedRoles": "Assigned Roles", "businessUnit": "Business Unit", "confirm": "Confirm", "currentApp": "Current App", "currentapp": "Current App", "editTitle": "Edit App User", "email": "Email", "error": "Error", "general": "General", "id": "Id", "lastUserLogin": "Last Login", "last_login": "Last Login", "manageRoles": "Manage Roles", "name": "Name", "noRowsSelected": "Please select atleast one row.", "nothingChangedMessage": "There is nothing to update.", "nothingChangedTitle": "No Changes", "showTitle": "App User", "submit": "Submit", "firstName": "First Name", "lastName": "Last Name", "department": "Department", "division": "Division", "jobTitle": "Job Title", "language": "Language", "timeZone": "Timezone", "english": "English", "dutch": "Dutch", "title": "App Users", "userGroup": "User Group"}, "appointments": {"appointmentState": "State", "leadAndContact": "Leads & Contacts", "movingHelp": "Moving Help", "name": "Attachment Name", "appointment": "Appointment", "attachment": "Attachment", "assignedTo": "Assigned To", "attachments": "Attachments", "appointmentType": "Appointment Type", "blockCalendar": "Block Calendar", "createPhoneCallFlag": "Register a phone call attempt to lead", "movingHelpBlockCalendar": "Moving Help - Block Calendar", "businessUnit": "Business Unit", "cancelButton": "Cancel", "closeAppointmentButton": "Close Appointment", "confirmCancelled": "Confirm Cancellation of Appointment", "confirmCompleted": "Confirm Completion of Appointment", "confirmNoShow": "Confirm No Show of Appointment", "contact": "Contact", "createAppointment": "Create Appointment", "createTitle": "Create Appointment", "delete": "Delete Appointment", "deleteConfirmation": "Are you sure you want to delete this Appointment? This action is destructive.", "description": "Description", "desk": "Desk", "editAppointment": "Edit Appointment", "editTitle": "Edit Appointment", "email": "Email", "endDate": "End Date", "isDefault": "<PERSON>", "lead": "Lead", "movingVan": "Moving Van", "ok": "OK", "setAppointmentCancelled": "<PERSON>cel Appointment", "setAppointmentCompleted": "Complete Appointment", "setAppointmentCompletedFromNoShow": "Change appointment to Completed", "setAppointmentNoShow": "Set appointment as No Show", "showTitle": "Appointment", "spaceTour": "Space Tour", "startDate": "Start Date", "subject": "Subject", "title": "Appointments", "trailer": "Trailer", "createSuccessTitle": "Success", "createSuccessMessage": "Appointment created and sent successfully!", "followUp": "Follow-Up", "engineRoomTour": "Engine Room Tour", "bankSafe": "<PERSON>ault Tour", "roboticStorageTour": "Robotic Storage Tour", "resetFilters": "Reset All Filters", "searchBusinessUnits": "Search Business Units...", "businessUnitTypeFilter": "Business Unit Type Filter", "searchAppointments": "Search Appointments...", "appointmentTypeFilter": "Appointment Type Filter", "selectedBUs": "Selected Business Units", "saveAndSend": "Save & Send", "save": "Save", "rescheduleAndSend": "Reschedule & Send", "duration": "Duration", "sendEmail": "Send Email", "cancel": "Cancel", "setAsCompleted": "Set as Completed", "setAsNoShow": "Set as Completed", "edit": "Edit"}, "attachments": {"attachments": "Attachments", "noAttachments": "No Attachments", "deleteTitle": "Delete confirmation", "deleteConfirmationQuestion": "Are you sure you want to delete the attachment", "deleteConfirmationWarning": "This action cannot be undone.", "deleteConfirmation": "Delete", "deleteCancel": "Cancel", "downloadLabel": "Download", "downloadAllLabel": "Download All", "removeAttachment": "Remove Attachment", "deleteAttachment": "Delete Attachment", "replaceAttachment": "Replace Attachment", "downloadAttachment": "Download Attachment", "addAttachment": "Add Attachment"}, "appointmentTypeTemplateMappings": {"appointmentType": "Appointment Type", "cancelButton": "Cancel", "htmlTemplate": "HTML Template", "title": "Appointment Types", "editAppointmentTypeTemplateMapping": "Edit Appointment Type Template Mapping", "editTitle": "Edit Appointment Type Template Mapping", "showTitle": "Appointment Type Template Mapping"}, "businessUnits": {"businessUnit": "Business Unit", "city": "City", "code": "Code", "country": "Country", "createTitle": "Create Business Unit", "delete": "Delete Business Unit", "deleteConfirmation": "Are you sure you want to delete this Business Unit? This action is destructive.", "description": "Description", "division": "Division", "editTitle": "Edit Business Unit", "email": "Email", "mainPhone": "Main Phone", "manager": "Manager", "name": "Name", "noBusinessUnitFound": "No Business Units Found", "parentBusiness": "Parent Business", "selectBusinessUnit": "Select Business Unit", "showTitle": "Business Unit", "street": "Street", "submit": "Submit", "title": "Business Units", "units": "Units", "website": "Website", "zip": "Postal Code", "recognitionPoints": "Recognition Points", "locationName": "Location Name", "roadwayHeight": "Roadway Height", "driveInHeightUnit": "Drive-In Height Unit", "parkingLength": "Parking Length", "parkingLotOutside": "Parking Lot Outside", "region": "Region", "costCentreCode": "Cost Centre Code", "afoMigrationId": "AFO Migration ID", "administrationCode": "Administration Code", "iban": "IBAN", "objectID": "Object ID", "dateOfOpening": "Date Of Opening", "recordState": "Status", "type": "Type", "accessControlType": "Access Control Type", "parkingSpotsType": "Parking Spots Type", "districtManager": "District Manager", "seniorManager": "Senior Manager", "active": "Active", "inactive": "Inactive", "selfStorage": "Self Storage", "engineRoom": "Engine Room", "bankSafe": "<PERSON><PERSON>", "roboticStorage": "Robotic Storage", "prisma": "Prisma", "sCAccess": "SC Access", "brivo": "Brivo", "cars": "Cars", "carSmallCaravan": "Car / Small Caravan", "campers": "Campers", "truck": "Truck", "usesTKB": "TKB", "usesTKBDescription": "Business Unit uses TKB", "isMrBox": "Mr<PERSON><PERSON>", "isMrBoxDescription": "Business Unit offers Mr. Box services", "isHidden": "Hide Business Unit", "isHiddenDescription": "Business Unit will be hidden from selectors", "usesDynamicPricing": "Dynamic Pricing", "usesDynamicPricingDescription": "Business Unit uses dynamic pricing", "businessUnitColor": "Business Unit Color", "isSelfStorage": "Self Storage", "isSelfStorageDescription": "Business Unit is self storage", "hasRoboticStorage": "Robotic Storage", "hasRoboticStorageDescription": "Business Unit is powered by robotic storage", "hasEngineRoom": "Engine Room", "hasEngineRoomDescription": "Business Unit has an engine room", "hasVault": "<PERSON><PERSON>", "hasVaultDescription": "Business Unit has a vault", "sectionDetails": "Business Unit Details", "sectionContacts": "Contact Information", "sectionManagement": "Management", "sectionLocation": "Location Details", "sectionParking": "Parking Details", "sectionFeatures": "Additional Features", "sectionTypes": "Unit Types"}, "userGroups": {"removeUser": "Remove User", "addUser": "Add User", "showTitle": "User Group", "title": "User Groups", "groupOwner": "Group Owner", "deleteConfirm": "Are you sure you want to delete the selected records?", "confirm": "Confirm", "updateSuccessMessage": "User Updated Successfully", "updateSuccessTitle": "Update Success", "noRowsSelected": "Please select atleast one row.", "error": "Error", "userGroupType": "Type", "businessUnit": "Business Unit", "salesAndService": "Sales & Service"}, "caseComments": {"case": "Case", "comment": "Comment", "createTitle": "Create Case Comment", "delete": "Delete Case Comment", "deleteConfirmation": "Are you sure you want to delete this Case Comment? This action is destructive.", "editTitle": "Edit Case Comment", "noCaseCommentsFound": "No Case Comments Found", "number": "Number", "showCaseComments": "Show Case Comments", "showTitle": "Case Comment", "submit": "Submit", "text": "Text", "title": "Case Comments"}, "caseReasons": {"createTitle": "Create Case Reason", "editTitle": "Edit Case Reason", "name": "Name", "noCaseReasonsFound": "No Case Reasons Found", "originCategory": "Origin Category", "selectCaseReason": "Select Case Reason", "showComplaintReason": "Show Complaint Reason", "showTitle": "Case Reason", "submit": "Submit", "title": "Case Reasons"}, "cases": {"EscalationReturned": "Escalation Returned", "active": "Active", "businessUnit": "Business Unit", "Standard": "Standard", "Webshop": "Webshop", "new": "New", "reopened": "Reopened", "cancel": "Cancel", "case": "Case", "caseClose": "Close Case", "caseCloseCancel": "Cancel", "caseCloseConfirm": "Close", "caseStartProgressSuccessTitle": "Status was changed successfully", "caseStartProgressSuccessMessage": "Status was set to in progresss...", "caseStartProgressErrorTitle": "Status was not changed!", "caseStartProgressErrorMessage": "Error occured while setting status to in progress...", "caseStartProgress": "Start Handling Case", "caseCloseConfirmation": "Are you sure you want to close this case?", "caseOpen": "Reopen Case", "caseOpenConfirm": "Open", "caseOpenConfirmation": "Are you sure you want to reopen this case?", "caseReasons": "Case Reasons", "close": "Close", "closed": "Closed", "complaintReasons": "Complaint Reasons", "confirm": "Confirm", "confirmEscalationTitle": "Confirm Escalation", "contact": "Contact", "createSuccessMessage": "The Case has been created successfully.", "createSuccessTitle": "Case Created", "createTitle": "Create Case", "delete": "Delete Case", "deleteConfirmation": "Are you sure you want to delete this Case? This action is destructive.", "description": "Description", "escalate": "Escalate Case", "escalated": "Escalated", "escalationErrorMessage": "Error occurred during escalation.", "escalationErrorTitle": "Error", "escalationReturned": "Escalation Returned", "escalationSuccessMessage": "Case Escalation was successful.", "escalationSuccessTitle": "Success", "inProgress": "In Progress", "inactive": "Inactive", "noCaseFound": "No Cases Found", "number": "Number", "originCategories": "Origin Categories", "originUrl": "Origin URL", "originalBusinessUnit": "Original Business Unit", "originalBusinessUnitIs": "Original Business Unit Id", "origins": "Origins", "recordState": "Record State", "reply": "Reply", "replyTemplate": "Template", "returnEscalation": "Return Escalation", "returnEscalationText": "Case will be returned to the original Business Unit {{BusinessUnitCode}}", "selectCase": "Select Case", "sendReply": "Reply", "sendReplyTooltip": "This action will not send an email to the CC and BCC", "sendReplyAll": "Reply All", "forward": "Forward", "senderEmail": "Sender <PERSON><PERSON>", "caseOwner": "Case Owner", "showTitle": "Case", "standard": "Standard", "status": "Status", "subject": "Subject", "title": "Cases", "general": "General", "history": "History", "updateSuccessMessage": "The Case has been updated successfully.", "updateSuccessTitle": "Case Updated", "webshop": "Webshop", "emails": "Emails", "closeCasesButton": "Close Cases", "closeCasesTitle": "Close Cases", "casesCloseConfirmation": "Are you sure you want to close the selected cases?", "closeCasesCancel": "Cancel", "closeCasesConfirm": "Close Selected Cases", "escalatedCasesView": "Sales & Service: Escalated Cases", "activeCases": "Active Cases", "inactiveCases": "Inactive Cases", "allCases": "All Cases"}, "complaintReasons": {"createTitle": "C<PERSON> Comp<PERSON>t Reason", "editTitle": "<PERSON> Complaint Reason", "name": "Name", "noComplaintReasonsFound": "No Complaint Reasons Found", "selectComplaintReason": "Select Complaint Reason", "showTitle": "Complaint Reason", "submit": "Submit", "title": "Complaint Reasons"}, "contactRoles": {"confirm": "Confirm", "contactRole": "Contact Role", "contactRoles": "Contact Roles", "delete": "Delete Contact Role", "deleteConfirmation": "Are you sure you want to delete this Contact Role? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "isMain": "Main", "manageContactRoles": "Manage Contact Roles", "name": "Name", "noTagsSelected": "No contact roles are selected.", "reset": "Reset", "selectAll": "Select All", "showTitle": "Contact Role", "successMessage": "Tags Updated Successfully", "successTitle": "Success", "title": "Contact Roles", "yes": "Yes", "no": "No"}, "contacts": {"contact": "Contact", "createTitle": "Create Contact", "dateOfBirth": "Date Of Birth", "delete": "Delete Contact", "deleteConfirmation": "Are you sure you want to delete this Contact? This action is destructive.", "editTitle": "Edit Contact", "email": "Email", "firstName": "First Name", "identificationNumber": "Identification Number", "identificationType": "Identification Type", "lastName": "Last Name", "mobile": "Mobile", "noContactsFound": "No Contacts Found", "number": "Number", "phone": "Phone", "reservations": "Reservations", "selectContact": "Select Contact", "showTitle": "Contact", "submit": "Submit", "title": "Contacts"}, "contactMoments": {"lead": "Lead", "comment": "Comment", "createContactMoment": "Create Contact Moment", "contactMoment": "Contact Moment"}, "contractLines": {"combinationLock": "Combination Lock", "contractId": "Contract", "contractLine": "Contract Line", "createSuccessMessage": "The Contract Line has been created successfully.", "createSuccessTitle": "Contract Line Created", "createTitle": "Create Contract Line", "delete": "Delete Contract Line", "deleteConfirmation": "Are you sure you want to delete this Contract Line? This action is destructive.", "discount": "Discount", "externalReference": "External Reference", "from": "From", "general": "General", "insuranceEnd": "Insurance End", "insuranceStart": "Insurance Start", "insuranceValue": "Insurance Value", "noContractLinesFound": "No Contract Lines Found", "price": "Price", "quantity": "Quantity", "remarks": "Remarks", "selectContractLine": "Select Contract Line", "showTitle": "Contract Line", "storageType": "Storage Type", "storageValue": "Storage Value", "title": "Contract Lines", "to": "To", "totalPrice": "Total Price", "unitGroup": "Unit Group", "updateSuccessMessage": "The Contract Line has been updated successfully.", "updateSuccessTitle": "Contract Line Updated", "vat": "VAT"}, "contracts": {"cancelReason": "Cancel Reason", "cancelledOn": "Cancelled On", "contact": "Contact", "contract": "Contract", "contractNumber": "Number", "contractStatus": "Contract Status", "createSuccessMessage": "The Contract has been created successfully.", "createSuccessTitle": "Contract Created", "createTitle": "Create Contract", "customer": "Customer", "delete": "Delete Contract", "deleteConfirmation": "Are you sure you want to delete this Contract? This action is destructive.", "firstCancelDate": "First Cancel Date", "from": "From", "general": "General", "invoicePeriodFrom": "Invoice Period From", "invoiceReference": "Invoice Reference", "invoicedUntil": "Invoiced Until", "invoicing": "Invoicing", "invoicingInterval": "Invoicing Interval", "invoicingType": "Invoicing Type", "moveOutDate": "Move Out Date", "paymentMethod": "Payment Method", "remarks": "Remarks", "selectContract": "Select Contract", "showTitle": "Contract", "signedOn": "Signed On", "title": "Contracts", "to": "To", "updateSuccessMessage": "Contract", "updateSuccessTitle": "Contract"}, "countries": {"code": "Code", "countries": "Countries", "country": "Country", "createTitle": "Create Country", "delete": "Delete Country", "deleteConfirmation": "Are you sure you want to delete this Country? This action is destructive.", "editTitle": "Edit Country", "mainPhone": "Main Phone", "name": "Name", "noStorageTypeFound": "No Countries Found", "selectStorageType": "Select Country", "showTitle": "Country", "submit": "Submit", "title": "Countries"}, "customerContacts": {"confirm": "Confirm", "contact": "Contact", "contactRole": "Contact Role", "createTitle": "Customer Contact", "customer": "Customer", "customerContacts": "Customer Contacts", "delete": "Delete Customer Contact", "deleteConfirmation": "Are you sure you want to delete this Customer Contact? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "manageCustomerContacts": "Manage Customer Contacts", "noTagsSelected": "No contact roles are selected.", "reset": "Reset", "selectAll": "Select All", "showTitle": "Customer Contact", "successMessage": "Tags Updated Successfully", "successTitle": "Success", "title": "Customer Contacts"}, "customers": {"address": "Address", "afoMigrationId": "AFO Migration Id", "city": "City", "country": "Country", "createTitle": "Create Customer", "customer": "Customer", "delete": "Delete Customer", "deleteConfirmation": "Are you sure you want to delete this Customer? This action is destructive.", "editTitle": "Edit Customer", "email": "Email", "fax": "Fax", "name": "Name", "noCustomersFound": "No Customers Found", "parentCustomer": "Parent Customer", "mobile": "Mobile", "phone": "Phone", "selectCustomer": "Select Customer", "showTitle": "Customer", "street": "Street", "submit": "Submit", "title": "Customers", "website": "Website", "zip": "Postal Code"}, "emails": {"attachments": "Attachments", "body": "Body", "cc": "CC", "delete": "Delete Email", "deleteConfirmation": "Are you sure you want to delete this Email? This action is destructive.", "download": "Download", "editTitle": "<PERSON> Email", "error": "Error", "from": "From", "name": "Name", "showHtml": "Show HTML", "showTitle": "Email", "showVisual": "Show Email", "subject": "Subject", "submit": "Submit", "title": "Emails", "to": "To", "createdOn": "Created On", "sender": "Sender", "showHistory": "Show History", "hideHistory": "Hide History"}, "entity": {"goBack": "Back", "cancel": "Cancel", "movingHelpNorth": "Moving Help North", "movingHelpSouth": "Moving Help South", "movingHelpWest": "Moving Help West", "createNew": "Create New", "createdBy": "Created By", "createdOn": "Created On", "callCount": "Call Count", "nextCallback": "Next Callback", "delete": "Delete", "deleteEntity": "Delete Entity", "deleteEntityConfirmation": "Are you sure you want to delete this entity? This action is destructive.", "edit": "Edit", "modifiedBy": "Modified By", "modifiedOn": "Modified On", "ownerId": "Owner", "refresh": "Refresh", "save": "Save", "saveAndClose": "Save & Close", "setDefaultView": "Set Default View", "setDefaultViewSuccessMessage": "Default view was set successfully", "setDefaultViewTitle": "Changing Default View", "createCustomView": "Create Custom View", "status": "Status", "wizards": "Wizards"}, "omnichannel": {"refresh": "Refresh <PERSON>", "fullNameIsEmpty": "Full Name Is Empty", "createAppointment": "Create Appointment", "createCall": "Create Call"}, "htmlTemplates": {"createTitle": "Create Html Template", "delete": "Delete Html Template", "deleteConfirmation": "Are you sure you want to delete this Html Template? This action is destructive.", "editTitle": "Edit Html Template", "html": "HTML English", "htmlTemplate": "Html Template", "cases": "Cases", "name": "Name", "showTitle": "Html Template", "submit": "Submit", "title": "Html Templates", "htmlNL": "HTML Dutch", "dutch": "Dutch", "english": "English", "subjectNL": "Subject Dutch", "subjectEN": "Subject English", "templateType": "Template Type", "service": "Service", "marketing": "Marketing"}, "leads": {"leadSourceHeader": "Lead Source", "omnichannelActivate": "Start", "omnichannelDeactivate": "Stop", "aSAP": "As Soon As Possible", "contactMoment": "Contact Moment", "dropdownAllTooltipLabel": "Open/Close All", "active": "Active", "activities": "Time Line", "activitiesDropDownLabel": "Activities", "addressSection": "Address", "advisedProducts": "Advised Products", "advisedUnits": "Advised Units", "appointment": "Appointment", "appointments": "Appointments", "approvalForAddressUsage": "Allow To Use Address", "archive": "Archive", "archiveUnit": "Archive Unit", "followUp": "Follow up", "backToParents": "Back to Parents", "basis": "<PERSON><PERSON>", "budget": "Budget", "businessAddress": "Business Address", "businessUnit": "Business Unit", "callCount": "Call Count", "callback": "Callback", "callCountReached": "Call Count Reached", "callCountNotReached": "Call Count Not Reached", "callbackAppointment": "Callback Appointment", "cancel": "Cancel", "cancelReservation": "Cancel Reservation", "city": "City", "cleanedUp": "Cleaned Up", "close": "Close", "comments": "Comments", "companyName": "Company Name", "competitionLocation": "Competition Location", "competitionPrice": "Competition Price", "confirm": "Confirm", "confirmReservation": "Do you really want to reserve advised units?", "confirmReservationCancel": "Do you really want to cancel current reservation?", "confirmReservationExtend": "Do you really want to extend current reservation?", "confirmReservationTitle": "Confirm Reservation", "confirmSendQuote": "Please confirm sending the quote", "manageQuotes": "Manage Quotes", "contract": "Contract", "contractNumberHeader": "Contract Number", "country": "Country", "createSuccessMessage": "The Lead has been created successfully.", "createSuccessTitle": "Lead Created", "createTitle": "Create Lead", "crisisNursingPension": "To crisis shelter, nursing or elderly home", "customDiscount": "Custom Discount", "currentQuote": "Current Quote", "delete": "Delete Lead", "deleteConfirmation": "Are you sure you want to delete this Lead? This action is destructive.", "description": "Description", "history": "History", "discount": "Discount", "discountText": "Discount Text", "distance": "Distance", "editTitle": "Edit Lead", "email": "Email", "emailHeader": "Email", "emigration": "Emigration", "engineRooms12": "Engine Rooms 12", "engineRooms6": "Engine Rooms 6", "engineRooms": "Engine Rooms", "existingContactId": "Existing Contact", "existingCustomerId": "Existing Customer", "extend": "Extend", "extendUntil": "Extend Until", "extraLarge": "Extra Large", "extraSmall": "Extra Small", "extraSpaceHobby": "Extra Space Hobby", "extraSpaceLiveSmaller": "Extra Space Live Smaller", "extraSpaceLiveTogether": "Extra Space Live Together", "extraSpaceSeasonalStorage": "Extra Space Seasonal Storage", "extraSpaceStaging": "Extra Space Staging", "existingLeadFound": "Existing Lead Found", "facility": "Facility", "facilityUnit": "Facility Unit", "fax": "Fax", "firstName": "First Name", "fixedTerm12": "Fixed Term 12", "fixedTerm6": "Fixed Term 6", "form": "Form", "foundAnother": "Found Another", "freeStorageGift": "Get your free storage gift", "general": "General", "generalCost": "What is it going to cost?", "homeService": "Home Service", "inactive": "Inactive", "large": "Large", "lastName": "Last Name", "lead": "Lead", "leadLostConfirm": "Confirm loss reason", "leadLostTitle": "Select a reason for losing the Lead", "leftForFamily": "Left For Family", "lettersMail": "Letters Mail", "longStay12to10": "Long Stay 12 to 10", "longStay12to11": "Long Stay 12 to 11", "lossReason": "Loss Reason", "lost": "Lost", "lowestPriceGuarantee": "Lowest Price Guarantee", "mailbox": "Mailbox", "medium": "Medium", "mobile": "Mobile", "mobileHeader": "Mobile", "months0to3": "0-3 months", "months3to6": "3-6 months", "months6to9": "6-9 months", "months9to12": "9-12 months", "motor": "Motor", "motorStorageCost": "What does motor storage cost?", "motorUnitBasis": "Motor Unit Basis", "motorUnitBudget": "Motor Unit Budget", "motorUnitPlus": "Motor Unit Plus", "moveInDate": "Move-In Date", "movingCancelled": "Moving Cancelled", "movingHelp": "Moving Help", "movingHouse": "Moving House", "noLeadsFound": "No Leads Found", "newsletter": "Newsletter", "null": "", "nextCallback": "Next Callback", "walkIn": "Walk-In", "phoneCallAtBU": "Phone Call At Business Unit", "phoneCallAtSalesService": "Phone Call At Sales And Service", "priority": "Priority", "advertisement": "Advertisement", "customerEvent": "Customer Event", "nationalAccount": "National Account", "googleAdWords": "Google Ad Words", "other": "Other", "partner": "Partner", "purchasedList": "Purchased List", "website": "Website", "salesLetter": "Sales Letter", "mr": "Mr", "mrBox": "mrBOX", "mrMrs": "Mr / Mrs", "mrs": "Mrs", "mx": "Mx", "NationalAccounts": "National Account", "new": "New", "inProgress": "In Progress", "reopened": "Reopened", "escalated": "Escalated", "escalationReturned": "Escalation Returned", "closed": "Closed", "noContact": "No Contact", "notReached": "Not Reached", "phoneCallStatus": "Phone Call Status", "reached": "Reached", "oneMonth": "1 Month", "oneWeek": "1 Week", "twoWeeks": "2 Weeks", "optOutEmail": "Opt Out Email", "optOutPhone": "Opt Out Phone", "pOBox": "PO Box", "parkingPlace": "Parking Place", "scooterStorage": "Scooter Storage", "webshopSelfService": "Webshop Self Service", "boatStorage": "Boot Storage", "caravanStorage": "Caravan Storage", "camperStorage": "Camper Storage", "passingAway": "Passing Away", "personnelUnit": "Personnel Unit", "phone": "Phone", "phoneCall": "Phone Call", "phoneCalls": "Phone Calls", "phoneHeader": "Phone", "plus": "Plus", "processStage": "Process Stage", "processStageHeader": "Process Stage", "productType": "Product Type", "promoRentFor4PayFor2": "Promo Rent for 4 Pay for 2", "promotionCode": "Promotion Code", "question": "Question", "storageSpace": "Storage Space", "quotationEmailSent": "Quotation Email Sent", "recordState": "Record State", "recordStateHeader": "Record State", "refurbishment": "Refurbishment", "rentAsBusiness": "Rent as Business", "rentFor3PayFor2": "Rent for 3 Pay for 2", "rentFor4PayFor3": "Rent for 4 Pay for 3", "rentFor6PayFor5": "Rent for 6 Pay for 5", "rentVehicle": "Rent Vehicle", "redirectedToExistingLead": "You were redirected to an existing lead.", "leadSource": "Lead Source", "reservation": "Reservation", "webformTitle": "Webform Title", "webformDetails": "Webform Details", "preferredLanguage": "Preferred Language", "english": "English", "dutch": "Dutch", "reservationCancelErrorMessage": "Cancelling reservation was not successful, please see the error.", "reservationCancelErrorTitle": "Reservation Cancelling Failed", "reservationCancelSuccessMessage": "Reservation was canceled successfully.", "reservationCancelSuccessTitle": "Reservation Canceled", "reservationErrorMessage": "Reservation was not successful, please see the error.", "reservationErrorTitle": "Reservation Failed", "reservationExtendErrorMessage": "Extending reservation was not successful, please see the error.", "reservationExtendErrorTitle": "Reservation Extension Failed", "reservationExtendSuccessMessage": "Reservation was extended successfully.", "reservationExtendSuccessTitle": "Reservation Extended", "reservationAlreadyExtendedMessage": "Reservation is already extended. Please cancel it and create a new one.", "reservationAlreadyExtendedTitle": "Reservation Already Extended", "cannotExtendResFromCurrentStatusMessage": "Cannot extend reservation from current status. Please cancel and create a new one.", "cannotExtendResFromCurrentStatusTitle": "Reservation Cannot be Extended", "reservationFieldsRequiredMessage": "Please fill Reservation Start and Reservation Until correctly.", "reservationFieldsRequiredTitle": "Required <PERSON>", "reservationStart": "Reservation Start", "reservationStatus": "Reservation Status", "reservationSuccessMessage": "Selected Units were reserved.", "reservationSuccessTitle": "Reservation Created", "reservations": "Reservations", "reserveUnits": "Reserve Units", "reservedUntil": "Reserved Until", "salutation": "Salutation", "savor": "Savor", "selectLead": "Select Lead", "selfService": "Self Service", "unknown": "Unknown", "separation": "Separation", "send": "Send", "sendNewQuote": "Send New Quote", "showTitle": "Lead", "sixMonths": "6 Months", "sizeOfUnit": "Size Of Unit", "small": "Small", "soldEverything": "Sold Everything", "spaceTour": "Space Tour", "sms": "SMS Message", "spaceTourRemarks": "Space Tour Remarks", "startWithin": "Start Within", "stockStorage": "Stock Storage", "storageCalculator": "Storage Calculator", "storageDuration": "Storage Duration", "storageType": "Storage Type", "storageUnitL": "Storage Unit Large", "storageUnitM": "Storage Unit Medium", "storageUnitReason": "Storage Unit Reason", "storageUnitS": "Storage Unit Small", "storageUnitXL": "Storage Unit Extra Large", "storageUnitXS": "Storage Unit Extra Small", "storageValue": "Storage Value", "street": "Street", "submit": "Submit", "supersize": "Supersize", "temporarySolutionCanceled": "Temporary Solution Canceled", "tireStorageBasis": "Tire Storage Basis", "tireStorageBudget": "Tire Storage Budget", "tireStoragePlus": "Tire Storage Plus", "title": "Leads", "tooExpensiveBudget": "Too Expensive Budget", "tooExpensiveCheaper": "Too Expensive Cheaper", "tooExpensiveWebsite": "Too Expensive Website", "transportation": "Transportation", "twoMonths": "2 Months", "threeMonths": "3 Months", "type": "Type", "later": "Later", "updateProcessStageSuccessMessage": "The Process Stage has been updated successfully.", "updateProcessStageSuccessTitle": "Process Stage Updated", "updateSuccessMessage": "The Lead has been updated successfully.", "updateSuccessTitle": "Lead Updated", "waitingForStudentHome": "Waiting for Student Home", "wantedPrice": "Wanted Price", "won": "Won", "wrongNumber": "Wrong Number", "zip": "Zip", "cancelled": "Cancelled", "aboutToExpire": "About To Expire", "converted": "Converted", "extended": "Extended", "expired": "Expired", "sendQuoteSuccessMessage": "Quo<PERSON> was sent successfully.", "sendQuoteSuccessTitle": "Success", "sendQuoteErrorTitle": "Error", "sendQuoteErrorMessage": "Sending Quote has failed. Please see the error.", "reOpenSuccessTitle": "Success", "reOpenSuccessMessage": "Lead was re-opened successfully", "reOpenErrorTitle": "Error", "reOpenErrorMessage": "Re-Opening of Lead has failed. Please see the error.", "reOpen": "Re-Open", "reOpenMessage": "After Lead is re-opened, it's Loss Reason will be cleared and it's Process Stage be set to New.", "confirmReOpen": "Do you really want to open a closed Lead?", "webForm": "WebForm", "optInType": "Opt-In Type", "optIn": "Opt-In", "doubleOptIn": "Double Opt-In", "optOut": "Opt-Out", "leadEvent": "Lead Event", "price": "Price", "unitSize": "Unit Size", "step": "Step", "volume": "Volume", "amount": "Amount", "moveStepperTitle": "Lead Stepper", "moveStepperText": "Do you really want to move lead to selected step?", "moveStepperConfirm": "Move to this step", "moveStepperCancel": "Cancel", "callSection": "Call Info", "leadLossSuccessTitle": "Lead was lost successfully.", "leadLossSuccessMessage": "Success", "leadLossErrorTitle": "Error", "leadLossErrorMessage": "Losing the lead failed. Please see the error.", "makeAnAppointment": "Make an Appointment", "postNL": "PostNL", "social": "Social", "sendEmail": "Send Email", "hasAttachment": "Has Attachments", "createWalkin": "Create Walk-In", "walkInSuccesstitle": "Succes", "walkInSucessMessage": "Walk-In was created successfully.", "walkInErrorTitle": "Error", "walkInErrorMessage": "Walk-In creation encountered an error."}, "leadList": {"callListStoreManagerView": "Call List"}, "lookup": {"nothingFound": "Nothing found..."}, "lossReasons": {"editTitle": "Edit Loss Reason", "name": "Name", "price": "Price", "product": "Product", "showTitle": "Loss Reason", "submit": "Submit", "title": "Loss Reasons", "htmlTemplate": "HTML Template", "Wrong number": "Wrong number", "Product or service not available": "Product or service not available", "Already a tenant": "Already a tenant", "Informed for somebody else": "Informed for somebody else", "General question location/transport": "General question location/transport", "Other solution": "Other solution", "Rented at competitor": "Rented at competitor", "Don't call me, I'll call you": "Don't call me, I'll call you", "Sold everything": "Sold everything", "Too expensive": "Too expensive", "Wishes not to be contacted": "Wishes not to be contacted", "Wants to know price": "Wants to know price", "Duplicate": "Duplicate", "Wrong information": "Wrong information", "Not reached after 6 attempts": "Not reached after 6 attempts", "Test": "Test", "Distance Too Far": "Distance Too Far", "Forgot Appointment": "Forgot Appointment", "Something Intervened": "Something Intervened", "Marketing": "Marketing", "Spam": "Spam", "No Show": "No Show", "Sales": "Sales"}, "mailbox": {"businessUnit": "Business Unit", "createTitle": "Create Mailbox", "delete": "Delete Mailbox", "deleteConfirmation": "Are you sure you want to delete this Mailbox? This action is destructive.", "description": "Description", "editTitle": "Edit Mailbox", "email": "Email", "name": "Name", "noMailboxFound": "No Mailboxes Found", "selectMailbox": "Select Mailbox", "showTitle": "Mailbox", "submit": "Submit", "title": "Mailboxes"}, "originCategories": {"code": "Code", "createTitle": "Create Origin Category", "editTitle": "Edit Origin Category", "name": "Name", "noOriginCategoriesFound": "No Origin Categories Found", "selectOriginCategory": "Select Origin Category", "showTitle": "Origin Category", "submit": "Submit", "title": "Origin Categories"}, "origins": {"category": "Category", "createTitle": "Create Origin", "editTitle": "Edit Origin", "name": "Name", "noOriginsFound": "No Origins Found", "selectOrigin": "Select Origin", "showTitle": "Origin", "submit": "Submit", "title": "Origins"}, "phoneCalls": {"autoDescription": "Created automatically after losing a lead.", "phoneCallStatus": "Status", "blockCalendar": "Block Calendar", "businessUnit": "Business Unit", "cancelButton": "Cancel", "closePhoneCallButton": "Close Phone Call", "confirmCancelled": "Close Phone Call as Cancelled?", "confirmReached": "Close Phone Call as Reached?", "confirmNotReached": "Close Phone Call as Not Reached?", "contact": "Contact", "createPhoneCall": "Create Phone Call", "createCallback": "Create Callback", "confirmCallback": "Create Callback?", "createTitle": "Create Phone Call", "delete": "Delete Phone Call", "deleteConfirmation": "Are you sure you want to delete this Phone Call? This action is destructive.", "description": "Description", "desk": "Desk", "editPhoneCall": "Edit Phone Call", "editTitle": "Edit Phone Call", "editCallback": "Edit Callback", "endDate": "End Date", "lead": "Lead", "movingVan": "Moving Van", "ok": "OK", "setPhoneCallCancelled": "Cancel", "setPhoneCallCompleted": "Complete", "setPhoneCallNotReached": "Not Reached", "setPhoneCallReached": "Reached", "showTitle": "Phone Call", "spaceTour": "Space Tour", "startDate": "Start Date", "subject": "Subject", "title": "Phone Calls", "trailer": "Trailer", "reached": "Reached", "notReached": "Not Reached", "updateStateSuccessTitle": "Success", "updateStateSuccessMessage": "Phone Call Updated", "createStateSuccessTitle": "Success", "createStateSuccessMessage": "Phone Call Created", "callbackDate": "Callback Date", "callback": "Callback"}, "prices": {"allPrices": "All Prices", "contact": "Price", "createTitle": "Create Price", "delete": "Delete Contact", "deleteConfirmation": "Are you sure you want to delete this Price? This action is destructive.", "editTitle": "Edit Price", "from": "From", "noPricesFound": "No Prices Found", "pricePerMonth": "Price Per Month", "minPrice": "<PERSON><PERSON>", "maxPrice": "<PERSON><PERSON>", "selectPrice": "Select Price", "showTitle": "Price", "submit": "Submit", "title": "Prices", "to": "To", "unit": "Unit"}, "products": {"createTitle": "Create Product", "delete": "Delete Product", "deleteConfirmation": "Are you sure you want to delete this Product? This action is destructive.", "editTitle": "Edit Product", "name": "Name", "price": "Price", "product": "Product", "showTitle": "Product", "submit": "Submit", "title": "Products", "allowPriceChange": "Allow Price Change", "afoMigrationId": "AFO Migration Id", "yes": "Yes", "no": "No"}, "quotes": {"createTitle": "Create Quote", "delete": "Delete Quote", "deleteConfirmation": "Are you sure you want to delete this Quote? This action is destructive.", "editTitle": "Edit Quote", "name": "Name", "quote": "Quote", "showTitle": "Quote", "submit": "Submit", "title": "Quotes", "number": "Number", "totalOneTimeFee": "Total One-Time Fee", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "totalMonthlyPrice": "Total Monthly Price", "status": "Status", "expirationDate": "Expiration Date", "lead": "Lead", "active": "Active", "draft": "Draft", "sent": "<PERSON><PERSON>", "accepted": "Accepted", "denied": "Denied", "expired": "Expired", "revised": "Revised", "cancelled": "Cancelled", "owner": "Owner", "createdOn": "Created On", "emails": "Emails", "cancelQuote": "<PERSON><PERSON> Quote", "denyQuote": "<PERSON><PERSON>"}, "refundProducts": {"createTitle": "Create Refund Product", "price": "Price", "product": "Product", "quantityToReturn": "Qty. to Return", "refund": "Refund", "showTitle": "Refund Product", "stillUsableQuantity": "Still Usable Qty.", "title": "Refund Products", "totalPrice": "Total Price", "delete": "Delete Refund Product", "deleteConfirmation": "Are you sure you want to delete this refund product?"}, "refunds": {"businessUnit": "Business Unit", "case": "Case", "checked": "Checked", "closed": "Closed", "contact": "Contact", "createTitle": "Create Refund", "creditCard": "Credit Card", "customer": "Customer", "customerBankAccountNumber": "Customer Bank Account Number", "delete": "Delete Refund", "deleteConfirmation": "Are you sure you want to delete this refund?", "description": "Description", "handlingDate": "Handling Date", "iDeal": "iDeal", "incomplete": "Incomplete", "incompleteProduct": "Incomplete Product", "internalRemarks": "Internal Remarks", "klarna": "<PERSON><PERSON><PERSON>", "new": "New", "normalUseProductDamage": "Normal Use Product Damage", "number": "Number", "orderNumber": "Order Number", "other": "Other", "payPal": "PayPal", "paymentMethod": "Payment Method", "pinOnLocation": "Pin On Location", "postNLProductDamage": "Post NL Product Damage", "quality": "Quality", "returnReason": "Return Reason", "returningUnused": "Returning Unused", "showTitle": "Refund", "status": "Status", "title": "Refunds", "totalOrderAmount": "Total Order Amount", "totalRefundedAmount": "Refunded Amount", "updateSuccessMessage": "Refund has been updated successfully.", "updateSuccessTitle": "Update Successful", "proofOfOrder": "Proof Of Order", "bankAccount": "Bank Account"}, "reservations": {"createTitle": "Create Reservation", "delete": "Delete Reservation", "deleteConfirmation": "Are you sure you want to delete this Reservation? This action is destructive.", "editTitle": "Edit Reservation", "lead": "Lead", "reservationStatus": "Reservation Status", "reservedUntil": "Reserved Until", "showTitle": "Reservation", "start": "Start", "status": "Status", "submit": "Submit", "title": "Reservations", "until": "Until", "sendEmail": "Send Email"}, "roles": {"description": "Description", "error": "Error", "id": "Id", "name": "Name", "noRolesSelected": "No roles are selected.", "showTitle": "Role", "successMessage": "Roles Updated Successfully", "successTitle": "Success", "title": "Roles"}, "settings": {"appointmentEmailTemplate": "Appointment Em<PERSON> Template", "delete": "Delete Setting", "deleteConfirmation": "Are you sure you want to delete this Setting? This action is destructive.", "editTitle": "Edit Setting", "name": "Name", "quoteEmailTemplate": "Quote <PERSON><PERSON>", "reservationEmailTemplate": "Reservation Email Template", "sendEmails": "Enable Email Sending", "setting": "Setting", "showTitle": "Setting", "submit": "Submit", "title": "Settings", "quoteExpiration": "Quote Expiration", "quoteAttachmentTemplate": "Quote At<PERSON><PERSON><PERSON> Template", "salesAndServiceGroup": "Sales And Service Group", "omnichannelPriority": "Omnichannel Priority", "callbackStoreManagerEmailTemplate": "Callback Store Manager <PERSON><PERSON>late", "callbackSalesEmailTemplate": "Callback Sales & Service Email Template", "noShowAppointmentEmailTemplate": "No Show Appointment Em<PERSON> Template", "general": "General", "templateMappings": "Template Mappings", "minimumDepositForUnits": "Minimum Deposit For Units", "dateFormat": "Date Format"}, "smsMessages": {"delete": "Delete SMS Message", "deleteConfirmation": "Are you sure you want to delete this SMS Message? This action is destructive.", "editTitle": "Edit SMS Message", "name": "Name", "showTitle": "SMS Message", "submit": "Submit", "title": "SMS Messages", "sms": "SMS Message", "subject": "Subject", "recipients": "Recipients", "externalId": "External Id", "body": "Body", "lead": "Lead"}, "storageTypes": {"code": "Code", "createTitle": "Create Storage Type", "delete": "Delete Storage Type", "deleteConfirmation": "Are you sure you want to delete this Storage Type? This action is destructive.", "editTitle": "Edit Storage Type", "mainPhone": "Main Phone", "name": "Name", "noStorageTypeFound": "No Storage Types Found", "selectStorageType": "Select Storage Type", "showTitle": "Storage Type", "storageType": "Storage Type", "storageTypes": "Storage Types", "submit": "Submit", "title": "Storage Types", "Trailer": "Trailer", "Archive": "Archive", "Car": "Car", "Tire Storage": "Tire Storage", "Caravan": "Caravan", "Electronics": "Electronics", "Household Goods": "Household Goods", "Household Goods And Inventory": "Household Goods And Inventory", "Inventory": "Inventory", "Art": "Art", "Motorcycle": "Motorcycle", "Other": "Other", "Mail": "Mail", "Moving Boxes": "Moving Boxes", "Inventory Stock": "Inventory Stock", "Webshop": "Webshop"}, "tags": {"confirm": "Confirm", "delete": "Delete Tag", "deleteConfirmation": "Are you sure you want to delete this Tag? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "manageTags": "Manage Tags", "name": "Name", "noTagsSelected": "No tags are selected.", "reset": "Reset", "selectAll": "Select All", "showTitle": "Tag", "successMessage": "Tags Updated Successfully", "successTitle": "Success", "title": "Tags", "unitTags": "Unit Tags"}, "unitTypes": {"code": "Code", "contractRemark": "Contract Remark", "contractReprot": "Contract Report", "editTitle": "Unit Type", "minimumInsuranceValue": "Minimum Insurance Value", "moveBusFreeHour": "Move Bus Free Hours", "moveBusFreeKm": "Move Bus Free Km's", "name": "Name", "proposedInsuranceValue": "Proposed Insurance Value", "showTitle": "Unit Type", "title": "Unit Types", "unitHeight": "Height", "unitLength": "Length", "unitVolume": "Volume", "unitWidth": "<PERSON><PERSON><PERSON>"}, "units": {"area": "Area", "available": "Available", "available24Hours": "Available 24 Hours", "blocked": "Blocked", "businessUnit": "Business Unit", "createTitle": "Unit", "delete": "Delete Business Unit", "deleteConfirmation": "Are you sure you want to delete this Unit? This action is destructive.", "distanceToElevator": "Distance to Elevator", "editTitle": "Edit Unit", "email": "Email", "floor": "Floor", "basement": "Basement", "floor0": "Floor 0", "floor1": "Floor 1", "floor2": "Floor 2", "floor3": "Floor 3", "floor4": "Floor 4", "floor5": "Floor 5", "floor6": "Floor 6", "floor7": "Floor 7", "floor8": "Floor 8", "floor9": "Floor 9", "parking": "Parking", "parkingInside": "Parking Inside", "height": "Height", "inMaintenance": "In Maintenance", "inUse": "In Use", "internalUse": "Internal Use", "length": "Length", "lengthHeader": "Length m", "mainPhone": "Main Phone", "moveOut": "Move Out", "contractCancelled": "Contract Cancelled", "awaitingHandover": "Awaiting Handover", "notInUse": "Not in Use", "noUnitFound": "No Units Found", "number": "Number", "prices": "Prices", "reserved": "Reserved", "selectUnit": "Select Unit", "showTitle": "Unit", "status": "Status", "submit": "Submit", "tags": "Tags", "title": "Units", "unit": "Unit", "unitCode": "Unit Code", "unitId": "Unit Id", "unitType": "Unit Type", "volume": "Volume", "volumeHeader": "Volume m³", "width": "<PERSON><PERSON><PERSON>", "widthHeader": "Width m"}, "audits": {"title": "Audits", "operation": "Operation", "changes": "Changes"}, "appUserBusinessUnits": {"title": "Assigned Business Units", "createTitle": "Assign Business Unit", "delete": "Remove Business Unit", "deleteConfirmation": "Are you sure you want to delete this Business Unit? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "manageCustomerContacts": "Manage Business Unit", "reset": "Reset", "selectAll": "Select All", "showTitle": "Business Unit", "successTitle": "Success"}, "customerSearch": {"searchTitle": "Search Customers", "noResults": "No records found, please refine your search...", "moreRecordsAvailable": "More records available, please refine your search...", "pleaseSearch": "Please enter a search term:", "charactersLeft": "characters left..."}, "webformEntries": {"webformEntry": "Webform Entry", "webformEntries": "Webform Entries", "entryId": "Entry Id", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "mobile": "Mobile", "webformTitle": "Webform Title", "webformDetails": "Webform Details", "leadSource": "Lead Source", "productType": "Product Type", "transportation": "Transportation", "startWithin": "Start Within", "price": "Price", "step": "Step", "unitSize": "Unit Size", "preferredLanguage": "Preferred Language", "transportationRequest": "Transportation Request", "comments": "Comments", "utm_campaign": "UTM Campaign", "utm_content": "UTM Content", "utm_medium": "UTM Medium", "utm_source": "UTM Source", "utm_term": "UTM Term", "gclid": "GCLID", "clientID": "Client ID", "trackingId": "Tracking Id", "referrer": "<PERSON><PERSON><PERSON>"}}