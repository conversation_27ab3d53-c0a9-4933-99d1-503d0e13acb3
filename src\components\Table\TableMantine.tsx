import { useEffect, useMemo, useRef } from "react";

import { useNavigate, useLocation } from "react-router";
import { MantineReactTable, useMantineReactTable } from "mantine-react-table";
import {
  Box,
  type ComboboxItem,
  type ComboboxLikeRenderOptionInput,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { LookupProvider } from "@/components/Lookup/Context/LookupContext";
import {
  getFilterString,
  getQueryParams,
  filterRenderOption,
} from "./utils/tableUtils";
import {
  type TableMantineProps,
  type BasicResponse,
  type BasicEntity,
  type CustomTableInstance,
} from "./types";
import { useTableUrlSync } from "./hooks/useTableUrlSync";
import { useTableStateManagement } from "./hooks/useTableStateManagement";
import { useTableStore } from "@/features/entity/stores/useTableStore";
import { useEntityListQuery } from "@/features/entity/queries";
import { TableToolbar } from "./TableToolbar";

// Default Page Size constant (can be shared or defined here)
const DEFAULT_PAGE_SIZE = 100;

export function TableMantine<
  TData extends BasicEntity,
  TResponse extends BasicResponse<TData>,
>({
  columns: baseColumns, // Rename prop to indicate they might be enriched
  resourcePath: baseResourcePath, // Base path, might be overridden by view
  queryKey,
  entityPath,
  redirectTo,
  disableNavigation = false,
  customNavigation = false,
  customNavigate,
  pageSize = DEFAULT_PAGE_SIZE,
  rawData,
  selectionEnabled = true,
  toolbarEnabled = true,
  tableRef,
  toolbar: customToolbarContent, // Rename for clarity
  visibleColumns: initialVisibleColumnsProp,
  initialSorting: initialSortingProp, // Rename for clarity
  viewOptions,
  title,
  enableGrouping = false,
  grouping = [],
  searchDisabled = false,
}: TableMantineProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation("features");
  const setViewValueInStore = useTableStore((state) => state.setViewValue);

  // Determine if we are on a primary list view (adjust logic as needed)
  const isListView = useMemo(() => {
    const pathnameParts = location.pathname.split("/").filter(Boolean);
    return pathnameParts.length === 2 && pathnameParts[0] === "app";
  }, [location.pathname]);

  const defaultViewOption = useMemo(
    () => viewOptions?.find((option) => option.default),
    [viewOptions],
  );

  const { initialStateFromUrl, updateUrlParams } = useTableUrlSync({
    viewOptions,
    defaultViewOption,
    initialSorting: initialSortingProp,
    isListView,
  });

  const {
    searchTerm,
    debouncedSearchTerm,
    pagination,
    columnFilters,
    sorting,
    columnFilterFns,
    activeView,
    columnVisibility,
    isLoading,
    // Setters
    setSearchTerm,
    setPagination,
    setIsLoading,
    // setColumnFilters, // Use onColumnFiltersChange
    setColumnFilterFns,
    setColumnVisibility,
    handleViewChange, // Function to trigger view change logic
    onColumnFiltersChangeCallback,
    onSortingChangeCallback,
    // Derived/Processed
    enrichedColumns, // Use these columns for the table instance
  } = useTableStateManagement({
    initialFilters: initialStateFromUrl.columnFilters,
    initialSorting: initialStateFromUrl.sorting,
    initialActiveView: initialStateFromUrl.activeView,
    initialVisibleColumnsProp,
    baseColumns, // Pass the original columns
    defaultPageSize: pageSize,
  });

  useEffect(() => {
    if (activeView?.value) {
      setViewValueInStore(activeView.value);
    }
  }, [activeView, setViewValueInStore]);

  // --- Sync Table State back to URL ---
  useEffect(() => {
    if (isListView) {
      if (
        JSON.stringify(columnFilters) !==
          JSON.stringify(initialStateFromUrl.columnFilters) ||
        JSON.stringify(sorting) !==
          JSON.stringify(initialStateFromUrl.sorting) ||
        activeView?.value !== initialStateFromUrl.activeView?.value
      ) {
        updateUrlParams(columnFilters, sorting, activeView?.value);
      }
    }
  }, [
    columnFilters,
    sorting,
    activeView,
    updateUrlParams,
    isListView,
    initialStateFromUrl,
  ]);

  // --- Data Fetching ---
  const currentResourcePath = activeView?.resourcePath ?? baseResourcePath;
  const filterString = useMemo(
    () => getFilterString(columnFilters, columnFilterFns, enrichedColumns),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [columnFilters, initialStateFromUrl],
  );
  const queryParams = useMemo(
    () =>
      getQueryParams(
        pagination.pageIndex,
        pagination.pageSize,
        sorting,
        filterString,
        debouncedSearchTerm || undefined, // Pass debounced search term
      ),

    [
      pagination.pageIndex,
      pagination.pageSize,
      sorting,
      filterString,
      debouncedSearchTerm,
    ],
  );

  const {
    data,
    isLoading: isQueryLoading,
    isFetching: isQueryFetching,
  } = useEntityListQuery<TResponse>({
    resourcePath: currentResourcePath,
    queryKey: queryKey,
    params: queryParams,
    enabled:
      (filterString !== "" && columnFilters.length > 0) ||
      (filterString === "" && columnFilters.length === 0),
  });

  useEffect(() => {
    if (!isQueryFetching && isLoading) {
      setIsLoading(false);
    }
  }, [isQueryFetching, isLoading, setIsLoading]);

  const isOverallLoading = isQueryLoading || isLoading;

  const rowData = useMemo(() => data?.data ?? [], [data]);
  const totalRowCount = useMemo(() => data?.totalCount ?? 0, [data]);
  const totalPages = useMemo(() => data?.totalPages, [data]); // For enabling/disabling bottom toolbar

  // --- Table Instance ---
  const table = useMantineReactTable({
    columns: enrichedColumns, // Use enriched columns
    data: rawData ?? rowData,

    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    mantineTableContainerProps: {
      mah: "80vh",
    },
    mantineSkeletonProps: {
      animate: true,
      height: 16,
      radius: "xl",
      width: "100%",
    },
    // State controlled by hooks
    state: {
      pagination,
      sorting,
      columnFilters,
      columnFilterFns,
      columnVisibility,
      isLoading: isOverallLoading,
    },
    // State Updaters from hook
    onPaginationChange: setPagination,
    onSortingChange: onSortingChangeCallback,
    onColumnFiltersChange: onColumnFiltersChangeCallback,
    onColumnFilterFnsChange: setColumnFilterFns,
    onColumnVisibilityChange: setColumnVisibility,
    // Core Table Options
    rowCount: totalRowCount,
    enableColumnFilterModes: true,
    enableColumnResizing: true,
    enableDensityToggle: false,
    enableGlobalFilter: false, // Search handled manually via debouncedSearchTerm
    enableFacetedValues: true,
    columnFilterDisplayMode: "popover",
    enableColumnPinning: false,
    enableTopToolbar: toolbarEnabled,
    enableBottomToolbar:
      (totalPages !== undefined && totalPages > 1) ||
      totalRowCount > pagination.pageSize,
    enableColumnActions: false,
    enableRowSelection: selectionEnabled,
    enableGrouping: enableGrouping,
    layoutMode: "grid", // Or "semantic"
    initialState: {
      // Keep minimal initial state if needed, most is controlled
      grouping: enableGrouping ? grouping : [],
      density: "xs",
      columnPinning: {
        left: ["mrt-row-expand", "mrt-row-select"],
        right: ["mrt-row-actions"],
      },
    },
    defaultColumn: {
      minSize: 40,
      maxSize: 1000,
      size: 80,
    },
    displayColumnDefOptions: {
      "mrt-row-select": { size: 40, grow: false },
    },
    paginationDisplayMode: "pages",
    enableToolbarInternalActions: false, // Disable MRT internal buttons if using custom toolbar fully
    enableStickyHeader: true,
    // --- Render Custom Toolbar ---
    renderTopToolbar: ({ table: mrtInstance }) => (
      <TableToolbar
        viewOptions={viewOptions}
        activeView={activeView}
        onViewChange={(value: string | null) => {
          const selectedView = viewOptions?.find((v) => v.value === value);
          if (selectedView) {
            handleViewChange(selectedView); // Trigger state updates via the hook
          }
        }}
        title={title}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        searchDisabled={searchDisabled}
        table={mrtInstance}
        customToolbarContent={customToolbarContent} // Pass custom elements
      />
    ),
    // --- Localization & Styling ---
    localization: {
      noRecordsToDisplay: t("table.noRecordsToDisplay"),
      noResultsFound: t("table.noResultsFound"),
      sortByColumnAsc: t("table.sortByColumnAsc"),
      sortByColumnDesc: t("table.sortByColumnDesc"),
      sortedByColumnAsc: t("table.sortedByColumnAsc"),
      sortedByColumnDesc: t("table.sortedByColumnDesc"),
      filterByColumn: t("table.filterByColumn"),
      filteringByColumn: t("table.filteringByColumn"),
      showHideColumns: t("table.showHideColumns"),
      showAllColumns: t("table.showAllColumns"),
      hideColumn: t("table.hideColumn"),
      toggleSelectAll: t("table.toggleSelectAll"),
      toggleFullScreen: t("table.toggleFullScreen"),
      rowsPerPage: t("table.rowsPerPage"),
    },
    mantineFilterSelectProps: {
      renderOption: (item: ComboboxLikeRenderOptionInput<ComboboxItem>) =>
        filterRenderOption(item, t, entityPath),
    },
    mantineFilterMultiSelectProps: {
      renderOption: (item: ComboboxLikeRenderOptionInput<ComboboxItem>) =>
        filterRenderOption(item, t, entityPath),
    },
    mantinePaginationProps: {
      radius: "md",
      size: "xs",
      rowsPerPageOptions: [
        pageSize.toString(),
        (DEFAULT_PAGE_SIZE * 2).toString(),
        (DEFAULT_PAGE_SIZE * 4).toString(),
      ].filter((v, i, a) => a.indexOf(v) === i), // Ensure unique options including prop pageSize
    },
    // --- Row Interaction ---
    mantineTableBodyRowProps: ({ row }) => ({
      h: 34,
      fz: 12,
      onDoubleClick: () => {
        // Only used for contract at the moment
        if (disableNavigation && customNavigation && customNavigate) {
          if (row && row.original?.id) {
            const contractId = row.original.id as string;
            customNavigate(contractId);
          }
        }

        if (!disableNavigation && row?.original?.id) {
          const targetPath = `/app/${entityPath}/${row.original.id}`;
          const finalPath = redirectTo
            ? `${targetPath}?redirectTo=${redirectTo}`
            : targetPath;
          navigate(finalPath);
        }
      },
    }),
  });

  // --- Update External Table Ref ---
  const internalTableRef = useRef(table); // Keep internal ref for current table instance
  useEffect(() => {
    internalTableRef.current = table; // Update internal ref when table instance changes
    if (tableRef) {
      // Attach activeView if needed, ensure tableRef.current is assignable
      tableRef.current = {
        ...table,
        activeView: activeView,
      } as CustomTableInstance;
    }
  }, [table, tableRef, activeView]);

  return (
    <Box style={{ pointerEvents: "auto" }} mb={8}>
      <LookupProvider>
        <MantineReactTable table={table} />
      </LookupProvider>
    </Box>
  );
}
