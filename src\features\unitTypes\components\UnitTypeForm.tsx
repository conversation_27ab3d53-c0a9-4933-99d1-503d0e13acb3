import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, NumberInput, Paper, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { config } from "@/config";
import "@mantine/tiptap/styles.css";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";

const formSchema = z.object({
  id: z.string(),
  code: z.string().nullable(),
  name: z.string().nullable(),
  contractReport: z.string().nullable(),
  contractRemark: z.string().nullable(),
  unitLength: z.coerce.number().nullable(),
  unitWidth: z.coerce.number().nullable(),
  unitHeight: z.coerce.number().nullable(),
  unitVolume: z.coerce.number().nullable(),
  moveBusFreeHour: z.coerce.number().nullable(),
  moveBusFreeKm: z.coerce.number().nullable(),
  proposedInsuranceValue: z.coerce.number().nullable(),
  minimumInsuranceValue: z.coerce.number().nullable(),
});

type FormSchema = z.infer<typeof formSchema>;

interface UnitFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  contextRecordId?: string;
  isCreate: boolean;
}

export function UnitTypeForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: UnitFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      code: initialValues?.code ?? "",
      name: initialValues?.name ?? "",
      contractReport: initialValues?.contractReport ?? "",
      contractRemark: initialValues?.contractRemark ?? "",
      unitLength: initialValues?.unitLength ?? null,
      unitWidth: initialValues?.unitWidth ?? null,
      unitHeight: initialValues?.unitHeight ?? null,
      unitVolume: initialValues?.unitVolume ?? null,
      moveBusFreeHour: initialValues?.moveBusFreeHour ?? null,
      moveBusFreeKm: initialValues?.moveBusFreeKm ?? null,
      proposedInsuranceValue: initialValues?.proposedInsuranceValue ?? null,
      minimumInsuranceValue: initialValues?.minimumInsuranceValue ?? null,
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="lg">
              <FieldValidation isDirty={form.isDirty("code")}>
                <TextInput
                  required
                  label={t("unitTypes.code")}
                  {...form.getInputProps("code")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("name")}>
                <TextInput
                  required
                  label={t("unitTypes.name")}
                  {...form.getInputProps("name")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("contractReport")}>
                <TextInput
                  required
                  label={t("unitTypes.contractReport")}
                  {...form.getInputProps("contractReport")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("contractRemark")}>
                <TextInput
                  required
                  label={t("unitTypes.contractRemark")}
                  {...form.getInputProps("contractRemark")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("unitLength")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.unitLength")}
                  leftSection={config.METERS.symbol}
                  {...form.getInputProps("unitLength")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("unitWidth")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.unitWidth")}
                  leftSection={config.METERS.symbol}
                  {...form.getInputProps("unitWidth")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("unitHeight")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.unitHeight")}
                  leftSection={config.METERS.symbol}
                  {...form.getInputProps("unitHeight")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("unitVolume")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.unitVolume")}
                  leftSection={config.CUBICMETERS.symbol}
                  {...form.getInputProps("unitVolume")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("moveBusFreeHour")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.moveBusFreeHour")}
                  {...form.getInputProps("moveBusFreeHour")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("moveBusFreeKm")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.moveBusFreeKm")}
                  {...form.getInputProps("moveBusFreeKm")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("proposedInsuranceValue")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.proposedInsuranceValue")}
                  {...form.getInputProps("proposedInsuranceValue")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("minimumInsuranceValue")}>
                <NumberInput
                  mt="sm"
                  label={t("unitTypes.minimumInsuranceValue")}
                  {...form.getInputProps("minimumInsuranceValue")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
