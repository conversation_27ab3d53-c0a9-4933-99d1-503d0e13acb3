import { PageLoader } from "@/components/PageLoader";
import { EntityLayout } from "@/features/entity";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { CustomerForm } from "../components/CustomerForm";
import { type SetNonNullable } from "type-fest";
import { useState } from "react";
import { useQueryClient } from "react-query";
import { notifications } from "@mantine/notifications";

export function CustomerShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Customer"],
    Schemas["CustomerCreateDto"]
  >({
    resourcePath: "/api/Customers/{id}",
    resourceId: id!,
    queryKey: "customer",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Customer"]>({
    resourcePath: "/api/Customers/{id}",
    resourceId: id!,
    queryKey: "customer",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("customer_" + id);
  };
  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <CustomerForm
      title={t("customers.showTitle", { id })}
      isCreate={false}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate("/app/customers");
          } else {
            return;
          }
        }
        update(filteredValues, {
          onSuccess: () => {
            if (close) {
              navigate("/app/customers");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      initialValues={
        filterFalsyValues(data) as Required<SetNonNullable<Schemas["Customer"]>>
      }
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
        </Group>
      }
    />
  );
}
