import { useState } from "react";
import {
  Button,
  Combobox,
  ScrollArea,
  useCombobox,
  Group,
  Box,
} from "@mantine/core";
import { IconCalendarTime } from "@tabler/icons-react";
import DashboardContent, {
  type ReportName,
  type DashboardContentProps,
} from "../components/index";
import { useTranslation } from "react-i18next";
import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";
import { BusinessUnitSelect } from "@/components/Layout/Header/BusinessUnitSelect";

type UserRole =
  | "Admin"
  | "Management"
  | "Marketing Manager"
  | "Sales"
  | "Sales Supervisor"
  | "Store Manager";

interface ReportsProps {
  display: string;
  value: string;
  periodEnabled: boolean;
  businessUnitEnabled: boolean;
  roles: UserRole[];
  enabled?: boolean;
}

interface TimePeriodProps {
  display: string;
  value: string;
}
const salesServicesReports: ReportsProps[] = [
  {
    display: "ALLSAFE Sales & Service (realtime)",
    value: "SalesServicesRealtime",
    periodEnabled: false,
    businessUnitEnabled: false,
    roles: [
      "Sales",
      "Sales Supervisor",
      "Store Manager",
      "Marketing Manager",
      "Management",
      "Admin",
    ],
    enabled: true,
  },
  {
    display: "ALLSAFE Sales & Service",
    value: "SalesServices",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: [
      "Sales",
      "Sales Supervisor",
      "Marketing Manager",
      "Management",
      "Admin",
    ],
    enabled: true,
  },
  {
    display: "S&S Afspraken en No shows Overzicht",
    value: "AfsprakenNoShows",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: [
      "Sales",
      "Sales Supervisor",
      "Marketing Manager",
      "Management",
      "Admin",
    ],
  },
  {
    display: "Sales & Service Overall",
    value: "Telesales",
    periodEnabled: false,
    businessUnitEnabled: false,
    roles: [
      "Sales",
      "Sales Supervisor",
      "Marketing Manager",
      "Management",
      "Admin",
    ],
    enabled: true,
  },
];

const operationsReports: ReportsProps[] = [
  {
    display: "Bezoekafspraken",
    value: "Bezoekafspraken",
    periodEnabled: true,
    businessUnitEnabled: true,
    roles: [
      "Store Manager",
      "Marketing Manager",
      "Management",
      "Sales Supervisor",
      "Admin",
    ],
    enabled: true,
  },
  /*
  {
    display: "Bezoekafspraken OPS",
    value: "BezoekafsprakenOPS",
    periodEnabled: true,
    businessUnitEnabled: true,
    roles: ["Sales Supervisor", "Marketing Manager", "Management", "Admin"],
  }, 
  */
  {
    display: "No Show",
    value: "NoShow",
    periodEnabled: true,
    businessUnitEnabled: true,
    roles: [
      "Store Manager",
      "Marketing Manager",
      "Management",
      "Sales Supervisor",
      "Admin",
    ],
    enabled: true,
  },
  /*
  {
    display: "No Show OPS",
    value: "NoShowOPS",
    periodEnabled: true,
    businessUnitEnabled: true,
    roles: ["Sales Supervisor", "Marketing Manager", "Management", "Admin"],
  }, 
  */
  {
    display: "Afgelegde Opportunities",
    value: "AfgelegdeOpportunities",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Sales Supervisor", "Store Manager", "Marketing Manager", "Admin"],
  },
  {
    display: "Conversie Dashboard",
    value: "ConversieDashboard",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Management", "Admin"],
  },
  {
    display: "Verlopen Opportunities",
    value: "VerlopenOpportunities",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Sales Supervisor", "Store Manager", "Marketing Manager", "Admin"],
  },
  {
    display: "Landelijk Overzicht",
    value: "LandelijkOverzicht",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Management", "Admin"],
  },
  {
    display: "Walk In",
    value: "WalkIn",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Admin"],
  },
  {
    display: "Competitie Leads Bellen",
    value: "CompetitieLeadsBellen",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Store Manager", "Marketing Manager", "Admin"],
  },
  {
    display: "Case Overzicht",
    value: "CaseOverzicht",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Sales Supervisor", "Store Manager", "Marketing Manager", "Admin"],
  },
  {
    display: "Regio Noord Oost",
    value: "RegioNoordOost",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Admin"],
  },
  {
    display: "Regio Midden",
    value: "RegioMidden",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Admin"],
  },
  {
    display: "Regio Zuid West",
    value: "RegioZuidWest",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Admin"],
  },
  {
    display: "Regio Zuid Oost",
    value: "RegioZuidOost",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Admin"],
  },
  {
    display: "Regio Noord West",
    value: "RegioNoordWest",
    periodEnabled: true,
    businessUnitEnabled: false,
    roles: ["Marketing Manager", "Admin"],
  },
];

const timePeriods: TimePeriodProps[] = [
  { display: "Today", value: "today" },
  { display: "Yesterday", value: "yesterday" },
  { display: "This week", value: "this_week" },
  { display: "Last week", value: "last_week" },
  { display: "This month", value: "this_month" },
  { display: "Last month", value: "last_month" },
  { display: "Quarterly", value: "quarterly" },
  { display: "This year", value: "this_year" },
  { display: "Last year", value: "last_year" },
];

export function DashboardsAll() {
  const { t } = useTranslation("common");
  const { roles } = useUserContext();
  const [selectedReport, setSelectedReport] =
    useState<DashboardContentProps["report"]>(null);

  const [selectedTimePeriod, setSelectedTimePeriod] =
    useState<DashboardContentProps["timePeriod"]>(null);

  const [selectedBusinessUnit, setSelectedBusinessUnit] =
    useState<DashboardContentProps["businessUnit"]>("");

  const reportCombobox = useCombobox({
    onDropdownClose: () => reportCombobox.resetSelectedOption(),
  });
  const timePeriodCombobox = useCombobox({
    onDropdownClose: () => timePeriodCombobox.resetSelectedOption(),
  });

  const reports = [...salesServicesReports, ...operationsReports];

  const enabledReports = reports.filter((report) => report.enabled);

  const allowedReports = enabledReports.filter((report) =>
    report.roles.some((role) => roles.includes(role)),
  );

  function getReportName(): string {
    const report = allowedReports.find(
      (report) => report.value === selectedReport,
    );
    return report ? report.display : t("dashboards.selectDashboard");
  }

  function getTimePeriodName(): string {
    const timePeriod = timePeriods.find(
      (period) => period.value === selectedTimePeriod,
    );
    return timePeriod ? timePeriod.display : "-";
  }

  const reportOptions = allowedReports.map((item: ReportsProps) => (
    <Combobox.Option value={item.value} key={item.value} ta={"center"}>
      {item.display}
    </Combobox.Option>
  ));

  const timePeriodOptions = timePeriods.map((item: TimePeriodProps) => (
    <Combobox.Option value={item.value} key={item.value} ta={"center"}>
      {item.display}
    </Combobox.Option>
  ));

  const selectedReportDetails = allowedReports.find(
    (report) => report.value === selectedReport,
  );

  const periodVisible = selectedReportDetails?.periodEnabled;
  const businessUnitVisible = selectedReportDetails?.businessUnitEnabled;

  return (
    <Box ml={10}>
      <Group align="flex-start" mt="xs">
        <Combobox
          store={reportCombobox}
          position="bottom-start"
          withArrow
          onOptionSubmit={(val) => {
            setSelectedReport(val as ReportName);
            setSelectedTimePeriod(null);
            reportCombobox.closeDropdown();
          }}
        >
          <Combobox.Target>
            <Button flex={2.75} onClick={() => reportCombobox.toggleDropdown()}>
              {getReportName()}
            </Button>
          </Combobox.Target>

          <Combobox.Dropdown>
            <Combobox.Options>
              <ScrollArea.Autosize type="always" scrollbars={"y"} mah={300}>
                {reportOptions}
              </ScrollArea.Autosize>
            </Combobox.Options>
          </Combobox.Dropdown>
        </Combobox>

        {selectedReport && periodVisible && (
          <Combobox
            store={timePeriodCombobox}
            position="bottom-start"
            withArrow
            onOptionSubmit={(val) => {
              setSelectedTimePeriod(val);
              timePeriodCombobox.closeDropdown();
            }}
          >
            <Combobox.Target>
              <Button
                flex={{ base: 1.1, md: 0.5, lg: 0.3 }}
                onClick={() => timePeriodCombobox.toggleDropdown()}
                leftSection={<IconCalendarTime size="1rem" />}
              >
                {getTimePeriodName()}
              </Button>
            </Combobox.Target>

            <Combobox.Dropdown>
              <Combobox.Options>
                <ScrollArea.Autosize type="always" mah={300}>
                  {timePeriodOptions}
                </ScrollArea.Autosize>
              </Combobox.Options>
            </Combobox.Dropdown>
          </Combobox>
        )}
        {selectedReport && businessUnitVisible && (
          <BusinessUnitSelect
            businessUnitId={selectedBusinessUnit ?? ""}
            setBusinessUnitId={(value: string | null) =>
              setSelectedBusinessUnit(value ?? "")
            }
            closeModal={true}
          />
        )}
      </Group>
      <DashboardContent
        report={selectedReport}
        timePeriod={periodVisible == false ? "custom" : selectedTimePeriod}
        periodVisible={periodVisible}
        businessUnit={selectedBusinessUnit}
      />
    </Box>
  );
}
