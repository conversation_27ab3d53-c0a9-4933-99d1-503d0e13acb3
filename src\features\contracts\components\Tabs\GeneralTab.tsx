import { Grid, Paper, Select, TextInput, Textarea } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { CancelReason, ContractStatus } from "@/types/enums";
import { DatePickerInput } from "@mantine/dates";
import { getEnumTransKey } from "@/utils/trans";
import { useContractFormContext } from "../../providers/form";
import { ContractLineList } from "@/features/contractLines/routes/ContractLineList";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

interface GeneralTabProps {
  contractId?: string;
}

export function GeneralTab({ contractId }: GeneralTabProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const form = useContractFormContext();

  return (
    <Grid mt="lg">
      <Grid.Col span={{ base: 12, md: 6 }}>
        <Paper shadow="xs" p="xs" pt="">
          <Select
            searchable
            label={t("contracts.contractStatus")}
            data={ContractStatus.map((value) => ({
              value,
              label: t(getEnumTransKey("contracts", value)),
            }))}
            {...form.getInputProps("contractStatus")}
            disabled
          />
          <TextInput
            label={t("contracts.contractNumber")}
            {...form.getInputProps("contractNumber")}
          />
          <ContactLookup
            label={t("contracts.contact")}
            initial={form.getValues().contact}
            initialId={form.getValues().contactId}
            identifier="contactIdContracts"
            {...form.getInputProps("contactId")}
          />
          <CustomerLookup
            label={t("contracts.customer")}
            initial={form.getValues().customer}
            initialId={form.getValues().customerId}
            identifier="customerIdContracts"
            {...form.getInputProps("customerId")}
          />
          <Textarea
            label={t("contracts.remarks")}
            {...form.getInputProps("remarks")}
            minRows={4}
            autosize
          />
        </Paper>
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 6 }}>
        <Paper shadow="xs" p="xs" pt="">
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            maxDate={form.getInputProps("to").value as Date | undefined}
            label={t("contracts.from")}
            {...form.getInputProps("from")}
          />
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            minDate={form.getInputProps("from").value as Date | undefined}
            label={t("contracts.to")}
            {...form.getInputProps("to")}
          />
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            label={t("contracts.signedOn")}
            {...form.getInputProps("signedOn")}
          />
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            label={t("contracts.firstCancelDate")}
            {...form.getInputProps("firstCancelDate")}
          />
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            label={t("contracts.cancelledOn")}
            {...form.getInputProps("cancelledOn")}
          />

          <Select
            clearable
            searchable
            label={t("contracts.cancelReason")}
            data={CancelReason.map((value) => ({
              value,
              label: t(getEnumTransKey("contracts", value)),
            }))}
            {...form.getInputProps("cancelReason")}
          />
          <DatePickerInput
            valueFormat={dateFormat.toUpperCase()}
            clearable
            label={t("contracts.moveOutDate")}
            {...form.getInputProps("moveOutDate")}
          />
        </Paper>
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 12 }}>
        {contractId ? (
          <ContractLineList
            parentEntityId={contractId}
            parentEntityIdParam="contractId"
            parentEntityName="contracts"
          />
        ) : null}
      </Grid.Col>
    </Grid>
  );
}
