import {
  type MRT_Column,
  type MRT_Header,
  type MRT_TableInstance,
  type MRT_RowData,
  type MRT_Cell,
} from "mantine-react-table";
import { type ReactNode } from "react";
import { type MantineStyleProps } from "@mantine/core";
import LookupFilter from "./Lookup/LookupFilter";
import { LookupCell } from "./Lookup/LookupCell";

interface RendererReturnProps {
  Filter: (props: {
    column: MRT_Column<MRT_RowData>;
    header: MRT_Header<MRT_RowData>;
    table: MRT_TableInstance<MRT_RowData>;
  }) => JSX.Element;
  Cell: (props: { cell: MRT_Cell<MRT_RowData> }) => ReactNode;
}

export type LookupComponentProps = MantineStyleProps & {
  initial?: MRT_RowData | null; // The currently selected filter entity
  initialId?: string | null; // The currently selected filter ID
  identifier: string; // Unique identifier (e.g., column ID + "Lookup")
  onChange: (value: string | null) => void; // Handler to update MRT filter state
};

export default function TableRenderer(
  Lookup: React.ComponentType<LookupComponentProps>,
  entityPath: string,
  nameFields: string[],
): RendererReturnProps {
  const filterComponent = (props: {
    column: MRT_Column<MRT_RowData>;
    header: MRT_Header<MRT_RowData>;
    table: MRT_TableInstance<MRT_RowData>;
  }): JSX.Element => (
    <LookupFilter
      column={props.column}
      table={props.table}
      header={props.header}
      LookupComponent={Lookup}
      entityPath={entityPath}
      nameFields={nameFields}
    />
  );

  const cellComponent = (props: { cell: MRT_Cell<MRT_RowData> }): ReactNode => (
    <LookupCell
      cell={props.cell}
      entityPath={entityPath}
      nameFields={nameFields}
    />
  );
  return {
    Filter: filterComponent,
    Cell: cellComponent,
  };
}
