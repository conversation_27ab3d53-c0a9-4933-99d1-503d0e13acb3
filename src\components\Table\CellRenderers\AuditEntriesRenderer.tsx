import { type Schemas } from "@/types";
import {
  type MRT_Column,
  type MRT_Row,
  type MRT_TableInstance,
  type MRT_Cell,
  type MRT_RowData,
} from "mantine-react-table";
import { type ReactNode, type RefObject } from "react";
import { IconArrowRight } from "@tabler/icons-react";

export function AuditEntriesRenderer(props: {
  cell: MRT_Cell<MRT_RowData, unknown>;
  column: MRT_Column<MRT_RowData, unknown>;
  renderedCellValue: ReactNode | number | string;
  renderedColumnIndex?: number;
  renderedRowIndex?: number;
  row: MRT_Row<MRT_RowData>;
  rowRef?: RefObject<HTMLTableRowElement>;
  table: MRT_TableInstance<MRT_RowData>;
}) {
  const cellData = props.cell.getValue<Schemas["AuditEntryRetrieveDto"][]>();
  if (!cellData || !Array.isArray(cellData)) {
    return <></>;
  }

  return (
    <table>
      <tbody>
        {cellData.map((entry) => (
          <tr key={entry.id}>
            <td>
              <b>{entry.fieldName}:</b>
            </td>
            <td>{entry.oldValue}</td>
            <td>
              <IconArrowRight />
            </td>
            <td>{entry.newValue}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
