import { useState, useMemo, useCallback } from "react";
import { useDebouncedValue } from "@mantine/hooks";
import {
  type MRT_ColumnFiltersState,
  type MRT_SortingState,
  type MRT_PaginationState,
  type MRT_ColumnFilterFnsState,
  type MRT_ColumnDef,
  type MRT_RowData,
} from "mantine-react-table";
import {
  getDefaultColumnFilterFns,
  enrichColumnsWithFilterOptions,
} from "@/components/Table/utils/tableUtils";
import { type ViewOption } from "../types";

const PAGE_SIZE = 50;
const STARTING_PAGE_NUMBER = 0;

interface UseTableStateManagementArgs {
  initialFilters: MRT_ColumnFiltersState;
  initialSorting: MRT_SortingState;
  initialActiveView?: ViewOption;
  initialVisibleColumnsProp?: string[];
  baseColumns: MRT_ColumnDef<MRT_RowData>[];
  defaultPageSize: number;
}

// Helper to calculate initial visibility based on view or props
const calculateInitialVisibility = (
  columns: MRT_ColumnDef<MRT_RowData>[],
  activeView?: ViewOption,
  visibleColumnsProp?: string[],
): Record<string, boolean> => {
  const initialShowColumns: Record<string, boolean> = {};
  const viewVisibleCols = activeView?.visibleColumns;

  if (viewVisibleCols?.length) {
    columns.forEach((col) => {
      initialShowColumns[(col.accessorKey as string) ?? col.id ?? ""] = false;
    });
    viewVisibleCols.forEach((key) => {
      initialShowColumns[key] = true;
    });
  } else if (visibleColumnsProp?.length) {
    columns.forEach((col) => {
      initialShowColumns[(col.accessorKey as string) ?? col.id ?? ""] = false;
    });
    visibleColumnsProp.forEach((key) => {
      initialShowColumns[key] = true;
    });
  } else {
    // Default: all columns visible if no specific config
    columns.forEach((col) => {
      initialShowColumns[(col.accessorKey as string) ?? col.id ?? ""] = true;
    });
  }
  return initialShowColumns;
};

export function useTableStateManagement({
  initialFilters,
  initialSorting,
  initialActiveView,
  initialVisibleColumnsProp,
  baseColumns, // Receive the base columns definition
  defaultPageSize = PAGE_SIZE,
}: UseTableStateManagementArgs) {
  const enrichedColumns = useMemo(
    () => enrichColumnsWithFilterOptions(baseColumns),
    [baseColumns],
  );
  const defaultFilterFns = useMemo(
    () => getDefaultColumnFilterFns(enrichedColumns),
    [enrichedColumns],
  );

  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm] = useDebouncedValue(searchTerm, 300);

  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: STARTING_PAGE_NUMBER,
    pageSize: defaultPageSize,
  });

  const [columnFilters, setColumnFilters] =
    useState<MRT_ColumnFiltersState>(initialFilters);
  const [debouncedColumnFilters] = useDebouncedValue(columnFilters, 300);

  const [sorting, setSorting] = useState<MRT_SortingState>(initialSorting);

  const [columnFilterFns, setColumnFilterFns] =
    useState<MRT_ColumnFilterFnsState>(defaultFilterFns);

  const [activeView, setActiveView] = useState<ViewOption | undefined>(
    initialActiveView,
  );

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [columnVisibility, setColumnVisibility] = useState<
    Record<string, boolean>
  >(() =>
    calculateInitialVisibility(
      enrichedColumns,
      initialActiveView,
      initialVisibleColumnsProp,
    ),
  );

  const handleViewChange = useCallback(
    (newView?: ViewOption) => {
      if (!newView) return;
      setIsLoading(true);
      setActiveView(newView);
      setColumnFilters(newView.filter ?? []);
      setSorting(newView.sorting ?? initialSorting); // Fallback to initial sorting if view has none
      setPagination((prev) => ({ ...prev, pageIndex: STARTING_PAGE_NUMBER })); // Reset to first page
      // Calculate and set new visibility
      const newVisibility = calculateInitialVisibility(
        enrichedColumns,
        newView,
        initialVisibleColumnsProp,
      );
      setColumnVisibility(newVisibility);
    },
    [enrichedColumns, initialSorting, initialVisibleColumnsProp],
  ); // Add dependencies

  const onColumnFiltersChangeCallback = useCallback(
    (
      updater:
        | MRT_ColumnFiltersState
        | ((old: MRT_ColumnFiltersState) => MRT_ColumnFiltersState),
    ) => {
      setColumnFilters(updater);
      setPagination((prev) => ({ ...prev, pageIndex: STARTING_PAGE_NUMBER }));
    },
    [setColumnFilters, setPagination],
  );

  const onSortingChangeCallback = useCallback(
    (
      updater: MRT_SortingState | ((old: MRT_SortingState) => MRT_SortingState),
    ) => {
      setIsLoading(true);
      setSorting(updater);
    },
    [setSorting],
  );

  return {
    // State values
    searchTerm,
    debouncedSearchTerm,
    pagination,
    columnFilters,
    debouncedColumnFilters,
    sorting,
    columnFilterFns,
    activeView,
    columnVisibility,
    isLoading,

    // Setters
    setSearchTerm,
    setPagination,
    setColumnFilterFns,
    setIsLoading,
    setActiveView, // Still needed if updated externally? Prefer handleViewChange
    setColumnVisibility,
    handleViewChange,
    onColumnFiltersChangeCallback,
    onSortingChangeCallback,

    // Derived/Processed
    enrichedColumns, // Pass enriched columns to the table
  };
}
