import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { DateRenderer } from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";

const PATH = "Countries";
export function CountryListInner({ resourcePath, createPath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["CountryRetrieveDto"],
        Schemas["CountryRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="country"
        entityPath="countries"
        title={t("countries.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "name",
            header: t("countries.name"),
            filterVariant: "text",
          },
          {
            accessorKey: "code",
            header: t("storageTypes.code"),
            filterVariant: "text",
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function CountryList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <CountryListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
