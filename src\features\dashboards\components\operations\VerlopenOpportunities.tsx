import type React from "react";
import { Grid } from "@mantine/core";
import HorizontalBarChart from "../common/HorizontalBarChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";

const metrics: MetricProps[] = [
  { metric: "AllAppointments" },
  { metric: "AllAppointments" },
  { metric: "AllAppointments" },
];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={12}>
      <HorizontalBarChart
        data={data[0]}
        orientation="horizontal"
        height="360"
        title="Total Opportunities"
      />
    </Grid.Col>
    <Grid.Col span={12}>
      <HorizontalBarChart
        data={data[1]}
        orientation="horizontal"
        height="360"
        title="Expired Opportunities"
      />
    </Grid.Col>
  </Grid>
);

const VerlopenOpportunities: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default VerlopenOpportunities;
