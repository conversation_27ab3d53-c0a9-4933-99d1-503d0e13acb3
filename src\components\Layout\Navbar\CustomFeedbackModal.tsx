import {
  <PERSON>dal,
  TextInput,
  Textarea,
  Button,
  Stack,
  Group,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import * as Sentry from "@sentry/react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

interface CustomFeedbackModalProps {
  opened: boolean;
  onClose: () => void;
}

export function CustomFeedbackModal({
  opened,
  onClose,
}: CustomFeedbackModalProps) {
  const { t } = useTranslation("common");

  const form = useForm({
    initialValues: {
      name: "",
      email: "",
      message: "",
    },
    validate: {
      message: (value) =>
        value.trim().length === 0
          ? t(
              "common:sentryFeedback.validation.messageRequired",
              "Message cannot be empty",
            ) // Provide fallback text
          : null,
      // Optional: email validation if needed
      // email: (value) => (/^\S+@\S+$/.test(value) || value.length === 0 ? null : 'Invalid email'),
    },
  });

  // Reset form when the modal is closed
  useEffect(() => {
    if (!opened) {
      form.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [opened]);

  const handleSubmit = (values: typeof form.values) => {
    try {
      console.log("Submitting feedback to Sentry:", values);
      Sentry.captureFeedback(
        {
          name: values.name,
          email: values.email,
          message: values.message,
        },
        {
          includeReplay: true,
        },
      );

      notifications.show({
        title: t(
          "common:sentryFeedback.successNotificationTitle",
          "common:sentryFeedback.successNotificationMessage",
        ),
        message: t(
          "common:sentryFeedback.successMessageText",
          "Thank you for your feedback!",
        ),
        color: "green",
      });

      form.reset();
      onClose();
    } catch (error) {
      console.error("Failed to send feedback to Sentry:", error);

      notifications.show({
        title: t("common:sentryFeedback.errorNotificationTitle", "Error"),
        message: t(
          "common:sentryFeedback.errorNotificationText",
          "Failed to send feedback. Please try again.",
        ),
        color: "red",
      });
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={t("common:sentryFeedback.formTitle", "Report a Bug or Feedback")}
      centered
      size="md"
    >
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label={t("common:sentryFeedback.nameLabel", "Your Name")}
            placeholder={t("common:sentryFeedback.namePlaceholder", "Jane Doe")}
            {...form.getInputProps("name")}
          />

          <TextInput
            type="email"
            label={t("common:sentryFeedback.emailLabel", "Your Email")}
            placeholder={t(
              "common:sentryFeedback.emailPlaceholder",
              "<EMAIL>",
            )}
            {...form.getInputProps("email")}
          />

          <Textarea
            label={t("common:sentryFeedback.messageLabel", "Description")}
            placeholder={t(
              "common:sentryFeedback.messagePlaceholder",
              "Please describe the issue or your feedback...",
            )}
            required
            minRows={4}
            autosize
            {...form.getInputProps("message")}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="default" onClick={onClose}>
              {t("common:sentryFeedback.cancelButtonLabel", "Cancel")}
            </Button>

            <Button type="submit">
              {t("common:sentryFeedback.submitButtonLabel", "Send Feedback")}
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
}
