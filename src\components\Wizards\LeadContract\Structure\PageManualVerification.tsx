import { useState } from "react";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Center,
  Flex,
  Text,
  Stack,
  Paper,
  TextInput,
  Select,
  Group,
  Alert,
  Divider,
} from "@mantine/core";
import {
  IconPencil,
  IconCheck,
  IconAlertCircle,
  IconUser,
  IconCalendar,
  IconId,
} from "@tabler/icons-react";
import { useForm } from "@mantine/form";

interface PageManualVerificationProps extends PageProps<PageName> {}

interface ManualVerificationForm {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  idType: string;
  idNumber: string;
  nationality: string;
  address: string;
  city: string;
  postalCode: string;
  phoneNumber: string;
  email: string;
}

export default function PageManualVerification({
  setPages,
  pages,
}: PageManualVerificationProps) {
  const { t } = useTranslation("features");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ManualVerificationForm>({
    initialValues: {
      firstName: "",
      lastName: "",
      dateOfBirth: "",
      idType: "",
      idNumber: "",
      nationality: "",
      address: "",
      city: "",
      postalCode: "",
      phoneNumber: "",
      email: "",
    },
    validate: {
      firstName: (value) =>
        value.length < 2 ? t("First name must be at least 2 characters") : null,
      lastName: (value) =>
        value.length < 2 ? t("Last name must be at least 2 characters") : null,
      dateOfBirth: (value) => (!value ? t("Date of birth is required") : null),
      idType: (value) => (!value ? t("ID type is required") : null),
      idNumber: (value) =>
        value.length < 5 ? t("ID number must be at least 5 characters") : null,
      email: (value) =>
        /^\S+@\S+$/.test(value) ? null : t("Invalid email format"),
      phoneNumber: (value) =>
        value.length < 10 ? t("Phone number must be at least 10 digits") : null,
      postalCode: (value) =>
        value.length < 4
          ? t("Postal code must be at least 4 characters")
          : null,
    },
  });

  const handleSubmit = async (values: ManualVerificationForm) => {
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("Manual verification data:", values);
    setIsSubmitting(false);
    setPages([...pages, "UNIT_SELECTION"]);
  };

  const idTypeOptions = [
    { value: "passport", label: t("Passport") },
    { value: "national_id", label: t("National ID") },
    { value: "drivers_license", label: t("Driver's License") },
    { value: "eu_id", label: t("EU ID Card") },
  ];

  return (
    <Box>
      <Center mt={20}>
        <Stack w="100%" maw={800} align="center" gap="lg">
          <Group gap="md">
            <IconPencil size={32} color="blue" />
            <Text fz={24} fw={700}>
              {t("Manual Verification")}
            </Text>
          </Group>

          <Text fz={14} c="dimmed" ta="center" maw={600}>
            {t(
              "Please manually enter the customer's identification details. All fields marked with * are required.",
            )}
          </Text>

          <Paper p="xl" withBorder w="100%">
            <form
              onSubmit={form.onSubmit(
                (values: ManualVerificationForm) => void handleSubmit(values),
              )}
            >
              <Stack gap="lg">
                {/* Personal Information Section */}
                <Box>
                  <Group gap="xs" mb="md">
                    <IconUser size={20} />
                    <Text fz={18} fw={600}>
                      {t("Personal Information")}
                    </Text>
                  </Group>
                  <Divider mb="md" />

                  <Group grow>
                    <TextInput
                      label={t("First Name")}
                      placeholder={t("Enter first name")}
                      required
                      {...form.getInputProps("firstName")}
                    />
                    <TextInput
                      label={t("Last Name")}
                      placeholder={t("Enter last name")}
                      required
                      {...form.getInputProps("lastName")}
                    />
                  </Group>

                  <Group grow mt="md">
                    <TextInput
                      label={t("Date of Birth")}
                      placeholder="YYYY-MM-DD"
                      type="date"
                      required
                      {...form.getInputProps("dateOfBirth")}
                    />
                    <TextInput
                      label={t("Nationality")}
                      placeholder={t("Enter nationality")}
                      {...form.getInputProps("nationality")}
                    />
                  </Group>
                </Box>

                {/* Identification Section */}
                <Box>
                  <Group gap="xs" mb="md">
                    <IconId size={20} />
                    <Text fz={18} fw={600}>
                      {t("Identification")}
                    </Text>
                  </Group>
                  <Divider mb="md" />

                  <Group grow>
                    <Select
                      label={t("ID Type")}
                      placeholder={t("Select ID type")}
                      data={idTypeOptions}
                      required
                      {...form.getInputProps("idType")}
                    />
                    <TextInput
                      label={t("ID Number")}
                      placeholder={t("Enter ID number")}
                      required
                      {...form.getInputProps("idNumber")}
                    />
                  </Group>
                </Box>

                {/* Contact Information Section */}
                <Box>
                  <Group gap="xs" mb="md">
                    <IconCalendar size={20} />
                    <Text fz={18} fw={600}>
                      {t("Contact Information")}
                    </Text>
                  </Group>
                  <Divider mb="md" />

                  <TextInput
                    label={t("Address")}
                    placeholder={t("Enter full address")}
                    {...form.getInputProps("address")}
                  />

                  <Group grow mt="md">
                    <TextInput
                      label={t("City")}
                      placeholder={t("Enter city")}
                      {...form.getInputProps("city")}
                    />
                    <TextInput
                      label={t("Postal Code")}
                      placeholder={t("Enter postal code")}
                      required
                      {...form.getInputProps("postalCode")}
                    />
                  </Group>

                  <Group grow mt="md">
                    <TextInput
                      label={t("Phone Number")}
                      placeholder={t("Enter phone number")}
                      required
                      {...form.getInputProps("phoneNumber")}
                    />
                    <TextInput
                      label={t("Email")}
                      placeholder={t("Enter email address")}
                      type="email"
                      required
                      {...form.getInputProps("email")}
                    />
                  </Group>
                </Box>

                <Alert
                  icon={<IconAlertCircle size="1rem" />}
                  title={t("Important")}
                  color="blue"
                >
                  {t(
                    "Please ensure all information is accurate and matches the customer's identification documents.",
                  )}
                </Alert>

                <Flex gap="md" justify="center" mt="xl">
                  <Button
                    variant="outline"
                    onClick={() => setPages([...pages.slice(0, -1)])}
                    disabled={isSubmitting}
                  >
                    {t("Back")}
                  </Button>
                  <Button
                    type="submit"
                    leftSection={<IconCheck size="1rem" />}
                    loading={isSubmitting}
                  >
                    {t("Verify & Continue")}
                  </Button>
                </Flex>
              </Stack>
            </form>
          </Paper>
        </Stack>
      </Center>
    </Box>
  );
}
