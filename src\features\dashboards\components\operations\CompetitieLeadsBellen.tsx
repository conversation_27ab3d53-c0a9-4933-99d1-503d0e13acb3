import type React from "react";
import { Grid } from "@mantine/core";
import HorizontalBarChart from "../common/HorizontalBarChart";
import { type DashboardContentProps } from "../index";
import { type MetricProps, type Statistic } from "../../utils/types";
import BaseReport from "../BaseReport";
import CardChart from "../common/CardChart";

const metrics: MetricProps[] = [{ metric: "AllAppointments" }];
const ChartGrid = ({
  data,
}: {
  data: Statistic[][];
  cardData: Statistic[][];
}) => (
  <Grid>
    <Grid.Col span={8}>
      <HorizontalBarChart
        marginTop={10}
        data={data[0]}
        orientation="horizontal"
        height="360"
        title="Total Leads Overview"
      />
    </Grid.Col>
    <Grid.Col span={4}>
      <CardChart marginTop={10} height="360" value="0" title={"title"} />
    </Grid.Col>
    <Grid.Col span={8}>
      <HorizontalBarChart
        marginTop={10}
        data={data[0]}
        orientation="horizontal"
        height="360"
        title="Total Leads Overview"
      />
    </Grid.Col>
    <Grid.Col span={4}>
      <CardChart marginTop={10} height="360" value="0" title={"title"} />
    </Grid.Col>
  </Grid>
);

const CompetitieLeadsBellen: React.FC<DashboardContentProps> = (props) => (
  <BaseReport
    {...props}
    metrics={metrics}
    renderContent={(data, cardData) => (
      <ChartGrid data={data} cardData={cardData} />
    )}
  />
);

export default CompetitieLeadsBellen;
