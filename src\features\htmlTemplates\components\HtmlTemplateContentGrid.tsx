import { Grid, Paper, Select, TextInput, Textarea } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { TemplateType } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
interface HtmlContentGridProps {
  label: string;
  content: string;
  onChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  nameProps: GetInputPropsReturnType; // For TextInput props
  replyToProps: GetInputPropsReturnType;
  templateTypeProps: GetInputPropsReturnType;
  subjectProps: ExtendedInputProps;
  previewContent: string;
}
interface ExtendedInputProps extends GetInputPropsReturnType {
  "data-path"?: string;
}
export function HtmlTemplateContentGrid({
  label,
  content,
  onChange,
  nameProps,
  replyToProps,
  templateTypeProps,
  subjectProps,
  previewContent,
}: HtmlContentGridProps) {
  const { t } = useTranslation("features");

  return (
    <Grid>
      <Grid.Col span={{ base: 12, md: 7 }}>
        <Paper shadow="xs" p="xs">
          <TextInput
            label={t("htmlTemplates.name")}
            {...nameProps}
            labelProps={{ style: { flex: 1 } }}
          />
          <Select
            searchable
            label={t("htmlTemplates.templateType")}
            data={TemplateType.map((value) => ({
              value,
              label: t(getEnumTransKey("htmlTemplates", value)),
            }))}
            clearable
            {...templateTypeProps}
            labelProps={{ style: { flex: 1 } }}
          />
          <TextInput
            label={t("htmlTemplates.replyTo")}
            {...replyToProps}
            labelProps={{ style: { flex: 1 } }}
          />
          <TextInput
            label={t(`${`htmlTemplates.` + subjectProps["data-path"]}`)}
            {...subjectProps}
            labelProps={{ style: { flex: 1 } }}
          />
          <Textarea
            minRows={26}
            maxRows={26}
            autosize
            label={label}
            value={content}
            onChange={onChange}
            labelProps={{ style: { flex: 1 } }}
          />
        </Paper>
      </Grid.Col>
      <Grid.Col span={{ base: 12, md: 5 }}>
        <div
          dangerouslySetInnerHTML={{
            __html: previewContent as TrustedHTML,
          }}
        />
      </Grid.Col>
    </Grid>
  );
}
