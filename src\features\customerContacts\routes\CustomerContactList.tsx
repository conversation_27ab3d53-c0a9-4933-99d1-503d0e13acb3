import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { CustomerContactColumns } from "../table/CustomerContactColumns";
import { CustomerContactCreate } from "./CustomerContactCreate";

const PATH = "CustomerContacts";
export function CustomerContactListInner({
  visibleColumns,
  resourcePath,
  createPath,
  parentEntityId,
  parentEntityName,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["CustomerContactRetrieveDto"],
        Schemas["CustomerContactRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="customerContact"
        entityPath="customerContacts"
        title={t("customerContacts.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton
              to={createPath}
              FormComponent={parentEntityId ? CustomerContactCreate : undefined}
              formProps={
                parentEntityId
                  ? {
                      parentEntityId: parentEntityId,
                      parentEntityName: parentEntityName,
                      redirectTo: "/app/customerContacts",
                      usingModal: true,
                    }
                  : undefined
              }
            />
          </Group>
        }
        visibleColumns={
          visibleColumns
            ? visibleColumns
            : ["customer", "contact", "contactRole"]
        }
        columns={CustomerContactColumns()}
        redirectTo={window.location.pathname}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function CustomerContactList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <CustomerContactListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
        parentEntityName={parentEntityName}
      />
    </ListCommandsProvider>
  );
}
