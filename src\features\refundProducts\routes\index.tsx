import { Route, Routes } from "react-router-dom";
import { RefundProductShow } from "@/features/refundProducts/routes/RefundProductShow";
import { RefundProductList } from "@/features/refundProducts/routes/RefundProductList";
import { RefundProductCreate } from "@/features/refundProducts/routes/RefundProductCreate";

export default function RefundProductsRoutes() {
  return (
    <Routes>
      <Route index element={<RefundProductList />} />
      <Route path=":id" element={<RefundProductShow />} />
      <Route path="create" element={<RefundProductCreate />} />
    </Routes>
  );
}
