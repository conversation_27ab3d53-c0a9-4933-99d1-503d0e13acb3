import { lazy, Suspense } from "react";
import { Center, Text, Loader } from "@mantine/core";

const reportComponents = {
  SalesServicesRealtime: lazy(() => import("./sales/SalesServicesRealtime")),
  SalesServices: lazy(() => import("./sales/SalesServices")),
  AfsprakenNoShows: lazy(() => import("./sales/AfsprakenNoShows")),
  Telesales: lazy(() => import("./sales/Telesales")),
  AfgelegdeOpportunities: lazy(
    () => import("./operations/AfgelegdeOpportunities"),
  ),
  Bezoekafspraken: lazy(() => import("./operations/Bezoekafspraken")),
  CaseOverzicht: lazy(() => import("./operations/CaseOverzicht")),
  CompetitieLeadsBellen: lazy(
    () => import("./operations/CompetitieLeadsBellen"),
  ),
  ConversieDashboard: lazy(() => import("./operations/ConversieDashboard")),
  LandelijkOverzicht: lazy(() => import("./operations/LandelijkOverzicht")),
  NoShow: lazy(() => import("./operations/NoShow")),
  Regios: lazy(() => import("./operations/Regios")),
  VerlopenOpportunities: lazy(
    () => import("./operations/VerlopenOpportunities"),
  ),
  WalkIn: lazy(() => import("./operations/WalkIn")),
};

export type ReportName = keyof typeof reportComponents;

export interface DashboardContentProps {
  report: ReportName | null;
  timePeriod: string | null;
  periodVisible?: boolean;
  businessUnitVisible?: boolean;
  businessUnit?: string;
}

const DashboardContent: React.FC<DashboardContentProps> = ({
  report,
  timePeriod,
  periodVisible,
  businessUnit,
}) => {
  if (!report) return null;
  if (!timePeriod && periodVisible) {
    return (
      <Center mt={20}>
        <Text size="xl" fw={900}>
          Please Choose A Filter
        </Text>
      </Center>
    );
  }

  const ReportComponent = reportComponents[report];

  return (
    <Suspense
      fallback={
        <Center mt={200}>
          <Loader size={200} color="primary" type="dots" />
        </Center>
      }
    >
      <ReportComponent
        report={report}
        timePeriod={timePeriod}
        businessUnit={businessUnit}
      />
    </Suspense>
  );
};

export default DashboardContent;
