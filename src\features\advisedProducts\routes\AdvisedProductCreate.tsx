import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { AdvisedProductForm } from "../components/AdvisedProductForm";
import { EntityLayout } from "@/features/entity";
import { Group, Loader } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEntityQuery } from "@/features/entity/queries";

interface AdvisedProductCreateProps {
  parentEntityId?: string;
  redirectTo?: string;
  usingModal?: boolean;
  closeModal?: () => void;
}

export function AdvisedProductCreate({
  parentEntityId: propParentEntityId,
  redirectTo: propRedirectTo,
  usingModal: propUsingModal,
  closeModal, // Add closeModal to props
}: AdvisedProductCreateProps) {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["AdvisedProduct"],
    Schemas["AdvisedProductCreateDto"]
  >({ resourcePath: "/api/AdvisedProducts", queryKey: "advisedProduct" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const leadId = propParentEntityId ?? searchParams.get("leadId");

  const redirectTo =
    propRedirectTo ?? searchParams.get("redirectTo") ?? "/app/advisedProducts";
  const usingModal = propUsingModal ?? false;

  const {
    data: lead = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/Leads/{id}",
    resourceId: leadId!,
    queryKey: ["lead", leadId],
  });

  if (isLoading || isFetching) {
    return <Loader />;
  }

  return (
    <AdvisedProductForm
      initialValues={{
        leadId: leadId ?? "",
        lead: lead,
      }}
      isCreate={true}
      title={t("advisedProducts.createTitle")}
      actionSection={
        <Group>
          {!usingModal && <EntityLayout.SaveButton setClose={setClose} />}
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (usingModal) {
              if (close) {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("advisedProducts.createSuccessMessage"),
                });
                closeModal?.();
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.createSuccessTitle"),
                  message: t("advisedProducts.createSuccessMessage"),
                });
              }
            } else {
              if (close) {
                navigate(redirectTo);
              } else {
                let navigateTo = `/app/advisedProducts/${data.data.id}`;
                navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
                navigate(navigateTo);
              }
            }
          },
        });
      }}
    />
  );
}
