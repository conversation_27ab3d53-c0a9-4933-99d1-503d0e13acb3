import { createContext, useContext, type ReactNode } from "react";
import { type MRT_RowData, type MRT_TableInstance } from "mantine-react-table";
import { useRef, useState } from "react";

interface ListCommandsContextType {
  loading: boolean;
  setLoading: (value: boolean) => void;
  tableRef: React.MutableRefObject<MRT_TableInstance<MRT_RowData> | null>;
}

const ListCommandsContext = createContext<ListCommandsContextType | undefined>(
  undefined,
);

export function ListCommandsProvider({ children }: { children: ReactNode }) {
  const [loading, setLoading] = useState(false);

  const tableRef = useRef<MRT_TableInstance<MRT_RowData> | null>(null);

  return (
    <ListCommandsContext.Provider
      value={{
        loading,
        setLoading,
        tableRef,
      }}
    >
      {children}
    </ListCommandsContext.Provider>
  );
}

// eslint-disable-next-line react-refresh/only-export-components
export function useListCommands() {
  const context = useContext(ListCommandsContext);
  if (!context) {
    throw new Error(
      "useListCommands must be used within a ListCommandsProvider",
    );
  }
  return context;
}
