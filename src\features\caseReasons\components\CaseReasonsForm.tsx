import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Switch, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { OriginCategoryLookup } from "@/components/Lookup/Features/OriginCategories/OriginCategoryLookup";
const formSchema = z.object({
  name: z.string().nullable(),
  showComplaintReason: z.boolean().default(true),
  originCategoryId: z.string().nullable(),
  originCategory: z.object({}).nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface CaseReasonsFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function CaseReasonsForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: CaseReasonsFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      name: initialValues?.name ?? "",
      showComplaintReason: initialValues?.showComplaintReason ?? true,
      originCategory: initialValues?.originCategory ?? null,
      originCategoryId: initialValues?.originCategoryId ?? "",
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="lg">
              <FieldValidation isDirty={form.isDirty("originCategory")}>
                <OriginCategoryLookup
                  mt="sm"
                  label={t("caseReasons.originCategory")}
                  initial={form.getValues().originCategory}
                  initialId={form.getValues().originCategoryId}
                  identifier="originCategoryIdCaseReason"
                  {...form.getInputProps("originCategoryId")}
                />
              </FieldValidation>

              <FieldValidation isDirty={form.isDirty("name")}>
                <TextInput
                  label={t("caseReasons.name")}
                  {...form.getInputProps("name")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("showComplaintReason")}>
                <Switch
                  mt="sm"
                  defaultChecked
                  label={t("caseReasons.showComplaintReason")}
                  {...form.getInputProps("showComplaintReason")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
