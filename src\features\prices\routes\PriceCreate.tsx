import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { PriceForm } from "../components/PriceForm";
import { EntityLayout } from "@/features/entity";
import { Group } from "@mantine/core";
import { notifications } from "@mantine/notifications";

export function PriceCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Price"],
    Schemas["PriceCreateDto"]
  >({ resourcePath: "/api/Prices", queryKey: "price" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") ?? "/app/prices";

  return (
    <PriceForm
      isCreate={true}
      title={t("prices.createTitle")}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              let navigateTo = `/app/prices/${data.data.id}`;
              navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
              navigate(navigateTo);
            }
          },
        });
      }}
    />
  );
}
