import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Date<PERSON><PERSON>er,
} from "@/components/Table/CellRenderers";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { ReservationStatus } from "@/types/enums";
import { lowerCaseNthLetter } from "@/utils/filters";
import { type ComboboxData } from "@mantine/core";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { UnitLookup } from "@/components/Lookup/Features/Units/UnitLookupField";
import { UnitTypeLookup } from "@/components/Lookup/Features/UnitTypes/UnitTypeLookup";
import { ReservationLookup } from "@/components/Lookup/Features/Reservations/ReservationLookup";
import { config } from "@/config";
import { AppUserLookup } from "@/components/Lookup/Features/AppUsers/AppUserLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

export function AdvisedUnitsColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "lead",
      header: t("advisedUnits.lead"),
      ...TableRenderer(LeadLookup, "leads", ["fullName"]),
    },
    {
      accessorKey: "unit",
      header: t("advisedUnits.unit"),
      ...TableRenderer(UnitLookup, "units", ["unitCode"]),
    },
    {
      accessorKey: "unit.unitType",
      header: t("advisedUnits.unitType"),
      ...TableRenderer(UnitTypeLookup, "unitTypes", ["name"]),
    },
    {
      accessorKey: "unit.volume",
      header: t("units.volume"),
      filterVariant: "range",
      Cell: ({ cell }) => {
        const value = cell.getValue<number>();
        return value !== null ? `${value} ${config.CUBICMETERS.symbol}` : "";
      },
    },
    {
      accessorKey: "reservationStatus",
      header: t("reservations.reservationStatus"),
      filterVariant: "multi-select",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        if (value) {
          return t("leads." + lowerCaseNthLetter(value));
        }
        return "";
      },
      mantineFilterSelectProps: {
        data: ReservationStatus as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "pricePerMonth",
      header: t("advisedUnits.pricePerMonth"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "pricePerWeek",
      header: t("advisedUnits.pricePerWeek"),
      filterVariant: "range",
      Cell: CurrencyRenderer,
    },
    {
      accessorKey: "reservation",
      header: t("advisedUnits.reservation"),
      ...TableRenderer(ReservationLookup, "reservations", ["start"]),
    },
    {
      accessorKey: "businessUnit",
      header: t("advisedUnits.businessUnit"),
      ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
    },
    {
      accessorKey: "createdOn",
      header: t("entity.createdOn"),
      sortingFn: "datetime",
      filterVariant: "date-range",
      Cell: DateRenderer,
    },
    {
      accessorKey: "createdByUser",
      header: t("entity.createdBy"),
      ...TableRenderer(AppUserLookup, "appUsers", ["name"]),
    },
  ];
  return columns;
}
