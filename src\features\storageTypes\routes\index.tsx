import { Route, Routes } from "react-router-dom";
import { StorageTypeList } from "./StorageTypeList";
import { StorageTypeShow } from "./StorageTypeShow";
import { StorageTypeCreate } from "./StorageTypeCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<StorageTypeList />} />
      <Route path=":id" element={<StorageTypeShow />} />
      <Route path="create" element={<StorageTypeCreate />} />
    </Routes>
  );
}
