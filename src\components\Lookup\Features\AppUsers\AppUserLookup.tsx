import { type Schemas } from "@/types";
import { Box, type MantineStyleProps } from "@mantine/core";
import { type GetInputPropsReturnType } from "node_modules/@mantine/form/lib/types";
import { type QueryParams } from "@/features/entity/api";
import React from "react";
import {
  OptionGroupRender,
  OptionRender,
} from "@/components/Lookup/SingleLookup/Lookup";
import { useLookup } from "../../Context/useLookup";
import { EntityLookup } from "../../Base/Structure/EntityLookup";
import { SingleLookup } from "../../SingleLookup/SingleLookup";
import { t } from "i18next";

const ENTITY = "appUsers";
type ENTITY_TYPE = Schemas["AppUserRetrieveDto"];

type AppUserLookupProps = GetInputPropsReturnType &
  MantineStyleProps & {
    label?: string;
    showFullName?: boolean;
    disabled?: boolean;
    required?: boolean;
    initial?: ENTITY_TYPE | null;
    initialId?: string | null;
    identifier: string;
  };

export function AppUserLookup({
  required = false,
  initial,
  initialId,
  identifier,
  disabled,
  showFullName = false,
  ...props
}: AppUserLookupProps) {
  const displayValue = showFullName ? initial?.name : initial?.email;
  const { searchTerm } = useLookup(
    identifier,
    displayValue,
    initialId,
    initial,
    true,
  );
  const queryParams: QueryParams = {
    pageSize: 50,
  };
  const { infiniteScrollRef, entityList, currentFocus, combobox, isFetching } =
    EntityLookup<ENTITY_TYPE, Schemas["AppUserRetrieveDtoPagedList"]>({
      queryParams: queryParams,
      resourcePath: "/api/AppUsers",
      queryKey: "lead",
      entity: identifier,
    });

  const header = (
    <React.Fragment>
      <Box>{t("common:lookup.email")}</Box>
      <Box>{t("common:lookup.fullName")}</Box>
    </React.Fragment>
  );
  const options: JSX.Element[] = entityList
    .map((entity, index) => {
      if (!entity) return null;

      const renderedOptions = [
        OptionRender(entity.name, searchTerm, `name_${entity.id}`),
        OptionRender(entity.email, searchTerm, `email_${entity.id}`),
      ];

      return OptionGroupRender(
        entity.id!,
        index,
        entity.name!,
        entityList.length,
        infiniteScrollRef,
        renderedOptions,
      );
    })
    .filter(Boolean) as JSX.Element[];

  return (
    <SingleLookup<ENTITY_TYPE>
      combobox={combobox}
      required={required}
      disabled={disabled}
      options={options}
      header={header}
      isFetching={isFetching}
      currentFocus={currentFocus}
      identifier={identifier}
      entity={ENTITY}
      entities={entityList}
      {...props}
    />
  );
}
