import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useState } from "react";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { TagForm } from "../components/TagForm";
import { EntityLayout } from "@/features/entity";
import { Group } from "@mantine/core";
import { notifications } from "@mantine/notifications";

export function TagCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Tag"],
    Schemas["TagCreateDto"]
  >({ resourcePath: "/api/Tags", queryKey: "tag" });
  const { t } = useTranslation("features");
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") ?? "/app/tags";

  return (
    <TagForm
      isCreate={true}
      title={t("tags.createTitle")}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              let navigateTo = `/app/tags/${data.data.id}`;
              navigateTo += redirectTo ? `?redirectTo=${redirectTo}` : "";
              navigate(navigateTo);
            }
          },
        });
      }}
    />
  );
}
