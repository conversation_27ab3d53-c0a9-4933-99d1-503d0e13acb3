import { Box, Flex, Group, Text } from "@mantine/core";

import classes from "../Home.module.css";

export default function PlaceholderTab() {
  return (
    <Box flex={1} className={classes.homeTab}>
      <Flex justify="center" align="center" direction="column" wrap="wrap">
        <Text
          ta="center"
          w={"100%"}
          fz={{ xs: 16, sm: 18, md: 14, lg: 12, xl: 16 }}
          fw={400}
        >
          This can be your metric
        </Text>

        <Group justify="center" mt={32} gap={4} w={"100%"}>
          <Text
            fz={{ xs: 16, sm: 12, md: 14, lg: 14, xl: 16 }}
            fw={300}
            className={classes.tabFooterText}
          >
            Contact @Wasser
          </Text>
        </Group>
      </Flex>
    </Box>
  );
}
