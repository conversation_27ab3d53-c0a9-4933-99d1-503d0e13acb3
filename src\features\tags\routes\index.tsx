import { Route, Routes } from "react-router-dom";
import { TagList } from "./TagList";
import { TagShow } from "./TagShow";
import { TagCreate } from "./TagCreate";

export default function ReservationsRoutes() {
  return (
    <Routes>
      <Route index element={<TagList />} />
      <Route path=":id" element={<TagShow />} />
      <Route path="create" element={<TagCreate />} />
    </Routes>
  );
}
