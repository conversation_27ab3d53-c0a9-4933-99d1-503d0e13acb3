import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { RefundProductsColumns } from "../table/RefundProductsColumns";
import { RefundProductCreate } from "./RefundProductCreate";

const PATH = "RefundProducts";

export function RefundProductListInner({
  visibleColumns,
  resourcePath,
  createPath,
  parentEntityId,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["RefundProductRetrieveDto"],
        Schemas["RefundProductRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="refundProduct"
        entityPath="refundProducts"
        title={t("refundProducts.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton
              to={createPath}
              FormComponent={parentEntityId ? RefundProductCreate : undefined}
              formProps={
                parentEntityId
                  ? {
                      parentEntityId: parentEntityId,
                      redirectTo: "/app/refundProducts",
                      usingModal: true,
                    }
                  : undefined
              }
            />
          </Group>
        }
        redirectTo={window.location.pathname}
        visibleColumns={visibleColumns}
        columns={RefundProductsColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function RefundProductList({
  visibleColumns,
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <RefundProductListInner
        visibleColumns={visibleColumns}
        resourcePath={resourcePath}
        createPath={createPath}
        parentEntityId={parentEntityId}
      />
    </ListCommandsProvider>
  );
}
