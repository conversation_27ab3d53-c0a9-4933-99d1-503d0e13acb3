import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Button,
  Center,
  Paper,
  Stack,
  Text,
  TextInput,
  Group,
  Select,
  Checkbox,
  Table,
  Flex,
  Loader,
  Box,
} from "@mantine/core";
import {
  IconPlus,
  IconSearch,
  IconChevronDown,
  IconHome,
} from "@tabler/icons-react";
import { useState } from "react";
import { useEntityListQuery } from "@/features/entity/queries";
import { DateInput } from "@mantine/dates";
import { useEntityPostMutation } from "@/features/entity/mutations";
import { useQueryClient } from "react-query";

interface PageUnitBrowseProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
  currentWizard?: Schemas["ContractRetrieveDto"];
}

interface UnitSearchCriteria {
  businessUnit: string;
  unitType: string;
  unitSize: string;
  availableTags: string;
  availabilityDate: Date | null;
}

interface FilterState {
  unitType: string;
  unitSize: string;
  price: string;
  availability: string;
  location: string;
}

export default function PageUnitBrowse({
  lead,
  setPages,
  pages,
  currentWizard,
}: PageUnitBrowseProps) {
  const { t } = useTranslation("features");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const queryClient = useQueryClient();
  const [currentView, setCurrentView] = useState<"search" | "browse">("search");
  const [filters, setFilters] = useState<FilterState>({
    unitType: "1",
    unitSize: "",
    price: "",
    availability: "",
    location: "",
  });

  const [searchCriteria, setSearchCriteria] = useState<UnitSearchCriteria>({
    businessUnit: lead?.businessUnitId?.toString() || "",
    unitType: "",
    unitSize: "",
    availableTags: "",
    availabilityDate: null,
  });

  const { mutate } = useEntityPostMutation({
    resourcePath: `/api/Contracts/${currentWizard?.id}/UnitContractLines`,
    queryKey: "unitContractLines",
  });

  // API queries for search criteria options
  const { data: businessUnitsResponse, isLoading: isLoadingBU } =
    useEntityListQuery<Schemas["BusinessUnitRetrieveDtoPagedList"]>({
      resourcePath: "/api/BusinessUnits",
      queryKey: `businessUnits`,
      params: {
        pageSize: 1000,
        orderBy: "name",
        desc: false,
      },
    });

  const { data: unitTypesResponse, isLoading: isLoadingUnitTypes } =
    useEntityListQuery<Schemas["UnitTypeRetrieveDtoPagedList"]>({
      resourcePath: "/api/UnitTypes",
      queryKey: `unitTypes`,
      params: {
        pageSize: 1000,
        orderBy: "name",
        desc: false,
      },
    });

  const { data: tagResponse, isLoading: isLoadingTags } = useEntityListQuery<
    Schemas["TagRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Tags",
    queryKey: `tags`,
    params: {
      pageSize: 1000,
      orderBy: "name",
      desc: false,
    },
  });

  // Units query for browse view
  const { data, isLoading: isLoadingUnits } = useEntityListQuery<
    Schemas["UnitRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Units",
    queryKey: `units`,
    params: {
      orderBy: "code",
      desc: false,
    },
  });

  const isLoading =
    isLoadingBU || isLoadingUnitTypes || isLoadingTags || isLoadingUnits;

  if (isLoading) {
    return (
      <Center>
        <Loader size="xl" />
      </Center>
    );
  }

  // Process API data for dropdowns
  const businessUnitsRaw = businessUnitsResponse?.data ?? [];
  const businessUnits = businessUnitsRaw.map((businessUnit) => ({
    label: `${businessUnit?.code ?? ""}`,
    value: businessUnit?.id ?? "",
  }));

  const unitTypesRaw = unitTypesResponse?.data ?? [];
  const unitTypes = unitTypesRaw.map((unitType) => ({
    label: `${unitType?.name ?? ""}`,
    value: unitType?.id ?? "",
  }));

  const tagsRaw = tagResponse?.data ?? [];
  const availableTags = tagsRaw.map((availableTag) => ({
    label: `${availableTag?.name ?? ""}`,
    value: availableTag?.id ?? "",
  }));

  const mockUnits = data?.data ?? [];
  // Mock data for filters
  const unitTypeOptions = [
    { value: "1", label: "Type of unit (1)" },
    { value: "regular", label: "Regular unit" },
    { value: "climate", label: "Climate controlled" },
  ];

  const unitSizeOptions = [
    { value: "", label: "Unit size" },
    { value: "5m3", label: "5m3" },
    { value: "10m3", label: "10m3" },
    { value: "13m3", label: "13m3" },
  ];

  const priceOptions = [
    { value: "", label: "Price" },
    { value: "0-100", label: "€0 - €100" },
    { value: "100-200", label: "€100 - €200" },
    { value: "200+", label: "€200+" },
  ];

  const availabilityOptions = [
    { value: "", label: "Availability" },
    { value: "available", label: "Available" },
    { value: "reserved", label: "Reserved" },
  ];

  const locationOptions = [
    { value: "", label: "Location" },
    { value: "ground", label: "Ground floor" },
    { value: "first", label: "First floor" },
  ];

  const handleUnitToggle = (unitId: string) => {
    setSelectedUnits((prev) =>
      prev.includes(unitId)
        ? prev.filter((id) => id !== unitId)
        : [...prev, unitId],
    );
  };

  const handleAddSelectedUnits = () => {
    const unitIds = selectedUnits;
    mutate(unitIds, {
      onSuccess: () => {
        void queryClient.invalidateQueries("contractLines");
        setPages(pages.slice(0, -1));
      },
    });
  };

  const handleCheckAvailability = () => {
    setCurrentView("browse");
  };

  const renderSearchView = () => (
    <Center>
      <Paper w={"30vw"}>
        <Stack gap="xs">
          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Business unit")}
            </Text>
            <Select
              placeholder={t("Select business unit")}
              data={businessUnits}
              value={searchCriteria.businessUnit}
              searchable
              clearable
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  businessUnit: value || "",
                }))
              }
              size="md"
            />
          </Box>

          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Unit type")}
            </Text>
            <Select
              placeholder={t("Select unit type")}
              data={unitTypes}
              value={searchCriteria.unitType}
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  unitType: value || "",
                }))
              }
              size="md"
            />
          </Box>

          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Available tags")}
            </Text>
            <Select
              placeholder={t("Select available tags")}
              data={availableTags}
              value={searchCriteria.availableTags}
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  availableTags: value || "",
                }))
              }
              size="md"
            />
          </Box>

          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Availability date")}
            </Text>
            <DateInput
              placeholder={t("Select availability date")}
              value={searchCriteria.availabilityDate}
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  availabilityDate: value,
                }))
              }
              size="md"
              valueFormat="DD-MM-YYYY"
            />
          </Box>

          <Button
            leftSection={<IconHome size={20} />}
            onClick={handleCheckAvailability}
            size="md"
            mt="md"
            variant="light"
            fullWidth
          >
            {t("Check unit availability")}
          </Button>
        </Stack>
      </Paper>
    </Center>
  );

  // Render browse units view
  const renderBrowseView = () => (
    <Center>
      <Paper w={"70vw"} p="xs" withBorder>
        <Stack gap={4}>
          <Group justify="space-between">
            <TextInput
              placeholder={t("Search")}
              leftSection={<IconSearch size={16} />}
              value={searchTerm}
              onChange={(event) => setSearchTerm(event.currentTarget.value)}
              size="md"
            />
            <Button
              variant="light"
              onClick={() => setCurrentView("search")}
              size="sm"
            >
              {t("Back to search")}
            </Button>
          </Group>

          <Flex gap="md">
            <Select
              data={unitTypeOptions}
              value={filters.unitType}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, unitType: value || "" }))
              }
              leftSection={<IconChevronDown size={16} />}
              size="sm"
            />
            <Select
              data={unitSizeOptions}
              value={filters.unitSize}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, unitSize: value || "" }))
              }
              placeholder="Unit size"
              size="sm"
            />
            <Select
              data={priceOptions}
              value={filters.price}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, price: value || "" }))
              }
              placeholder="Price"
              size="sm"
            />
            <Select
              data={availabilityOptions}
              value={filters.availability}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, availability: value || "" }))
              }
              placeholder="Availability"
              size="sm"
            />
            <Select
              data={locationOptions}
              value={filters.location}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, location: value || "" }))
              }
              placeholder="Location"
              size="sm"
            />
          </Flex>

          <Table highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th></Table.Th>
                <Table.Th>{t("Unit")}</Table.Th>
                <Table.Th>{t("Business Unit")}</Table.Th>
                <Table.Th>{t("Type")}</Table.Th>
                <Table.Th>{t("Size")}</Table.Th>
                <Table.Th>{t("Price")}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {mockUnits.map((unit) => (
                <Table.Tr
                  key={unit.id}
                  style={{
                    backgroundColor: selectedUnits.includes(unit.id!)
                      ? "rgba(0, 123, 255, 0.1)"
                      : "transparent",
                  }}
                >
                  <Table.Td>
                    <Checkbox
                      checked={selectedUnits.includes(unit.id!)}
                      onChange={() => handleUnitToggle(unit.id!)}
                    />
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{unit.unitCode}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.businessUnit?.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.unitType?.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.status}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.area}</Text>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          <Group justify="flex-end" mt="lg">
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={handleAddSelectedUnits}
              disabled={selectedUnits.length === 0}
            >
              {t("Add selected units")}
            </Button>
          </Group>
        </Stack>
      </Paper>
    </Center>
  );

  return currentView === "search" ? renderSearchView() : renderBrowseView();
}
