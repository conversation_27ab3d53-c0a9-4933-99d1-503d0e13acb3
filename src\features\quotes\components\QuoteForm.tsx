import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import {
  Box,
  Grid,
  NumberInput,
  Paper,
  Select,
  TextInput,
} from "@mantine/core";
import { type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { QuoteStatus } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { DatePickerInput } from "@mantine/dates";
import { AdvisedUnitList } from "@/features/advisedUnits/routes/AdvisedUnitList";
import { AdvisedProductList } from "@/features/advisedProducts/routes/AdvisedProductList";
import { TimeLine } from "@/components/TimeLine/TimeLine";
import { LeadLookup } from "@/components/Lookup/Features/Leads/LeadLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";

const formSchema = z.object({
  number: z.string(),
  totalMonthlyPrice: z.coerce.number().nullable(),
  totalOneTimeFee: z.coerce.number().nullable(),
  deposit: z.coerce.number().nullable(),
  status: z.enum(QuoteStatus as [string]).nullable(),
  leadId: z.string().nullable(),
  lead: z.object({}).nullable(),
  id: z.string(),
  expirationDate: z.date().nullable(),
});

export type FormSchema = z.infer<typeof formSchema>;

interface QuoteFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
  recordState?: string;
}

export function QuoteForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
  recordState,
}: QuoteFormProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      number: initialValues?.number ?? "",
      totalMonthlyPrice: initialValues?.totalMonthlyPrice ?? null,
      totalOneTimeFee: initialValues?.totalOneTimeFee ?? null,
      deposit: initialValues?.deposit ?? null,
      status: initialValues?.status ?? "Draft",
      leadId: initialValues?.leadId ?? "",
      lead: initialValues?.lead ?? null,
      id: initialValues?.id ?? "",
      expirationDate: initialValues?.expirationDate
        ? new Date(initialValues.expirationDate)
        : null,
    },
    validate: zodResolver(formSchema),
  });

  const isFormDisabled = true;
  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
        recordState={recordState}
      >
        <Grid>
          <Grid.Col span={{ base: 6, md: 6 }}>
            <Paper shadow="xs" p="xs" pt="">
              <TextInput
                disabled
                label={t("quotes.number")}
                {...form.getInputProps("number")}
              />
              <FieldValidation isDirty={form.isDirty("totalOneTimeFee")}>
                <NumberInput
                  disabled
                  label={t("quotes.totalOneTimeFee")}
                  leftSection={config.CURRENCY.symbol}
                  {...form.getInputProps("totalOneTimeFee")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("totalMonthlyPrice")}>
                <NumberInput
                  disabled
                  label={t("quotes.totalMonthlyPrice")}
                  leftSection={config.CURRENCY.symbol}
                  {...form.getInputProps("totalMonthlyPrice")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("status")}>
                <Select
                  searchable
                  disabled
                  label={t("quotes.status")}
                  data={QuoteStatus.map((value) => ({
                    value,
                    label: t(getEnumTransKey("quotes", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("status")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("expirationDate")}>
                <DatePickerInput
                  disabled
                  valueFormat={dateFormat.toUpperCase()}
                  clearable
                  label={t("quotes.expirationDate")}
                  {...form.getInputProps("expirationDate")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("leadId")}>
                <LeadLookup
                  disabled
                  label={t("quotes.lead")}
                  initial={form.getValues().lead}
                  initialId={form.getValues().leadId}
                  identifier="leadIdQoute"
                  {...form.getInputProps("leadId")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 6 }}>
            {!isCreate && (
              <Box mb={6}>
                <AdvisedUnitList
                  parentEntityId={initialValues?.id ?? ""}
                  parentEntityName="Quotes"
                  parentEntityIdParam="quoteId"
                  hideCreate={true}
                  visibleColumns={[
                    "unit",
                    "unitType",
                    "pricePerMonth",
                    "pricePerWeek",
                  ]}
                />
              </Box>
            )}
            {!isCreate && (
              <Box mb={6}>
                <AdvisedProductList
                  parentEntityId={initialValues?.id ?? ""}
                  parentEntityName="Quotes"
                  parentEntityIdParam="quoteId"
                  hideCreate={true}
                  visibleColumns={[
                    "product",
                    "price",
                    "quantity",
                    "totalPrice",
                  ]}
                />
              </Box>
            )}
          </Grid.Col>
          {!isCreate && (
            <Grid.Col span={{ base: 12, md: 6 }}>
              <TimeLine
                timeLineEntity="Quotes"
                timeLineEntityId={initialValues?.id ?? ""}
                queryKey="quoteTimeLine"
                disableToolbar
                emailWithBody
              />
            </Grid.Col>
          )}
        </Grid>
      </EntityLayout>
    </form>
  );
}
