import { useState } from "react";
import WizardHeader from "../Common/Header/WizardHeader";
import { Box } from "@mantine/core";
import PageSpaceTour from "./Structure/PageSpaceTour";
import PageStart from "./Structure/PageStart";
import PageShowUp from "./Structure/PageShowUp";
import { type PageName } from "./Common/Common";
import PageUpdate from "./Structure/PageUpdate";
import PageReschedule from "./Structure/PageReschedule";
import { type Schemas } from "@/types";
import PageCompleted from "./Structure/PageCompleted";
import PageCancel from "./Structure/PageCancel";

interface NoShowWizardProps {
  closeModal?: () => void;
  lead: Schemas["LeadRetrieveDto"];
  appointment: Schemas["AppointmentRetrieveDto"];
}

const DEFAULT_TOTAL_PAGES = 3;

export default function NoShowWizard({
  closeModal,
  lead,
  appointment,
}: NoShowWizardProps) {
  const [pages, setPages] = useState<PageName[]>(["START"]);
  const [totalPages, setTotalPages] = useState(DEFAULT_TOTAL_PAGES);
  const currentPageContent = () => {
    switch (pages.at(-1)) {
      case "START":
        return (
          <PageStart
            appointment={appointment}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "SHOW_UP":
        return (
          <PageShowUp
            setPages={setPages}
            pages={pages}
            appointment={appointment}
            lead={lead}
          />
        );
      case "SPACETOUR":
        return (
          <PageSpaceTour
            appointment={appointment}
            lead={lead}
            setPages={setPages}
            pages={pages}
          />
        );
      case "UPDATE":
        return (
          <PageUpdate
            setPages={setPages}
            setTotalPages={setTotalPages}
            pages={pages}
            lead={lead}
            appointment={appointment}
          />
        );
      case "RESCHEDULE":
        return (
          <PageReschedule
            setPages={setPages}
            pages={pages}
            lead={lead}
            appointment={appointment}
          />
        );
      case "CANCEL":
        return (
          <PageCancel
            appointment={appointment}
            lead={lead}
            setPages={setPages}
            pages={pages}
          />
        );
      case "COMPLETED":
        return <PageCompleted />;
      default:
        return <div>Page not found</div>;
    }
  };
  return (
    <Box h={"80vh"}>
      <WizardHeader
        defaultTotalPages={DEFAULT_TOTAL_PAGES}
        setPages={setPages}
        setTotalPages={setTotalPages}
        pages={pages}
        totalPages={totalPages}
        closeModal={closeModal}
      />
      {currentPageContent()}
    </Box>
  );
}
