import { useTranslation } from "react-i18next";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useNavigate } from "react-router-dom";
import { filterFalsyValues } from "@/utils/filters";
import { type Schemas } from "@/types";
import { UnitForm } from "../components/UnitForm";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";
import { useState } from "react";
import { notifications } from "@mantine/notifications";

export function UnitCreate() {
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { mutate } = useEntityCreateMutation<
    Schemas["Unit"],
    Schemas["UnitCreateDto"]
  >({ resourcePath: "/api/Units", queryKey: "unit" });
  const { t } = useTranslation("features");

  return (
    <UnitForm
      isCreate={true}
      title={t("units.createTitle")}
      onSubmit={(values) => {
        const filteredValues = filterFalsyValues(values);

        mutate(filteredValues, {
          onSuccess: (data) => {
            if (close) {
              navigate("/app/units");
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.createSuccessTitle"),
                message: t("notifications.createSuccessMessage"),
              });
              navigate("/app/units/" + data.data.id);
            }
          },
        });
      }}
      actionSection={
        <Group mt={1}>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
        </Group>
      }
    />
  );
}
