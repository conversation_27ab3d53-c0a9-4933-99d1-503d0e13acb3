import { EntityLayout } from "@/features/entity";
import { type PathKeys, type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { PriceColumns } from "../table/PriceColumns";

const PATH = "Prices";

export function PriceListInner({ resourcePath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["PriceRetrieveDto"],
        Schemas["PriceRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        disableNavigation
        resourcePath={resourcePath as PathKeys}
        queryKey="price"
        entityPath="prices"
        viewOptions={[
          {
            value: "All",
            label: t("prices.allPrices"),
            filter: [],
            default: true,
          },
          {
            value: "Active",
            label: t("prices.activePrices"),
            filter: [{ id: "recordState", value: "Active" }],
          },
          {
            value: "Inactive",
            label: t("prices.inactivePrices"),
            filter: [{ id: "recordState", value: "Inactive" }],
          },
        ]}
        title={t("prices.title")}
        redirectTo={window.location.pathname}
        visibleColumns={[
          "from",
          "to",
          "pricePerMonth",
          "minPrice",
          "maxPrice",
          "unit",
        ]}
        columns={PriceColumns()}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function PriceList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <PriceListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
