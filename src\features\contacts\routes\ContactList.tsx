import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ail<PERSON><PERSON><PERSON>,
  EntityLink<PERSON><PERSON>er,
  PhoneRenderer,
} from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";

const PATH = "Contacts";
export function ContactListInner({ resourcePath, createPath }: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();
  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["ContactRetrieveDto"],
        <PERSON>hem<PERSON>["ContactRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="contact"
        entityPath="contacts"
        title={t("contacts.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        columns={[
          {
            accessorKey: "number",
            header: t("contacts.number"),
            filterVariant: "text",
          },
          {
            accessorKey: "firstName",
            header: t("contacts.firstName"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "contacts"),
          },
          {
            accessorKey: "lastName",
            header: t("contacts.lastName"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "contacts"),
          },
          {
            accessorKey: "email",
            header: t("leads.emailHeader"),
            filterVariant: "text",
            Cell: EmailRenderer,
          },
          {
            accessorKey: "mobile",
            header: t("leads.mobileHeader"),
            filterVariant: "text",
            Cell: PhoneRenderer,
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        redirectTo={window.location.pathname}
        initialSorting={[
          {
            id: "createdOn",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function ContactList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <ContactListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
